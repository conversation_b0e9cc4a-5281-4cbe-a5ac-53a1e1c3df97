import { SetMetadata } from '@nestjs/common';

export interface FeatureGateOptions {
  feature: string;
  requireValue?: any;
  allowTrial?: boolean;
  gracefulDegradation?: boolean;
  customMessage?: string;
  upgradePrompt?: boolean;
}

export const FEATURE_GATE_KEY = 'feature_gate';

/**
 * Decorator to protect routes/methods with feature gating
 * 
 * @param feature - The feature key to check
 * @param options - Additional options for feature gating
 * 
 * @example
 * ```typescript
 * @FeatureGate('advanced_reporting')
 * @Get('advanced-reports')
 * async getAdvancedReports() {
 *   // This endpoint requires the 'advanced_reporting' feature
 * }
 * 
 * @FeatureGate('api_access', { requireValue: true, allowTrial: false })
 * @Post('api/data')
 * async apiEndpoint() {
 *   // This endpoint requires API access to be explicitly enabled
 * }
 * ```
 */
export const FeatureGate = (feature: string, options?: Partial<FeatureGateOptions>) => {
  const gateOptions: FeatureGateOptions = {
    feature,
    allowTrial: true,
    gracefulDegradation: false,
    upgradePrompt: true,
    ...options,
  };
  
  return SetMetadata(FEATURE_GATE_KEY, gateOptions);
};

/**
 * Decorator for features that should show upgrade prompts instead of blocking
 */
export const FeatureUpgrade = (feature: string, customMessage?: string) => {
  return FeatureGate(feature, {
    gracefulDegradation: true,
    upgradePrompt: true,
    customMessage,
  });
};

/**
 * Decorator for beta features that should be more permissive
 */
export const BetaFeature = (feature: string) => {
  return FeatureGate(feature, {
    allowTrial: true,
    gracefulDegradation: true,
    upgradePrompt: false,
  });
};

/**
 * Decorator for enterprise-only features
 */
export const EnterpriseFeature = (feature: string) => {
  return FeatureGate(feature, {
    allowTrial: false,
    gracefulDegradation: false,
    upgradePrompt: true,
    customMessage: 'This feature is only available in Enterprise plans',
  });
};
