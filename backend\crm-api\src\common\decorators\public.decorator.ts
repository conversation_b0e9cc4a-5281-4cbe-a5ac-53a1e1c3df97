import { SetMetadata } from '@nestjs/common';

export const IS_PUBLIC_KEY = 'isPublic';
export const REQUIRES_TENANT_KEY = 'requiresTenant';

/**
 * Decorator to mark an endpoint as public (no authentication required)
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);

/**
 * Decorator to mark an endpoint as not requiring tenant context
 */
export const NoTenant = () => SetMetadata(REQUIRES_TENANT_KEY, false);
