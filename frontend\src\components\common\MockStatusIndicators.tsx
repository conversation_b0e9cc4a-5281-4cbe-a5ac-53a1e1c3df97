'use client';

import React from 'react';
import { shouldUseMockAPI, shouldUseMockAuth } from '../../config/mock.config';
import { getMockApiConfig } from '../../lib/mockApi';

// Mock API status indicator
export const MockApiStatus: React.FC = () => {
  if (!shouldUseMockAPI()) {
    return null;
  }

  const config = getMockApiConfig();

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      left: 10,
      background: '#e3f2fd',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
      border: '1px solid #2196f3',
      fontFamily: 'monospace',
    }}>
      🎭 Mock API: Enabled (delay: {config.delay}ms)
    </div>
  );
};

// Mock Auth status indicator
export const MockAuthStatus: React.FC = () => {
  if (!shouldUseMockAuth()) {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: '#f0f0f0',
      padding: '8px 12px',
      borderRadius: '4px',
      fontSize: '12px',
      zIndex: 9999,
      border: '1px solid #ccc',
      fontFamily: 'monospace',
    }}>
      🎭 Mock Auth: Enabled
    </div>
  );
};

// Combined mock status component
export const MockStatusIndicators: React.FC = () => {
  return (
    <>
      <MockApiStatus />
      <MockAuthStatus />
    </>
  );
};
