import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';
import { FeatureFlag } from './feature-flag.entity';

export enum FeatureStatus {
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  INHERITED = 'inherited',
  TRIAL = 'trial',
  EXPIRED = 'expired',
}

@Entity('organization_features')
@Index(['organizationId', 'featureFlagId'], { unique: true })
export class OrganizationFeature {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @ManyToOne(() => FeatureFlag, featureFlag => featureFlag.organizationFeatures)
  @JoinColumn({ name: 'feature_flag_id' })
  featureFlag: FeatureFlag;

  @Column({ name: 'feature_flag_id' })
  featureFlagId: string;

  @Column({
    type: 'enum',
    enum: FeatureStatus,
    default: FeatureStatus.INHERITED,
  })
  status: FeatureStatus;

  @Column({ type: 'json', nullable: true })
  value: any;

  @Column({ type: 'json', nullable: true })
  configuration: {
    limits?: Record<string, number>;
    settings?: Record<string, any>;
    customization?: Record<string, any>;
  };

  @Column({ type: 'timestamp', nullable: true })
  enabledAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  disabledAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiresAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  trialStartedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  trialEndsAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  enabledBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  disabledBy: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  usageStats: {
    totalUsage?: number;
    lastUsedAt?: Date;
    usageCount?: number;
    averageUsagePerDay?: number;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get isEnabled(): boolean {
    if (this.status === FeatureStatus.DISABLED) return false;
    if (this.status === FeatureStatus.EXPIRED) return false;
    if (this.expiresAt && new Date() > this.expiresAt) return false;
    if (this.status === FeatureStatus.TRIAL && this.trialEndsAt && new Date() > this.trialEndsAt) return false;
    
    return this.status === FeatureStatus.ENABLED || 
           this.status === FeatureStatus.TRIAL ||
           this.status === FeatureStatus.INHERITED;
  }

  get isInTrial(): boolean {
    return this.status === FeatureStatus.TRIAL && 
           this.trialEndsAt && 
           new Date() < this.trialEndsAt;
  }

  get isExpired(): boolean {
    return this.status === FeatureStatus.EXPIRED ||
           (this.expiresAt && new Date() > this.expiresAt) ||
           (this.status === FeatureStatus.TRIAL && this.trialEndsAt && new Date() > this.trialEndsAt);
  }

  get daysUntilExpiry(): number {
    const expiryDate = this.expiresAt || this.trialEndsAt;
    if (!expiryDate) return -1;
    
    const now = new Date();
    const diff = expiryDate.getTime() - now.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  get trialDaysRemaining(): number {
    if (!this.isInTrial || !this.trialEndsAt) return 0;
    
    const now = new Date();
    const diff = this.trialEndsAt.getTime() - now.getTime();
    return Math.max(0, Math.ceil(diff / (1000 * 60 * 60 * 24)));
  }
}
