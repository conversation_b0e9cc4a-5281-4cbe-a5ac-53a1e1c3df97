import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TenantOnboardingController } from './tenant-onboarding.controller';
import { TenantOnboardingService } from './tenant-onboarding.service';
import { OnboardingStep } from './entities/onboarding-step.entity';
import { OnboardingTemplate } from './entities/onboarding-template.entity';
import { TenantOnboardingProgress } from './entities/tenant-onboarding-progress.entity';
import { OrganizationModule } from '../organization/organization.module';
import { UserModule } from '../user/user.module';
import { EmailModule } from '../email/email.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      OnboardingStep,
      OnboardingTemplate,
      TenantOnboardingProgress,
    ]),
    OrganizationModule,
    UserModule,
    EmailModule,
    NotificationModule,
  ],
  controllers: [TenantOnboardingController],
  providers: [TenantOnboardingService],
  exports: [TenantOnboardingService],
})
export class TenantOnboardingModule {}
