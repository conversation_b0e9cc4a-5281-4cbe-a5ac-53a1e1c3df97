import { test, expect } from '@playwright/test'

test.describe('Enhanced Components', () => {
  test.beforeEach(async ({ page }) => {
    // We'll create a test page for our components
    await page.goto('/component-test')
  })

  test.describe('Enhanced Button Component', () => {
    test('should render different button variants', async ({ page }) => {
      // Test default button
      const defaultButton = page.getByTestId('button-default')
      await expect(defaultButton).toBeVisible()
      await expect(defaultButton).toHaveClass(/bg-primary/)

      // Test destructive button
      const destructiveButton = page.getByTestId('button-destructive')
      await expect(destructiveButton).toBeVisible()
      await expect(destructiveButton).toHaveClass(/bg-destructive/)

      // Test outline button
      const outlineButton = page.getByTestId('button-outline')
      await expect(outlineButton).toBeVisible()
      await expect(outlineButton).toHaveClass(/border/)
    })

    test('should handle loading state correctly', async ({ page }) => {
      const loadingButton = page.getByTestId('button-loading')
      
      // Should show loading spinner when loading
      await expect(loadingButton).toBeVisible()
      await expect(loadingButton).toBeDisabled()
      await expect(loadingButton.locator('svg')).toBeVisible() // Loading spinner
    })

    test('should support different sizes', async ({ page }) => {
      const smallButton = page.getByTestId('button-small')
      const largeButton = page.getByTestId('button-large')
      
      await expect(smallButton).toHaveClass(/h-8/)
      await expect(largeButton).toHaveClass(/h-10/)
    })

    test('should be accessible', async ({ page }) => {
      const button = page.getByTestId('button-default')
      
      // Should be focusable
      await button.focus()
      await expect(button).toBeFocused()
      
      // Should be clickable with keyboard
      await button.press('Enter')
      await expect(page.getByText('Button clicked')).toBeVisible()
    })

    test('should handle disabled state', async ({ page }) => {
      const disabledButton = page.getByTestId('button-disabled')
      
      await expect(disabledButton).toBeDisabled()
      await expect(disabledButton).toHaveClass(/opacity-50/)
      
      // Should not be clickable
      await disabledButton.click({ force: true })
      await expect(page.getByText('Button clicked')).not.toBeVisible()
    })
  })

  test.describe('Enhanced Form Component', () => {
    test('should render form fields correctly', async ({ page }) => {
      // Check form is present
      const form = page.getByTestId('enhanced-form')
      await expect(form).toBeVisible()

      // Check input field
      const nameInput = page.getByLabel('Name')
      await expect(nameInput).toBeVisible()
      await expect(nameInput).toHaveAttribute('required')

      // Check textarea
      const descriptionTextarea = page.getByLabel('Description')
      await expect(descriptionTextarea).toBeVisible()

      // Check select
      const categorySelect = page.getByLabel('Category')
      await expect(categorySelect).toBeVisible()

      // Check checkbox
      const agreeCheckbox = page.getByLabel('I agree to the terms')
      await expect(agreeCheckbox).toBeVisible()
    })

    test('should validate required fields', async ({ page }) => {
      const form = page.getByTestId('enhanced-form')
      const submitButton = page.getByRole('button', { name: /submit/i })

      // Try to submit empty form
      await submitButton.click()

      // Should show validation errors
      await expect(page.getByText('Name is required')).toBeVisible()
      await expect(page.getByText('Email is required')).toBeVisible()
    })

    test('should validate email format', async ({ page }) => {
      const emailInput = page.getByLabel('Email')
      const submitButton = page.getByRole('button', { name: /submit/i })

      // Enter invalid email
      await emailInput.fill('invalid-email')
      await submitButton.click()

      // Should show email validation error
      await expect(page.getByText('Invalid email format')).toBeVisible()
    })

    test('should submit valid form', async ({ page }) => {
      // Fill out form with valid data
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByLabel('Description').fill('Test description')
      
      // Select category
      await page.getByLabel('Category').click()
      await page.getByRole('option', { name: 'Business' }).click()
      
      // Check agreement
      await page.getByLabel('I agree to the terms').check()

      // Submit form
      await page.getByRole('button', { name: /submit/i }).click()

      // Should show success message
      await expect(page.getByText('Form submitted successfully')).toBeVisible()
    })

    test('should show loading state during submission', async ({ page }) => {
      // Fill out form
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      
      const submitButton = page.getByRole('button', { name: /submit/i })
      
      // Submit form
      await submitButton.click()
      
      // Should show loading state
      await expect(submitButton).toBeDisabled()
      await expect(submitButton).toContainText('Submitting')
    })

    test('should handle form reset', async ({ page }) => {
      // Fill out form
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      
      // Reset form
      await page.getByRole('button', { name: /reset/i }).click()
      
      // Fields should be empty
      await expect(page.getByLabel('Name')).toHaveValue('')
      await expect(page.getByLabel('Email')).toHaveValue('')
    })
  })

  test.describe('Form Accessibility', () => {
    test('should have proper ARIA labels', async ({ page }) => {
      const nameInput = page.getByLabel('Name')
      const emailInput = page.getByLabel('Email')
      
      await expect(nameInput).toHaveAttribute('aria-required', 'true')
      await expect(emailInput).toHaveAttribute('aria-required', 'true')
    })

    test('should associate errors with fields', async ({ page }) => {
      const submitButton = page.getByRole('button', { name: /submit/i })
      await submitButton.click()
      
      const nameInput = page.getByLabel('Name')
      const errorId = await nameInput.getAttribute('aria-describedby')
      
      if (errorId) {
        const errorElement = page.locator(`#${errorId}`)
        await expect(errorElement).toBeVisible()
        await expect(errorElement).toContainText('required')
      }
    })

    test('should support keyboard navigation', async ({ page }) => {
      // Tab through form fields
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Name')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Email')).toBeFocused()
      
      await page.keyboard.press('Tab')
      await expect(page.getByLabel('Description')).toBeFocused()
    })
  })

  test.describe('Responsive Behavior', () => {
    test('should work on mobile devices', async ({ page }) => {
      await page.setViewportSize({ width: 375, height: 667 })
      
      // Form should still be usable
      const form = page.getByTestId('enhanced-form')
      await expect(form).toBeVisible()
      
      // Buttons should be appropriately sized
      const submitButton = page.getByRole('button', { name: /submit/i })
      await expect(submitButton).toBeVisible()
      
      // Form fields should be touch-friendly
      const nameInput = page.getByLabel('Name')
      await nameInput.tap()
      await expect(nameInput).toBeFocused()
    })

    test('should handle different screen sizes', async ({ page }) => {
      // Test tablet size
      await page.setViewportSize({ width: 768, height: 1024 })
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
      
      // Test desktop size
      await page.setViewportSize({ width: 1920, height: 1080 })
      await expect(page.getByTestId('enhanced-form')).toBeVisible()
    })
  })

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/submit', route => route.abort())
      
      // Fill and submit form
      await page.getByLabel('Name').fill('John Doe')
      await page.getByLabel('Email').fill('<EMAIL>')
      await page.getByRole('button', { name: /submit/i }).click()
      
      // Should show error message
      await expect(page.getByText('Submission failed')).toBeVisible()
    })

    test('should recover from errors', async ({ page }) => {
      // First submission fails
      await page.route('**/api/submit', route => route.abort())
      await page.getByLabel('Name').fill('John Doe')
      await page.getByRole('button', { name: /submit/i }).click()
      await expect(page.getByText('Submission failed')).toBeVisible()
      
      // Second submission succeeds
      await page.unroute('**/api/submit')
      await page.getByRole('button', { name: /submit/i }).click()
      await expect(page.getByText('Form submitted successfully')).toBeVisible()
    })
  })
})
