'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './MockAuthProvider';

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: string;
  maxUsers: number;
  maxStorageGb: number;
  features: Record<string, boolean>;
  settings: Record<string, any>;
}

interface TenantContextType {
  organization: Organization | null;
  isLoading: boolean;
  refreshOrganization: () => Promise<void>;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

interface TenantProviderProps {
  children: React.ReactNode;
}

export const TenantProvider: React.FC<TenantProviderProps> = ({ children }) => {
  const auth = useAuth();
  const { user, isAuthenticated } = auth;
  const token = 'token' in auth ? auth.token : auth.getToken?.();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchOrganization = async () => {
    const orgId = user && 'organizationId' in user ? user.organizationId : (user as any)?.orgId;
    if (!isAuthenticated || !token || !orgId) {
      setOrganization(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch('/api/organizations/current', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Org-Id': orgId,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const orgData = await response.json();
        setOrganization(orgData);
      } else {
        console.error('Failed to fetch organization:', response.statusText);
        setOrganization(null);
      }
    } catch (error) {
      console.error('Error fetching organization:', error);
      setOrganization(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganization();
  }, [isAuthenticated, token, user && 'organizationId' in user ? user.organizationId : (user as any)?.orgId]);

  const refreshOrganization = async () => {
    await fetchOrganization();
  };

  const value: TenantContextType = {
    organization,
    isLoading,
    refreshOrganization,
  };

  return <TenantContext.Provider value={value}>{children}</TenantContext.Provider>;
};
