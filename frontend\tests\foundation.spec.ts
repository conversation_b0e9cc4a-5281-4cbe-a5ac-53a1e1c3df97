import { test, expect } from '@playwright/test'

test.describe('Foundation Components', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should display the landing page correctly', async ({ page }) => {
    // Check main heading (use exact match to avoid conflicts)
    await expect(page.getByRole('heading', { name: 'OneCRM', exact: true })).toBeVisible()

    // Check subtitle
    await expect(page.getByText('Enterprise Customer Relationship Management')).toBeVisible()

    // Check description
    await expect(page.getByText('Streamline your business relationships')).toBeVisible()
  })

  test('should display feature cards', async ({ page }) => {
    // Check all feature cards are present (use text content since they're in card titles)
    await expect(page.getByText('Contact Management')).toBeVisible()
    await expect(page.getByText('Company Tracking')).toBeVisible()
    await expect(page.getByText('Sales Pipeline')).toBeVisible()
    await expect(page.getByText('Analytics', { exact: true })).toBeVisible()
  })

  test('should have working navigation buttons', async ({ page }) => {
    // Check Sign In button
    const signInButton = page.getByRole('button', { name: /sign in/i })
    await expect(signInButton).toBeVisible()
    await expect(signInButton).toBeEnabled()
    
    // Check View Demo button
    const demoButton = page.getByRole('button', { name: /view demo/i })
    await expect(demoButton).toBeVisible()
    await expect(demoButton).toBeEnabled()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check that content is still visible and properly laid out
    await expect(page.getByRole('heading', { name: 'OneCRM', exact: true })).toBeVisible()
    await expect(page.getByText('Contact Management')).toBeVisible()
    
    // Check that buttons are still accessible
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible()
  })

  test('should have proper accessibility attributes', async ({ page }) => {
    // Check heading hierarchy
    const h1 = page.getByRole('heading', { level: 1 })
    await expect(h1).toBeVisible()
    
    // Check that buttons have proper labels
    const signInButton = page.getByRole('button', { name: /sign in/i })
    await expect(signInButton).toBeVisible()
    
    // Check that images have alt text (if any)
    const images = page.locator('img')
    const imageCount = await images.count()
    for (let i = 0; i < imageCount; i++) {
      await expect(images.nth(i)).toHaveAttribute('alt')
    }
  })

  test('should support keyboard navigation', async ({ page }) => {
    // Tab through interactive elements
    await page.keyboard.press('Tab')
    
    // Check that focus is visible on interactive elements
    const signInButton = page.getByRole('button', { name: /sign in/i })
    await signInButton.focus()
    await expect(signInButton).toBeFocused()
    
    // Test Enter key activation
    await page.keyboard.press('Enter')
    // Should navigate or trigger action
  })

  test('should handle theme switching', async ({ page }) => {
    // Note: This test assumes theme toggle is available
    // We'll implement this when we add the theme toggle to the landing page
    
    // Check default theme
    const body = page.locator('body')
    
    // For now, just check that the page loads without theme-related errors
    await expect(body).toBeVisible()
    
    // Check that CSS variables are properly set
    const rootElement = page.locator('html')
    await expect(rootElement).toBeVisible()
  })

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = []
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })
    
    page.on('pageerror', (error) => {
      errors.push(error.message)
    })
    
    await page.reload()
    
    // Wait for page to fully load
    await page.waitForLoadState('networkidle')
    
    // Check that no critical JavaScript errors occurred (allow some 404s for missing resources)
    const criticalErrors = errors.filter(error =>
      !error.includes('Failed to load resource') &&
      !error.includes('404')
    )
    expect(criticalErrors).toHaveLength(0)
  })

  test('should have proper meta tags', async ({ page }) => {
    // Check title
    await expect(page).toHaveTitle(/OneCRM/)
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveAttribute('content', /CRM/)
  })

  test('should handle loading states gracefully', async ({ page }) => {
    // Simulate slow network
    await page.route('**/*', (route) => {
      setTimeout(() => route.continue(), 100)
    })
    
    await page.goto('/')
    
    // Check that loading indicators work properly
    // This will be more relevant when we have actual loading states
    await expect(page.getByRole('heading', { name: 'OneCRM', exact: true })).toBeVisible()
  })
})
