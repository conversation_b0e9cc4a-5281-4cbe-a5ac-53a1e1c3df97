_format_version: "3.0"
_transform: true

services:
  - name: crm-api
    url: http://host.docker.internal:3001
    tags:
      - crm
      - api
    routes:
      - name: crm-api-route
        paths:
          - /api
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "http://localhost:3000"
            - "http://localhost:3001"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - X-Org-Id
          exposed_headers:
            - X-Auth-Token
            - X-Tenant-Id
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          policy: local
          hide_client_headers: false
      - name: request-transformer
        config:
          add:
            headers:
              - "X-Kong-Request-ID:$(kong.request.get_header('kong-request-id'))"
          remove:
            headers:
              - "X-Client-Org-Id"
      - name: response-transformer
        config:
          add:
            headers:
              - "X-Kong-Upstream-Latency:$(kong.response.get_header('x-kong-upstream-latency'))"

  - name: auth-service
    url: http://host.docker.internal:3002
    tags:
      - auth
      - service
    routes:
      - name: auth-service-route
        paths:
          - /auth
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https

  - name: tenant-service
    url: http://host.docker.internal:3003
    tags:
      - tenant
      - service
    routes:
      - name: tenant-service-route
        paths:
          - /tenants
        strip_path: false
        preserve_host: false
        protocols:
          - http
          - https

# Global plugins
plugins:
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true

# Consumers (will be populated by Keycloak integration)
consumers: []

# Upstreams for load balancing (if needed)
upstreams: []
