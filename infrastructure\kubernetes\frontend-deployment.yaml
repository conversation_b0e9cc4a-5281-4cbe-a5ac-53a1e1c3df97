apiVersion: apps/v1
kind: Deployment
metadata:
  name: onecrm-frontend
  namespace: onecrm
  labels:
    app: onecrm-frontend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: onecrm-frontend
  template:
    metadata:
      labels:
        app: onecrm-frontend
        version: v1
    spec:
      containers:
      - name: frontend
        image: onecrm/frontend:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: NEXT_PUBLIC_API_URL
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: api-url
        - name: NEXT_PUBLIC_KEYCLOAK_URL
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-url
        - name: NEXT_PUBLIC_KEYCLOAK_REALM
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-realm
        - name: NEXT_PUBLIC_KEYCLOAK_CLIENT_ID
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-client-id
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nextjs-cache
          mountPath: /.next
      volumes:
      - name: tmp
        emptyDir: {}
      - name: nextjs-cache
        emptyDir: {}
      securityContext:
        fsGroup: 1001
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - onecrm-frontend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: onecrm-frontend-service
  namespace: onecrm
  labels:
    app: onecrm-frontend
spec:
  selector:
    app: onecrm-frontend
  ports:
  - name: http
    port: 3000
    targetPort: 3000
    protocol: TCP
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: onecrm-frontend-hpa
  namespace: onecrm
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: onecrm-frontend
  minReplicas: 2
  maxReplicas: 8
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
