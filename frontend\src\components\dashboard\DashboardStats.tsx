'use client';

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Skeleton,
} from '@mui/material';
import {
  People as PeopleIcon,
  Business as BusinessIcon,
  TrendingUp as TrendingUpIcon,
  Assignment as AssignmentIcon,
  AttachMoney as MoneyIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface StatCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ReactNode;
  color: string;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  changeType = 'neutral',
  icon,
  color,
  isLoading = false,
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'success.main';
      case 'negative':
        return 'error.main';
      default:
        return 'text.secondary';
    }
  };

  const getChangeIcon = () => {
    switch (changeType) {
      case 'positive':
        return <TrendingUpIcon fontSize="small" />;
      case 'negative':
        return <TrendingDownIcon fontSize="small" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Skeleton variant="text" width={120} height={24} />
              <Skeleton variant="text" width={80} height={40} />
              <Skeleton variant="text" width={100} height={20} />
            </Box>
            <Skeleton variant="circular" width={56} height={56} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            {change && (
              <Box display="flex" alignItems="center" mt={1}>
                {getChangeIcon()}
                <Typography
                  variant="body2"
                  color={getChangeColor()}
                  sx={{ ml: 0.5 }}
                >
                  {change}
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: color,
              width: 56,
              height: 56,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );
};

export const DashboardStats: React.FC = () => {
  const { data: contactsStats, isLoading: contactsLoading } = useSWR('/api/contacts/stats');
  const { data: companiesStats, isLoading: companiesLoading } = useSWR('/api/companies/stats');
  const { data: dealsStats, isLoading: dealsLoading } = useSWR('/api/deals/pipeline');
  const { data: activitiesStats, isLoading: activitiesLoading } = useSWR('/api/activities/stats');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const stats = [
    {
      title: 'Total Contacts',
      value: contactsStats ? formatNumber(contactsStats.total) : 0,
      change: contactsStats?.recentlyCreated ? `+${contactsStats.recentlyCreated} this month` : undefined,
      changeType: 'positive' as const,
      icon: <PeopleIcon />,
      color: 'primary.main',
      isLoading: contactsLoading,
    },
    {
      title: 'Total Companies',
      value: companiesStats ? formatNumber(companiesStats.total) : 0,
      change: companiesStats?.recentlyCreated ? `+${companiesStats.recentlyCreated} this month` : undefined,
      changeType: 'positive' as const,
      icon: <BusinessIcon />,
      color: 'secondary.main',
      isLoading: companiesLoading,
    },
    {
      title: 'Pipeline Value',
      value: dealsStats ? formatCurrency(dealsStats.totalValue) : '$0',
      change: dealsStats?.weightedValue ? `${formatCurrency(dealsStats.weightedValue)} weighted` : undefined,
      changeType: 'neutral' as const,
      icon: <MoneyIcon />,
      color: 'success.main',
      isLoading: dealsLoading,
    },
    {
      title: 'Active Deals',
      value: dealsStats ? formatNumber(dealsStats.totalDeals) : 0,
      change: dealsStats?.averageDealSize ? `${formatCurrency(dealsStats.averageDealSize)} avg` : undefined,
      changeType: 'neutral' as const,
      icon: <TrendingUpIcon />,
      color: 'warning.main',
      isLoading: dealsLoading,
    },
    {
      title: 'Pending Tasks',
      value: activitiesStats ? formatNumber(activitiesStats.pending) : 0,
      change: activitiesStats?.overdue ? `${activitiesStats.overdue} overdue` : undefined,
      changeType: activitiesStats?.overdue > 0 ? 'negative' as const : 'neutral' as const,
      icon: <AssignmentIcon />,
      color: 'info.main',
      isLoading: activitiesLoading,
    },
    {
      title: 'Due Today',
      value: activitiesStats ? formatNumber(activitiesStats.dueToday) : 0,
      change: activitiesStats?.dueThisWeek ? `${activitiesStats.dueThisWeek} this week` : undefined,
      changeType: 'neutral' as const,
      icon: <AssignmentIcon />,
      color: 'error.main',
      isLoading: activitiesLoading,
    },
  ];

  return (
    <Grid container spacing={3}>
      {stats.map((stat, index) => (
        <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
          <StatCard {...stat} change={stat.change || ''} />
        </Grid>
      ))}
    </Grid>
  );
};
