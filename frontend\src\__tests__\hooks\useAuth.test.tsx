import React from 'react';
import { renderHook } from '@testing-library/react';
import { useAuth } from '../../components/providers/AuthProvider';
import { AuthContext } from '../../components/providers/AuthProvider';
import { AuthContextType } from '@onecrm/types';

// Mock AuthContext
const mockAuthContext: AuthContextType = {
  isAuthenticated: true,
  isLoading: false,
  user: {
    id: 'user-1',
    username: 'testuser',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    orgId: 'org-1',
    role: 'user',
    isActive: true,
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  organization: {
    id: 'org-1',
    name: 'Test Organization',
    slug: 'test-org',
    plan: 'pro',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  token: 'mock-jwt-token',
  login: jest.fn(),
  logout: jest.fn(),
  refreshToken: jest.fn(),
  hasRole: jest.fn(),
  hasPermission: jest.fn(),
};

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthContext.Provider value={mockAuthContext}>
    {children}
  </AuthContext.Provider>
);

describe('useAuth hook', () => {
  it('returns auth context when used within AuthProvider', () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    expect(result.current).toBe(mockAuthContext);
    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user?.email).toBe('<EMAIL>');
  });

  it('throws error when used outside AuthProvider', () => {
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
    
    expect(() => {
      renderHook(() => useAuth());
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleErrorSpy.mockRestore();
  });
});

describe('useHasRole hook', () => {
  it('returns result from hasRole function', () => {
    const mockHasRole = jest.fn().mockReturnValue(true);
    const contextWithMockHasRole = {
      ...mockAuthContext,
      hasRole: mockHasRole,
    };

    const wrapperWithMock = ({ children }: { children: React.ReactNode }) => (
      <AuthContext.Provider value={contextWithMockHasRole}>
        {children}
      </AuthContext.Provider>
    );

    const { result } = renderHook(() => useHasRole('admin'), { wrapper: wrapperWithMock });
    
    expect(result.current).toBe(true);
    expect(mockHasRole).toHaveBeenCalledWith('admin');
  });
});

describe('useHasPermission hook', () => {
  it('returns result from hasPermission function', () => {
    const mockHasPermission = jest.fn().mockReturnValue(false);
    const contextWithMockHasPermission = {
      ...mockAuthContext,
      hasPermission: mockHasPermission,
    };

    const wrapperWithMock = ({ children }: { children: React.ReactNode }) => (
      <AuthContext.Provider value={contextWithMockHasPermission}>
        {children}
      </AuthContext.Provider>
    );

    const { result } = renderHook(() => useHasPermission('users:delete'), { wrapper: wrapperWithMock });
    
    expect(result.current).toBe(false);
    expect(mockHasPermission).toHaveBeenCalledWith('users:delete');
  });
});
