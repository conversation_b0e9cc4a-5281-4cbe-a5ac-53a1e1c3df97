import { test, expect } from '@playwright/test'

test.describe('UI Positioning and Layout Check', () => {
  test.beforeEach(async ({ page }) => {
    // Set a consistent viewport size
    await page.setViewportSize({ width: 1920, height: 1080 })
  })

  test('Dashboard page layout and positioning', async ({ page }) => {
    await page.goto('/ui-test')
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle')
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/dashboard-full-page.png', 
      fullPage: true 
    })
    
    // Take viewport screenshot
    await page.screenshot({ 
      path: 'test-results/dashboard-viewport.png' 
    })
    
    // Check if main elements are visible and positioned correctly
    await expect(page.locator('h1')).toBeVisible()
    await expect(page.locator('[data-testid="metric-cards"]')).toBeVisible()
    
    console.log('Dashboard screenshots taken')
  })

  test('Component test page layout', async ({ page }) => {
    await page.goto('/component-test')
    
    await page.waitForLoadState('networkidle')
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/component-test-full-page.png', 
      fullPage: true 
    })
    
    // Take viewport screenshot
    await page.screenshot({ 
      path: 'test-results/component-test-viewport.png' 
    })
    
    console.log('Component test page screenshots taken')
  })

  test('Contacts page layout', async ({ page }) => {
    await page.goto('/contacts')
    
    await page.waitForLoadState('networkidle')
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/contacts-full-page.png', 
      fullPage: true 
    })
    
    // Take viewport screenshot
    await page.screenshot({ 
      path: 'test-results/contacts-viewport.png' 
    })
    
    console.log('Contacts page screenshots taken')
  })

  test('Companies page layout', async ({ page }) => {
    await page.goto('/companies')
    
    await page.waitForLoadState('networkidle')
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/companies-full-page.png', 
      fullPage: true 
    })
    
    // Take viewport screenshot
    await page.screenshot({ 
      path: 'test-results/companies-viewport.png' 
    })
    
    console.log('Companies page screenshots taken')
  })

  test('Deals page layout', async ({ page }) => {
    await page.goto('/deals')
    
    await page.waitForLoadState('networkidle')
    
    // Take full page screenshot
    await page.screenshot({ 
      path: 'test-results/deals-full-page.png', 
      fullPage: true 
    })
    
    // Take viewport screenshot
    await page.screenshot({ 
      path: 'test-results/deals-viewport.png' 
    })
    
    console.log('Deals page screenshots taken')
  })

  test('Mobile layout check - Dashboard', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Take mobile screenshot
    await page.screenshot({ 
      path: 'test-results/dashboard-mobile.png', 
      fullPage: true 
    })
    
    console.log('Mobile dashboard screenshot taken')
  })

  test('Tablet layout check - Dashboard', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })

    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Take tablet screenshot
    await page.screenshot({ 
      path: 'test-results/dashboard-tablet.png', 
      fullPage: true 
    })
    
    console.log('Tablet dashboard screenshot taken')
  })

  test('Navigation and sidebar positioning', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Take screenshot of navigation area
    const nav = page.locator('nav').first()
    if (await nav.isVisible()) {
      await nav.screenshot({ path: 'test-results/navigation.png' })
    }
    
    // Check if sidebar exists and take screenshot
    const sidebar = page.locator('[data-testid="sidebar"]')
    if (await sidebar.isVisible()) {
      await sidebar.screenshot({ path: 'test-results/sidebar.png' })
    }
    
    console.log('Navigation screenshots taken')
  })

  test('Form components positioning', async ({ page }) => {
    await page.goto('/component-test')
    await page.waitForLoadState('networkidle')
    
    // Scroll to form section
    await page.locator('form').first().scrollIntoViewIfNeeded()
    
    // Take screenshot of form area
    const form = page.locator('form').first()
    if (await form.isVisible()) {
      await form.screenshot({ path: 'test-results/form-components.png' })
    }
    
    console.log('Form components screenshot taken')
  })

  test('Button components positioning', async ({ page }) => {
    await page.goto('/component-test')
    await page.waitForLoadState('networkidle')
    
    // Find button section and take screenshot
    const buttonSection = page.locator('h3:has-text("Button Variants")').locator('..')
    if (await buttonSection.isVisible()) {
      await buttonSection.screenshot({ path: 'test-results/button-components.png' })
    }
    
    console.log('Button components screenshot taken')
  })

  test('Card components positioning', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Take screenshot of metric cards
    const metricsSection = page.locator('.grid').first()
    if (await metricsSection.isVisible()) {
      await metricsSection.screenshot({ path: 'test-results/metric-cards.png' })
    }
    
    console.log('Card components screenshot taken')
  })
})
