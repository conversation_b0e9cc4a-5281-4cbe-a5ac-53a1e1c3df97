import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';

export enum ChangeRequestType {
  PLAN_UPGRADE = 'plan_upgrade',
  PLAN_DOWNGRADE = 'plan_downgrade',
  BILLING_CYCLE_CHANGE = 'billing_cycle_change',
  PAYMENT_METHOD_UPDATE = 'payment_method_update',
  BILLING_ADDRESS_UPDATE = 'billing_address_update',
  ACCOUNT_CANCELLATION = 'account_cancellation',
  FEATURE_REQUEST = 'feature_request',
  USAGE_LIMIT_INCREASE = 'usage_limit_increase',
  CUSTOM_PLAN_REQUEST = 'custom_plan_request',
}

export enum ChangeRequestStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IMPLEMENTED = 'implemented',
  CANCELLED = 'cancelled',
}

export enum ChangeRequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Entity('account_change_requests')
@Index(['organizationId', 'status'])
@Index(['type', 'status'])
export class AccountChangeRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ type: 'varchar', length: 255, name: 'requested_by_user_id' })
  requestedByUserId: string;

  @Column({ type: 'varchar', length: 255, name: 'requested_by_email' })
  requestedByEmail: string;

  @Column({
    type: 'enum',
    enum: ChangeRequestType,
  })
  type: ChangeRequestType;

  @Column({
    type: 'enum',
    enum: ChangeRequestStatus,
    default: ChangeRequestStatus.PENDING,
  })
  status: ChangeRequestStatus;

  @Column({
    type: 'enum',
    enum: ChangeRequestPriority,
    default: ChangeRequestPriority.MEDIUM,
  })
  priority: ChangeRequestPriority;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'json' })
  requestData: {
    currentValues?: Record<string, any>;
    requestedValues?: Record<string, any>;
    planId?: string;
    billingCycle?: string;
    paymentMethodId?: string;
    billingAddress?: Record<string, any>;
    customRequirements?: string;
    businessJustification?: string;
  };

  @Column({ type: 'json', nullable: true })
  approvalData: {
    approvedBy?: string;
    approvedAt?: Date;
    approvalNotes?: string;
    implementationPlan?: string;
    estimatedImplementationDate?: Date;
  };

  @Column({ type: 'json', nullable: true })
  rejectionData: {
    rejectedBy?: string;
    rejectedAt?: Date;
    rejectionReason?: string;
    alternativeSuggestions?: string[];
  };

  @Column({ type: 'timestamp', nullable: true, name: 'target_implementation_date' })
  targetImplementationDate: Date;

  @Column({ type: 'timestamp', nullable: true, name: 'implemented_at' })
  implementedAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'implemented_by' })
  implementedBy: string;

  @Column({ type: 'text', nullable: true, name: 'implementation_notes' })
  implementationNotes: string;

  @Column({ type: 'json', nullable: true, name: 'communication_log' })
  communicationLog: Array<{
    timestamp: Date;
    type: 'email' | 'notification' | 'comment';
    from: string;
    to: string;
    subject?: string;
    message: string;
    metadata?: Record<string, any>;
  }>;

  @Column({ type: 'json', nullable: true })
  metadata: {
    urgencyReason?: string;
    businessImpact?: string;
    customerTier?: string;
    contractValue?: number;
    escalationLevel?: number;
    internalNotes?: string;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get isPending(): boolean {
    return this.status === ChangeRequestStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.status === ChangeRequestStatus.APPROVED;
  }

  get isImplemented(): boolean {
    return this.status === ChangeRequestStatus.IMPLEMENTED;
  }

  get daysSinceCreated(): number {
    const now = new Date();
    const diff = now.getTime() - this.createdAt.getTime();
    return Math.floor(diff / (1000 * 60 * 60 * 24));
  }

  get isOverdue(): boolean {
    if (!this.targetImplementationDate || this.isImplemented) return false;
    return new Date() > this.targetImplementationDate;
  }

  get requiresUrgentAttention(): boolean {
    return this.priority === ChangeRequestPriority.URGENT || 
           (this.priority === ChangeRequestPriority.HIGH && this.daysSinceCreated > 3) ||
           this.isOverdue;
  }
}
