import { useContext } from 'react';
import { AuthContext } from '../components/providers/AuthProvider';
import { AuthContextType } from '../types/auth';

/**
 * Custom hook to access authentication context
 * @returns AuthContextType
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
};

/**
 * Custom hook to check if user has specific role
 * @param role - Role to check
 * @returns boolean
 */
export const useHasRole = (role: string): boolean => {
  const { hasRole } = useAuth();
  return hasRole(role);
};

/**
 * Custom hook to check if user has specific permission
 * @param permission - Permission to check
 * @returns boolean
 */
export const useHasPermission = (permission: string): boolean => {
  const { hasPermission } = useAuth();
  return hasPermission(permission);
};

/**
 * Custom hook to get current user
 * @returns UserProfile | null
 */
export const useCurrentUser = () => {
  const { user } = useAuth();
  return user;
};

/**
 * Custom hook to get current organization
 * @returns Organization | null
 */
export const useCurrentOrganization = () => {
  const { organization } = useAuth();
  return organization;
};
