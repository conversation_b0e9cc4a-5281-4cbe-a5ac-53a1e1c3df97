import { PartialType } from '@nestjs/swagger';
import { CreateOrganizationDto } from './create-organization.dto';
import { IsOptional, IsString, MaxLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateOrganizationDto extends PartialType(CreateOrganizationDto) {
  @ApiProperty({ description: 'Organization name', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;
}

export class UpdateOrganizationSettingsDto {
  @ApiProperty({ description: 'Organization settings as key-value pairs' })
  settings: Record<string, any>;
}
