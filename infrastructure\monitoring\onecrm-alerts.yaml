groups:
  - name: onecrm.rules
    rules:
      # High-level application alerts
      - alert: OneCRMBackendDown
        expr: up{job="onecrm-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: onecrm-backend
        annotations:
          summary: "OneCRM Backend is down"
          description: "OneCRM Backend has been down for more than 1 minute in namespace {{ $labels.kubernetes_namespace }}"

      - alert: OneCRMFrontendDown
        expr: up{job="onecrm-frontend"} == 0
        for: 1m
        labels:
          severity: critical
          service: onecrm-frontend
        annotations:
          summary: "OneCRM Frontend is down"
          description: "OneCRM Frontend has been down for more than 1 minute in namespace {{ $labels.kubernetes_namespace }}"

      - alert: OneCRMHighErrorRate
        expr: rate(http_requests_total{job="onecrm-backend",status=~"5.."}[5m]) / rate(http_requests_total{job="onecrm-backend"}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "High error rate in OneCRM Backend"
          description: "OneCRM Backend error rate is {{ $value | humanizePercentage }} for more than 5 minutes"

      - alert: OneCRMHighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="onecrm-backend"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "High response time in OneCRM Backend"
          description: "OneCRM Backend 95th percentile response time is {{ $value }}s for more than 5 minutes"

      - alert: OneCRMDatabaseConnectionFailure
        expr: onecrm_database_connections_failed_total > 0
        for: 1m
        labels:
          severity: critical
          service: onecrm-backend
        annotations:
          summary: "Database connection failures"
          description: "OneCRM Backend is experiencing database connection failures"

      - alert: OneCRMRedisConnectionFailure
        expr: onecrm_redis_connections_failed_total > 0
        for: 1m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "Redis connection failures"
          description: "OneCRM Backend is experiencing Redis connection failures"

      # Infrastructure alerts
      - alert: PostgreSQLDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
          service: postgres
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
          service: redis
        annotations:
          summary: "Redis is down"
          description: "Redis cache has been down for more than 1 minute"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High memory usage"
          description: "Node {{ $labels.instance }} has memory usage above 90% for more than 5 minutes"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: infrastructure
        annotations:
          summary: "High CPU usage"
          description: "Node {{ $labels.instance }} has CPU usage above 80% for more than 5 minutes"

      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Low disk space"
          description: "Node {{ $labels.instance }} has less than 10% disk space available on {{ $labels.mountpoint }}"

      # Kubernetes alerts
      - alert: PodCrashLooping
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Pod is crash looping"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} is crash looping"

      - alert: PodNotReady
        expr: kube_pod_status_ready{condition="false"} == 1
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Pod not ready"
          description: "Pod {{ $labels.namespace }}/{{ $labels.pod }} has been not ready for more than 5 minutes"

      - alert: DeploymentReplicasMismatch
        expr: kube_deployment_spec_replicas != kube_deployment_status_available_replicas
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "Deployment replicas mismatch"
          description: "Deployment {{ $labels.namespace }}/{{ $labels.deployment }} has {{ $labels.spec_replicas }} desired but {{ $labels.available_replicas }} available replicas"

      # Business logic alerts
      - alert: OneCRMHighLoginFailureRate
        expr: rate(onecrm_login_failures_total[5m]) > 10
        for: 2m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "High login failure rate"
          description: "OneCRM is experiencing high login failure rate: {{ $value }} failures per second"

      - alert: OneCRMLowActiveUsers
        expr: onecrm_active_users_total < 1
        for: 10m
        labels:
          severity: info
          service: onecrm-backend
        annotations:
          summary: "Low active users"
          description: "OneCRM has very few active users: {{ $value }}"

      - alert: OneCRMHighAPIUsage
        expr: rate(http_requests_total{job="onecrm-backend"}[5m]) > 1000
        for: 5m
        labels:
          severity: info
          service: onecrm-backend
        annotations:
          summary: "High API usage"
          description: "OneCRM API is experiencing high usage: {{ $value }} requests per second"

      # Security alerts
      - alert: OneCRMUnauthorizedAccess
        expr: rate(http_requests_total{job="onecrm-backend",status="401"}[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "High unauthorized access attempts"
          description: "OneCRM is experiencing high unauthorized access attempts: {{ $value }} per second"

      - alert: OneCRMSuspiciousActivity
        expr: rate(http_requests_total{job="onecrm-backend",status="403"}[5m]) > 2
        for: 2m
        labels:
          severity: warning
          service: onecrm-backend
        annotations:
          summary: "Suspicious activity detected"
          description: "OneCRM is experiencing suspicious activity: {{ $value }} forbidden requests per second"
