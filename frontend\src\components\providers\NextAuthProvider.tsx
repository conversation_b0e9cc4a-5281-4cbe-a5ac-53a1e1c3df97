'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { SessionProvider, useSession, signIn, signOut } from 'next-auth/react';
import { 
  AuthContextType, 
  UserProfile, 
  Organization,
  UserProfileSchema,
  OrganizationSchema 
} from '../../types/auth';
import { apiClient } from '../../lib/api';

// Create Auth Context
export const NextAuthContext = createContext<AuthContextType | null>(null);

interface NextAuthProviderProps {
  children: React.ReactNode;
}

const NextAuthProviderInner: React.FC<NextAuthProviderProps> = ({ children }) => {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get token from NextAuth session
  const getToken = (): string | null => {
    return session?.accessToken || null;
  };

  // Login function
  const login = async () => {
    await signIn('keycloak');
  };

  // Logout function
  const logout = async () => {
    await signOut();
    setUser(null);
    setOrganization(null);
  };

  // Fetch user profile from backend
  const fetchUserProfile = async (token: string) => {
    try {
      const response = await apiClient.get('/users/profile', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const validatedUser = UserProfileSchema.parse(response.data);
      setUser(validatedUser);
      return validatedUser;
    } catch (error) {
      console.error('Failed to fetch user profile:', error);
      return null;
    }
  };

  // Fetch organization data
  const fetchOrganization = async (token: string, orgId: string) => {
    try {
      const response = await apiClient.get(`/organizations/${orgId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const validatedOrg = OrganizationSchema.parse(response.data);
      setOrganization(validatedOrg);
      return validatedOrg;
    } catch (error) {
      console.error('Failed to fetch organization:', error);
      return null;
    }
  };

  // Check if user has specific role
  const hasRole = (role: string): boolean => {
    if (!session?.roles) return false;
    return session.roles.includes(role);
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles: string[]): boolean => {
    if (!session?.roles) return false;
    return roles.some(role => session.roles.includes(role));
  };

  // Check if user has permission (simplified - in real app, this would check backend)
  const hasPermission = (permission: string): boolean => {
    if (!session?.roles) return false;
    
    // Simple role-to-permission mapping (in real app, this would be more sophisticated)
    const rolePermissions: Record<string, string[]> = {
      'super_admin': ['*'], // All permissions
      'org_admin': ['user:create', 'user:read', 'user:update', 'user:delete', 'org:manage'],
      'org_manager': ['user:read', 'user:update', 'contact:create', 'contact:read', 'contact:update'],
      'sales_manager': ['contact:create', 'contact:read', 'contact:update', 'deal:create', 'deal:read', 'deal:update'],
      'sales_rep': ['contact:read', 'contact:update', 'deal:read', 'deal:update'],
      'org_user': ['contact:read', 'deal:read'],
      'org_viewer': ['contact:read'],
    };

    // Check if user has super admin role (all permissions)
    if (session.roles.includes('super_admin')) return true;

    // Check specific permissions for user's roles
    for (const role of session.roles) {
      const permissions = rolePermissions[role] || [];
      if (permissions.includes(permission) || permissions.includes('*')) {
        return true;
      }
    }

    return false;
  };

  // Effect to handle session changes
  useEffect(() => {
    const handleSessionChange = async () => {
      setIsLoading(true);

      if (status === 'loading') {
        return; // Still loading
      }

      if (status === 'unauthenticated' || !session) {
        setUser(null);
        setOrganization(null);
        setIsLoading(false);
        return;
      }

      if (status === 'authenticated' && session) {
        const token = session.accessToken;
        if (token) {
          // Fetch user profile
          const userProfile = await fetchUserProfile(token);
          
          // Fetch organization if user has orgId
          if (session.orgId && userProfile) {
            await fetchOrganization(token, session.orgId);
          }
        }
      }

      setIsLoading(false);
    };

    handleSessionChange();
  }, [session, status]);

  const contextValue: AuthContextType = {
    isAuthenticated: status === 'authenticated',
    isLoading,
    user,
    organization,
    login,
    logout,
    getToken,
    hasRole,
    hasAnyRole,
    hasPermission,
    session: session || null,
  };

  return (
    <NextAuthContext.Provider value={contextValue}>
      {children}
    </NextAuthContext.Provider>
  );
};

export const NextAuthProvider: React.FC<NextAuthProviderProps> = ({ children }) => {
  return (
    <SessionProvider>
      <NextAuthProviderInner>{children}</NextAuthProviderInner>
    </SessionProvider>
  );
};

// Custom hook to use NextAuth context
export const useNextAuth = (): AuthContextType => {
  const context = useContext(NextAuthContext);
  if (!context) {
    throw new Error('useNextAuth must be used within a NextAuthProvider');
  }
  return context;
};
