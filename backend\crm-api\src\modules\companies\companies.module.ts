import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompaniesController } from './companies.controller';
import { CompaniesService } from './companies.service';
import { Company } from './company.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';

@Module({
  imports: [TypeOrmModule.forFeature([Company, User, Contact])],
  controllers: [CompaniesController],
  providers: [CompaniesService, TenantContextService],
  exports: [CompaniesService],
})
export class CompaniesModule {}
