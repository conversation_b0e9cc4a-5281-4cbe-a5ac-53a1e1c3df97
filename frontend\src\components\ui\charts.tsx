'use client'

import * as React from 'react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts'
import { cn } from '@/lib/utils'
import { EnhancedCard } from './enhanced-card'

// Color palette for charts
const CHART_COLORS = {
  primary: 'hsl(var(--primary))',
  secondary: 'hsl(var(--secondary))',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6',
  muted: 'hsl(var(--muted-foreground))',
}

const CHART_COLOR_PALETTE = [
  '#8884d8',
  '#82ca9d',
  '#ffc658',
  '#ff7300',
  '#00ff00',
  '#0088fe',
  '#00c49f',
  '#ffbb28',
  '#ff8042',
  '#8dd1e1',
]

// Base chart wrapper component
interface ChartWrapperProps {
  title?: string
  description?: string
  children: React.ReactNode
  className?: string
  height?: number
  loading?: boolean
  error?: string
}

function ChartWrapper({
  title,
  description,
  children,
  className,
  height = 300,
  loading = false,
  error,
}: ChartWrapperProps) {
  if (loading) {
    return (
      <EnhancedCard title={title} description={description} className={className}>
        <div 
          className="flex items-center justify-center bg-muted/20 rounded animate-pulse"
          style={{ height }}
        >
          <div className="text-muted-foreground">Loading chart...</div>
        </div>
      </EnhancedCard>
    )
  }

  if (error) {
    return (
      <EnhancedCard title={title} description={description} className={className}>
        <div 
          className="flex items-center justify-center bg-destructive/10 rounded border border-destructive/20"
          style={{ height }}
        >
          <div className="text-destructive text-sm">Error loading chart: {error}</div>
        </div>
      </EnhancedCard>
    )
  }

  return (
    <EnhancedCard title={title} description={description} className={className}>
      <div style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          {children}
        </ResponsiveContainer>
      </div>
    </EnhancedCard>
  )
}

// Custom tooltip component
interface CustomTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
  formatter?: (value: any, name: string) => [string, string]
}

function CustomTooltip({ active, payload, label, formatter }: CustomTooltipProps) {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border rounded-lg shadow-lg p-3">
        <p className="font-medium text-foreground mb-2">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-muted-foreground">{entry.name}:</span>
            <span className="font-medium text-foreground">
              {formatter ? formatter(entry.value, entry.name)[0] : entry.value}
            </span>
          </div>
        ))}
      </div>
    )
  }
  return null
}

// Line Chart Component
interface LineChartData {
  name: string
  [key: string]: any
}

interface EnhancedLineChartProps {
  data: LineChartData[]
  lines: Array<{
    dataKey: string
    name: string
    color?: string
    strokeWidth?: number
    strokeDasharray?: string
  }>
  title?: string
  description?: string
  height?: number
  showGrid?: boolean
  showLegend?: boolean
  className?: string
  loading?: boolean
  error?: string
  formatter?: (value: any, name: string) => [string, string]
}

export function EnhancedLineChart({
  data,
  lines,
  title,
  description,
  height = 300,
  showGrid = true,
  showLegend = true,
  className,
  loading,
  error,
  formatter,
}: EnhancedLineChartProps) {
  return (
    <ChartWrapper
      title={title}
      description={description}
      height={height}
      className={className}
      loading={loading}
      error={error}
    >
      <LineChart data={data}>
        {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />}
        <XAxis 
          dataKey="name" 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
        />
        <YAxis 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
        />
        <Tooltip content={<CustomTooltip formatter={formatter} />} />
        {showLegend && <Legend />}
        {lines.map((line, index) => (
          <Line
            key={line.dataKey}
            type="monotone"
            dataKey={line.dataKey}
            name={line.name}
            stroke={line.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length]}
            strokeWidth={line.strokeWidth || 2}
            strokeDasharray={line.strokeDasharray}
            dot={{ fill: line.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length], r: 4 }}
            activeDot={{ r: 6 }}
          />
        ))}
      </LineChart>
    </ChartWrapper>
  )
}

// Area Chart Component
interface EnhancedAreaChartProps {
  data: LineChartData[]
  areas: Array<{
    dataKey: string
    name: string
    color?: string
    fillOpacity?: number
  }>
  title?: string
  description?: string
  height?: number
  showGrid?: boolean
  showLegend?: boolean
  className?: string
  loading?: boolean
  error?: string
  stacked?: boolean
}

export function EnhancedAreaChart({
  data,
  areas,
  title,
  description,
  height = 300,
  showGrid = true,
  showLegend = true,
  className,
  loading,
  error,
  stacked = false,
}: EnhancedAreaChartProps) {
  return (
    <ChartWrapper
      title={title}
      description={description}
      height={height}
      className={className}
      loading={loading}
      error={error}
    >
      <AreaChart data={data}>
        {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />}
        <XAxis 
          dataKey="name" 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
        />
        <YAxis 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend />}
        {areas.map((area, index) => (
          <Area
            key={area.dataKey}
            type="monotone"
            dataKey={area.dataKey}
            name={area.name}
            stackId={stacked ? "1" : undefined}
            stroke={area.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length]}
            fill={area.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length]}
            fillOpacity={area.fillOpacity || 0.6}
          />
        ))}
      </AreaChart>
    </ChartWrapper>
  )
}

// Bar Chart Component
interface EnhancedBarChartProps {
  data: LineChartData[]
  bars: Array<{
    dataKey: string
    name: string
    color?: string
  }>
  title?: string
  description?: string
  height?: number
  showGrid?: boolean
  showLegend?: boolean
  className?: string
  loading?: boolean
  error?: string
  horizontal?: boolean
}

export function EnhancedBarChart({
  data,
  bars,
  title,
  description,
  height = 300,
  showGrid = true,
  showLegend = true,
  className,
  loading,
  error,
  horizontal = false,
}: EnhancedBarChartProps) {
  return (
    <ChartWrapper
      title={title}
      description={description}
      height={height}
      className={className}
      loading={loading}
      error={error}
    >
      <BarChart data={data} layout={horizontal ? 'horizontal' : 'vertical'}>
        {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />}
        <XAxis 
          dataKey="name" 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
          type={horizontal ? 'number' : 'category'}
        />
        <YAxis 
          stroke="hsl(var(--muted-foreground))"
          fontSize={12}
          type={horizontal ? 'category' : 'number'}
        />
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend />}
        {bars.map((bar, index) => (
          <Bar
            key={bar.dataKey}
            dataKey={bar.dataKey}
            name={bar.name}
            fill={bar.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length]}
            radius={[2, 2, 0, 0]}
          />
        ))}
      </BarChart>
    </ChartWrapper>
  )
}

// Pie Chart Component
interface PieChartData {
  name: string
  value: number
  color?: string
}

interface EnhancedPieChartProps {
  data: PieChartData[]
  title?: string
  description?: string
  height?: number
  showLegend?: boolean
  className?: string
  loading?: boolean
  error?: string
  innerRadius?: number
  outerRadius?: number
  showLabels?: boolean
}

export function EnhancedPieChart({
  data,
  title,
  description,
  height = 300,
  showLegend = true,
  className,
  loading,
  error,
  innerRadius = 0,
  outerRadius = 80,
  showLabels = true,
}: EnhancedPieChartProps) {
  const renderLabel = (entry: any) => {
    if (!showLabels) return null
    return `${entry.name}: ${entry.value}`
  }

  return (
    <ChartWrapper
      title={title}
      description={description}
      height={height}
      className={className}
      loading={loading}
      error={error}
    >
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderLabel}
          outerRadius={outerRadius}
          innerRadius={innerRadius}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.color || CHART_COLOR_PALETTE[index % CHART_COLOR_PALETTE.length]} 
            />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
        {showLegend && <Legend />}
      </PieChart>
    </ChartWrapper>
  )
}

// Metric Chart (Simple number display with trend)
interface MetricChartProps {
  value: number | string
  label: string
  trend?: {
    value: number
    direction: 'up' | 'down' | 'neutral'
    period?: string
  }
  format?: 'number' | 'currency' | 'percentage'
  className?: string
}

export function MetricChart({
  value,
  label,
  trend,
  format = 'number',
  className,
}: MetricChartProps) {
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(val)
      case 'percentage':
        return `${val}%`
      default:
        return new Intl.NumberFormat('en-US').format(val)
    }
  }

  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-muted-foreground'
    }
  }

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up':
        return '↗'
      case 'down':
        return '↘'
      default:
        return '→'
    }
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="text-2xl font-bold">{formatValue(value)}</div>
      <div className="text-sm text-muted-foreground">{label}</div>
      {trend && (
        <div className={cn('text-sm flex items-center gap-1', getTrendColor(trend.direction))}>
          <span>{getTrendIcon(trend.direction)}</span>
          <span>{Math.abs(trend.value)}%</span>
          {trend.period && <span className="text-muted-foreground">vs {trend.period}</span>}
        </div>
      )}
    </div>
  )
}

export { CHART_COLORS, CHART_COLOR_PALETTE }
