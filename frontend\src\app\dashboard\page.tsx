'use client';

import React, { useState, useEffect } from 'react';
import { AppShell, Container, PageHeader, ContentSection } from '@/components/layout/app-shell';
import { EnhancedCard, MetricCard, ActionCard } from '@/components/ui/enhanced-card';
import { EnhancedBadge, StatusBadge } from '@/components/ui/enhanced-badge';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Users,
  Building2,
  TrendingUp,
  DollarSign,
  Activity,
  Calendar,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Eye,
  Info
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { formatCurrency, formatDate } from '@/lib/utils';

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const isAuthenticated = status === 'authenticated';
  const isLoading = status === 'loading';
  const user = session?.user;

  useEffect(() => {
    // Simulate loading dashboard data
    const loadDashboardData = async () => {
      try {
        // Mock data for development
        const mockData = {
          stats: {
            totalContacts: 1247,
            totalCompanies: 89,
            totalDeals: 156,
            totalRevenue: 2450000,
            monthlyGrowth: 12.5,
            conversionRate: 24.8,
          },
          recentActivity: [
            {
              id: '1',
              type: 'deal',
              title: 'New deal created: Enterprise Software License',
              description: 'Deal worth $50,000 added to pipeline',
              timestamp: new Date().toISOString(),
              user: 'John Doe',
            },
            {
              id: '2',
              type: 'contact',
              title: 'Contact updated: Sarah Johnson',
              description: 'Contact information and preferences updated',
              timestamp: new Date(Date.now() - 3600000).toISOString(),
              user: 'Jane Smith',
            },
            {
              id: '3',
              type: 'company',
              title: 'New company added: TechCorp Solutions',
              description: 'Enterprise client with 500+ employees',
              timestamp: new Date(Date.now() - 7200000).toISOString(),
              user: 'Mike Wilson',
            },
          ],
        };

        setDashboardData(mockData);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading || loading) {
    return (
      <AppShell>
        <Container className="py-8">
          <div className="space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Skeleton className="h-96 lg:col-span-2" />
              <Skeleton className="h-96" />
            </div>
          </div>
        </Container>
      </AppShell>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        {/* Header */}
        <PageHeader
          title="Dashboard"
          description={`Welcome back, ${user?.name || 'User'}!`}
          actions={
            <div className="flex items-center gap-2">
              <EnhancedBadge variant="info" icon={<Info className="h-3 w-3" />}>
                Demo Mode
              </EnhancedBadge>
              <EnhancedButton
                variant="outline"
                size="sm"
                leftIcon={<Plus className="h-4 w-4" />}
                onClick={() => router.push('/contacts/new')}
              >
                Quick Add
              </EnhancedButton>
            </div>
          }
        />

        {/* Demo Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Demo Environment:</strong> This is a demonstration of the OneCRM system.
            All data shown is mock data for testing purposes.
          </AlertDescription>
        </Alert>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-testid="metric-cards">
          <MetricCard
            value={dashboardData?.stats?.totalContacts?.toLocaleString() || '0'}
            label="Total Contacts"
            change={{
              value: 8.2,
              type: 'increase',
              period: 'last month'
            }}
            icon={<Users className="h-6 w-6" />}
          />
          <MetricCard
            value={dashboardData?.stats?.totalCompanies?.toLocaleString() || '0'}
            label="Companies"
            change={{
              value: 12.5,
              type: 'increase',
              period: 'last month'
            }}
            icon={<Building2 className="h-6 w-6" />}
          />
          <MetricCard
            value={dashboardData?.stats?.totalDeals?.toLocaleString() || '0'}
            label="Active Deals"
            change={{
              value: 3.1,
              type: 'decrease',
              period: 'last month'
            }}
            icon={<TrendingUp className="h-6 w-6" />}
          />
          <MetricCard
            value={formatCurrency(dashboardData?.stats?.totalRevenue || 0)}
            label="Total Revenue"
            change={{
              value: 15.3,
              type: 'increase',
              period: 'last month'
            }}
            icon={<DollarSign className="h-6 w-6" />}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <EnhancedCard
              title="Recent Activity"
              description="Latest updates and changes in your CRM"
              headerActions={
                <EnhancedButton
                  variant="ghost"
                  size="sm"
                  rightIcon={<Eye className="h-4 w-4" />}
                >
                  View All
                </EnhancedButton>
              }
            >
              <div className="space-y-4">
                {dashboardData?.recentActivity?.map((activity: any) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="p-2 rounded-full bg-primary/10">
                      <Activity className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm">{activity.title}</p>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatDate(activity.timestamp)}
                        </span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{activity.user}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>
          </div>

          {/* Quick Actions */}
          <div>
            <EnhancedCard
              title="Quick Actions"
              description="Common tasks and shortcuts"
            >
              <div className="space-y-3">
                <ActionCard
                  icon={<Users className="h-5 w-5" />}
                  label="Add Contact"
                  description="Create a new contact"
                  onCardClick={() => router.push('/contacts/new')}
                />
                <ActionCard
                  icon={<Building2 className="h-5 w-5" />}
                  label="Add Company"
                  description="Register a new company"
                  onCardClick={() => router.push('/companies/new')}
                />
                <ActionCard
                  icon={<TrendingUp className="h-5 w-5" />}
                  label="Create Deal"
                  description="Start a new deal"
                  onCardClick={() => router.push('/deals/new')}
                />
                <ActionCard
                  icon={<Calendar className="h-5 w-5" />}
                  label="Schedule Meeting"
                  description="Book a meeting"
                  onCardClick={() => router.push('/calendar/new')}
                />
              </div>
            </EnhancedCard>
          </div>
        </div>

        {/* Analytics Overview */}
        <EnhancedCard
          title="Analytics Overview"
          description="Key performance metrics and trends"
          headerActions={
            <EnhancedButton
              variant="outline"
              size="sm"
              rightIcon={<ArrowUpRight className="h-4 w-4" />}
              onClick={() => router.push('/analytics')}
            >
              View Details
            </EnhancedButton>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Conversion Rate</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">24.8%</span>
                <div className="flex items-center text-green-600 text-sm">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>+2.1%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Monthly Growth</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">12.5%</span>
                <div className="flex items-center text-green-600 text-sm">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>+0.8%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Activity Score</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">87</span>
                <div className="flex items-center text-red-600 text-sm">
                  <ArrowDownRight className="h-3 w-3" />
                  <span>-3</span>
                </div>
              </div>
            </div>
          </div>
        </EnhancedCard>

        {/* Navigation Cards */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Explore OneCRM</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <ActionCard
              icon={<Users className="h-6 w-6" />}
              label="Contacts"
              description="Manage your customer contacts and relationships"
              onCardClick={() => router.push('/contacts')}
            />
            <ActionCard
              icon={<Building2 className="h-6 w-6" />}
              label="Companies"
              description="Track companies and organizational hierarchies"
              onCardClick={() => router.push('/companies')}
            />
            <ActionCard
              icon={<TrendingUp className="h-6 w-6" />}
              label="Deals"
              description="Manage sales pipeline and deal progression"
              onCardClick={() => router.push('/deals')}
            />
            <ActionCard
              icon={<BarChart3 className="h-6 w-6" />}
              label="Analytics"
              description="Advanced reporting and business insights"
              onCardClick={() => router.push('/analytics')}
            />
          </div>
        </div>
      </Container>
    </AppShell>
  );
}
