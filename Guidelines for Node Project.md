# **Guidelines for Node Project**

​​Application Development Guidelines  
1\. Programming Language and Stack Discipline  
Strictly use Node.js for backend development.

Do not switch to Python or any other language for backend services. If an issue arises, resolve it in Node.js. No shortcuts or workarounds in other languages.

2\. Development Workflow  
Hot Reload must be enabled for all services in development mode.

The server should always run with hot reload to minimize downtime and maximize productivity.

Do not break the app build while editing code.

Use feature flags, branches, and pull request reviews to keep main/master always buildable and deployable.

All development must follow Test Driven Development (TDD).

Write tests before code. Ensure coverage is high, especially for core business logic.

Write and run all necessary tests.

Failed tests must be auto-fixed before merging code.

Milestone Documentation.

Only generate documentation in /docs when a meaningful milestone is achieved and the related feature is stable.

3\. Project Architecture & Code Organization  
Adopt microservice architecture for all backend components.

Keycloak Onesso must be the sole auth provider. No duplicate or secondary authentication logic.

Frontend and backend code are strictly separated, communicating via Kong API Gateway.

Directory Structure (Do not duplicate files across directories):

/frontend — Next.js app, all frontend logic, components, and assets.

/backend — All backend microservices, APIs, and business logic.

/mobile — Flutter app and related Dart code.

/scripts — Automation scripts, tools, migrations, and one-off utilities.

/docs — All markdown (.md) documentation files.

4\. Frontend (Next.js, Monorepo, Dynamic UI)  
No static HTML. No plain HTML demos. All UI must be developed in Next.js.

Frontend follows Nx Monorepo standards.

Dynamic UI: Use SchemaToMUI approach — generate UI components from JSON schemas.

Automatic integration with backend APIs via Kong Gateway is mandatory.

Type Safety:

Avoid TypeScript type mismatches at all costs.

Use Zod v4 for all schema validations and enforce end-to-end type safety.

Libraries and Standards:

Animations: GSAP

Tables: TanStack Table

Forms: react-jsonschema-form

Charts: Recharts

UI/UX: Material Tailwind for design system

Material UI Interoperability: Adhere to MUI Integration Guidelines

Do not create duplicate files. Refactor or replace existing files if changes are necessary.

5\. Backend (Node.js, Microservices)  
API Development:

Every API must be defined and compliant with OpenAPI specifications.

Maintain corresponding OpenAPI spec files (.yaml or .json).

Multi-Tenancy:

All APIs and services must be tenant-aware and customizable per tenant.

Do not hard-code tenant-specific logic.

Testing:

All backend logic and APIs must be covered by automated tests.

No code is merged unless all tests pass.

6\. Authentication & Security  
Keycloak Onesso is the only auth provider.

Suspend authentication validations in development mode (only for local/dev builds, never for production).

Never commit credentials, secrets, or API keys to the repository. Use environment variables and secure secret stores.

7\. CI/CD & Code Management  
Run all tests and lint checks on every commit.

Auto-fix test failures before merging.

No code is merged into main branches without review and passing tests.

Opinion & Recommendations  
Be uncompromising on TDD and type safety. Most bugs and production failures can be traced to lax testing or type errors—these are non-negotiable standards.

Documentation after milestones prevents noise and ensures only stable, production-ready features are documented.

Never break builds: Use feature toggles and protect critical paths; this keeps team velocity high and reduces firefighting.

OpenAPI compliance is essential for future integrations, contract testing, and documentation.

Refactor, don’t duplicate: Duplicates kill maintainability—refactoring should be the norm.

Monorepo and microservice approach keeps code organized and scales better for multi-tenant customizations.

Suspend auth only in dev mode, and revert before staging/production. Accidental deployments without auth are unacceptable.

Forward-Looking View:  
If these guidelines are strictly enforced, you will achieve a maintainable, scalable, and robust platform. Cutting corners—be it with type safety, documentation, or test coverage—will always cost 10x more in production.  
