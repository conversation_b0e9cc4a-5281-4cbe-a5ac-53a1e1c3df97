'use client'

import { useState } from 'react'
import { z } from 'zod'
import { Container } from '@/components/layout/app-shell'
import { 
  EnhancedButton, 
  IconButton, 
  LoadingButton, 
  ActionButton 
} from '@/components/ui/enhanced-button'
import {
  EnhancedForm,
  FormField,
  FormInput,
  FormTextarea,
  FormSelect,
  FormCheckbox,
  FormActions,
  FormSubmit,
  FormValidationStatus
} from '@/components/ui/enhanced-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Plus, Edit, Trash2, Save, X } from 'lucide-react'

// Form validation schema
const testFormSchema = z.object({
  name: z.string().min(1, 'Name is required').min(2, 'Name must be at least 2 characters'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  description: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  agree: z.boolean().refine(val => val === true, 'You must agree to the terms'),
})

type TestFormData = z.infer<typeof testFormSchema>

export default function ComponentTestPage() {
  const [buttonClicked, setButtonClicked] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [submissionError, setSubmissionError] = useState(false)

  const handleButtonClick = () => {
    setButtonClicked(true)
    setTimeout(() => setButtonClicked(false), 2000)
  }

  const handleLoadingToggle = () => {
    setIsLoading(!isLoading)
  }

  const handleFormSubmit = async (data: TestFormData) => {
    setSubmissionError(false)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Simulate random failure for testing
    if (Math.random() > 0.7) {
      setSubmissionError(true)
      throw new Error('Submission failed')
    }
    
    setFormSubmitted(true)
    console.log('Form submitted:', data)
    
    // Reset success message after 3 seconds
    setTimeout(() => setFormSubmitted(false), 3000)
  }

  const categoryOptions = [
    { value: 'business', label: 'Business' },
    { value: 'personal', label: 'Personal' },
    { value: 'other', label: 'Other' },
  ]

  return (
    <Container className="py-8">
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold">Component Test Page</h1>
          <p className="text-muted-foreground">
            Testing enhanced UI components with accessibility and functionality
          </p>
        </div>

        {/* Button Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Enhanced Button Components</CardTitle>
            <CardDescription>
              Testing different button variants, sizes, and states
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Button Variants */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Button Variants</h3>
              <div className="flex flex-wrap gap-2">
                <EnhancedButton 
                  data-testid="button-default"
                  onClick={handleButtonClick}
                >
                  Default
                </EnhancedButton>
                <EnhancedButton 
                  variant="destructive"
                  data-testid="button-destructive"
                >
                  Destructive
                </EnhancedButton>
                <EnhancedButton 
                  variant="outline"
                  data-testid="button-outline"
                >
                  Outline
                </EnhancedButton>
                <EnhancedButton 
                  variant="secondary"
                >
                  Secondary
                </EnhancedButton>
                <EnhancedButton 
                  variant="ghost"
                >
                  Ghost
                </EnhancedButton>
                <EnhancedButton 
                  variant="success"
                >
                  Success
                </EnhancedButton>
                <EnhancedButton 
                  variant="warning"
                >
                  Warning
                </EnhancedButton>
              </div>
            </div>

            <Separator />

            {/* Button Sizes */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Button Sizes</h3>
              <div className="flex flex-wrap items-center gap-2">
                <EnhancedButton size="xs">Extra Small</EnhancedButton>
                <EnhancedButton 
                  size="sm"
                  data-testid="button-small"
                >
                  Small
                </EnhancedButton>
                <EnhancedButton>Default</EnhancedButton>
                <EnhancedButton 
                  size="lg"
                  data-testid="button-large"
                >
                  Large
                </EnhancedButton>
                <EnhancedButton size="xl">Extra Large</EnhancedButton>
              </div>
            </div>

            <Separator />

            {/* Button States */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Button States</h3>
              <div className="flex flex-wrap gap-2">
                <LoadingButton 
                  isLoading={isLoading}
                  data-testid="button-loading"
                  onClick={handleLoadingToggle}
                  loadingText="Loading..."
                >
                  Toggle Loading
                </LoadingButton>
                <EnhancedButton 
                  disabled
                  data-testid="button-disabled"
                >
                  Disabled
                </EnhancedButton>
                <EnhancedButton 
                  leftIcon={<Plus className="h-4 w-4" />}
                >
                  With Icon
                </EnhancedButton>
                <IconButton 
                  icon={<Edit className="h-4 w-4" />}
                  tooltip="Edit item"
                />
              </div>
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Action Buttons</h3>
              <div className="flex flex-wrap gap-2">
                <ActionButton action="create" leftIcon={<Plus className="h-4 w-4" />} />
                <ActionButton action="edit" leftIcon={<Edit className="h-4 w-4" />} />
                <ActionButton action="delete" leftIcon={<Trash2 className="h-4 w-4" />} />
                <ActionButton action="save" leftIcon={<Save className="h-4 w-4" />} />
                <ActionButton action="cancel" leftIcon={<X className="h-4 w-4" />} />
              </div>
            </div>

            {buttonClicked && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">Button clicked!</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Form Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Enhanced Form Components</CardTitle>
            <CardDescription>
              Testing form validation, accessibility, and user experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <EnhancedForm
              schema={testFormSchema}
              onSubmit={handleFormSubmit}
              defaultValues={{
                name: '',
                email: '',
                description: '',
                category: '',
                agree: false,
              }}
              data-testid="enhanced-form"
            >
              <div className="space-y-6">
                <FormField name="name" label="Name" required>
                  <FormInput 
                    name="name" 
                    placeholder="Enter your full name"
                  />
                </FormField>

                <FormField 
                  name="email" 
                  label="Email" 
                  required
                  description="We'll never share your email with anyone else"
                >
                  <FormInput 
                    name="email" 
                    type="email"
                    placeholder="Enter your email address"
                  />
                </FormField>

                <FormField 
                  name="description" 
                  label="Description"
                  description="Optional description or comments"
                >
                  <FormTextarea 
                    name="description"
                    placeholder="Enter a description..."
                    rows={3}
                  />
                </FormField>

                <FormField name="category" label="Category" required>
                  <FormSelect
                    name="category"
                    placeholder="Select a category"
                    options={categoryOptions}
                  />
                </FormField>

                <FormCheckbox
                  name="agree"
                  label="I agree to the terms and conditions"
                  description="You must agree to continue"
                />

                <FormValidationStatus />

                <FormActions>
                  <EnhancedButton 
                    type="button" 
                    variant="outline"
                    onClick={() => window.location.reload()}
                  >
                    Reset
                  </EnhancedButton>
                  <FormSubmit loadingText="Submitting...">
                    Submit Form
                  </FormSubmit>
                </FormActions>
              </div>
            </EnhancedForm>

            {formSubmitted && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                <p className="text-green-800">Form submitted successfully!</p>
              </div>
            )}

            {submissionError && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-800">Submission failed. Please try again.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
