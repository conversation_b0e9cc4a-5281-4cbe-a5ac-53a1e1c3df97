import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { AdminAuditLog, AdminAction, AdminActionResult } from './entities/admin-audit-log.entity';
import { SystemAlert, AlertSeverity, AlertCategory, AlertStatus } from './entities/system-alert.entity';
import { TenantHealthCheck } from './entities/tenant-health-check.entity';
import { OrganizationService } from '../organization/organization.service';
import { SubscriptionService } from '../subscription/subscription.service';
import { UsageMonitoringService } from '../usage-monitoring/usage-monitoring.service';
import { FeatureGatingService } from '../feature-gating/feature-gating.service';
import { UserService } from '../user/user.service';

export interface TenantOverview {
  id: string;
  name: string;
  status: 'active' | 'suspended' | 'trial' | 'expired';
  plan: string;
  users: number;
  createdAt: Date;
  lastActivity: Date;
  subscription: {
    status: string;
    daysUntilRenewal: number;
    monthlyRevenue: number;
  };
  usage: {
    contacts: number;
    deals: number;
    apiCalls: number;
    storageGB: number;
  };
  health: {
    score: number;
    issues: string[];
  };
}

export interface SystemMetrics {
  totalTenants: number;
  activeTenants: number;
  trialTenants: number;
  suspendedTenants: number;
  totalUsers: number;
  totalRevenue: number;
  monthlyRecurringRevenue: number;
  churnRate: number;
  averageUsage: {
    contacts: number;
    deals: number;
    apiCalls: number;
    storageGB: number;
  };
  systemHealth: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    activeAlerts: number;
  };
}

export interface AdminActionRequest {
  adminUserId: string;
  adminUserEmail: string;
  action: AdminAction;
  targetOrganizationId?: string;
  targetUserId?: string;
  description: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable()
export class TenantAdminService {
  private readonly logger = new Logger(TenantAdminService.name);

  constructor(
    @InjectRepository(AdminAuditLog)
    private readonly auditLogRepository: Repository<AdminAuditLog>,
    @InjectRepository(SystemAlert)
    private readonly systemAlertRepository: Repository<SystemAlert>,
    @InjectRepository(TenantHealthCheck)
    private readonly healthCheckRepository: Repository<TenantHealthCheck>,
    private readonly organizationService: OrganizationService,
    private readonly subscriptionService: SubscriptionService,
    private readonly usageMonitoringService: UsageMonitoringService,
    private readonly featureGatingService: FeatureGatingService,
    private readonly userService: UserService,
  ) {}

  async getTenantOverview(organizationId: string): Promise<TenantOverview> {
    // Get organization
    const organization = await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Get subscription
    const subscription = await this.subscriptionService.getOrganizationSubscription(organizationId);
    
    // Get usage stats
    const usageStats = await this.usageMonitoringService.getUsageStats(organizationId);
    
    // Get user count
    const userCount = await this.userService.getOrganizationUserCount(organizationId);
    
    // Get health check
    const healthCheck = await this.getLatestHealthCheck(organizationId);

    // Determine status
    let status: 'active' | 'suspended' | 'trial' | 'expired' = 'active';
    if (organization.suspended) {
      status = 'suspended';
    } else if (subscription?.isTrialing) {
      status = 'trial';
    } else if (subscription && !subscription.isActive) {
      status = 'expired';
    }

    return {
      id: organization.id,
      name: organization.name,
      status,
      plan: subscription?.plan?.name || 'Free',
      users: userCount,
      createdAt: organization.createdAt,
      lastActivity: organization.lastActivityAt || organization.updatedAt,
      subscription: {
        status: subscription?.status || 'none',
        daysUntilRenewal: subscription?.daysUntilRenewal || 0,
        monthlyRevenue: subscription?.plan?.price || 0,
      },
      usage: {
        contacts: usageStats.metrics.contacts?.current || 0,
        deals: usageStats.metrics.deals?.current || 0,
        apiCalls: usageStats.metrics.api_calls?.current || 0,
        storageGB: Math.round((usageStats.metrics.storage_bytes?.current || 0) / (1024 * 1024 * 1024) * 100) / 100,
      },
      health: {
        score: healthCheck?.healthScore || 100,
        issues: healthCheck?.issues || [],
      },
    };
  }

  async getAllTenants(
    page = 1,
    limit = 50,
    filters?: {
      status?: string;
      plan?: string;
      search?: string;
    },
  ): Promise<{ tenants: TenantOverview[]; total: number; page: number; limit: number }> {
    // Get organizations with filters
    const organizations = await this.organizationService.findAll({
      page,
      limit,
      search: filters?.search,
      status: filters?.status,
    });

    // Get tenant overviews
    const tenantOverviews = await Promise.all(
      organizations.data.map(org => this.getTenantOverview(org.id))
    );

    // Apply additional filters
    let filteredTenants = tenantOverviews;
    if (filters?.plan) {
      filteredTenants = tenantOverviews.filter(tenant => tenant.plan === filters.plan);
    }

    return {
      tenants: filteredTenants,
      total: organizations.total,
      page,
      limit,
    };
  }

  async getSystemMetrics(): Promise<SystemMetrics> {
    // Get all organizations
    const allOrganizations = await this.organizationService.findAll({ page: 1, limit: 10000 });
    const organizations = allOrganizations.data;

    // Calculate tenant metrics
    const totalTenants = organizations.length;
    const activeTenants = organizations.filter(org => !org.suspended).length;
    const suspendedTenants = organizations.filter(org => org.suspended).length;

    // Get subscription metrics
    let trialTenants = 0;
    let totalRevenue = 0;
    let monthlyRecurringRevenue = 0;

    for (const org of organizations) {
      const subscription = await this.subscriptionService.getOrganizationSubscription(org.id);
      if (subscription?.isTrialing) {
        trialTenants++;
      }
      if (subscription?.plan) {
        const revenue = subscription.plan.price;
        totalRevenue += revenue;
        if (subscription.isActive) {
          monthlyRecurringRevenue += revenue;
        }
      }
    }

    // Calculate churn rate (simplified)
    const churnRate = suspendedTenants / Math.max(totalTenants, 1) * 100;

    // Get total users
    const totalUsers = await this.userService.getTotalUserCount();

    // Get system health metrics
    const activeAlerts = await this.systemAlertRepository.count({
      where: { status: AlertStatus.ACTIVE },
    });

    // Calculate average usage (simplified)
    const averageUsage = {
      contacts: Math.round(totalUsers * 50), // Estimate
      deals: Math.round(totalUsers * 10), // Estimate
      apiCalls: Math.round(totalUsers * 100), // Estimate
      storageGB: Math.round(totalUsers * 0.5), // Estimate
    };

    return {
      totalTenants,
      activeTenants,
      trialTenants,
      suspendedTenants,
      totalUsers,
      totalRevenue,
      monthlyRecurringRevenue,
      churnRate: Math.round(churnRate * 100) / 100,
      averageUsage,
      systemHealth: {
        uptime: 99.9, // This would come from monitoring
        responseTime: 150, // This would come from monitoring
        errorRate: 0.1, // This would come from monitoring
        activeAlerts,
      },
    };
  }

  async suspendTenant(
    organizationId: string,
    adminRequest: AdminActionRequest,
    reason?: string,
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Suspend organization
      await this.organizationService.suspend(organizationId, reason);

      // Log successful action
      await this.logAdminAction({
        ...adminRequest,
        action: AdminAction.TENANT_SUSPEND,
        targetOrganizationId: organizationId,
        result: AdminActionResult.SUCCESS,
        durationMs: Date.now() - startTime,
        details: { reason },
      });

      this.logger.log(`Tenant ${organizationId} suspended by admin ${adminRequest.adminUserId}`);

    } catch (error) {
      // Log failed action
      await this.logAdminAction({
        ...adminRequest,
        action: AdminAction.TENANT_SUSPEND,
        targetOrganizationId: organizationId,
        result: AdminActionResult.FAILURE,
        durationMs: Date.now() - startTime,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  async reactivateTenant(
    organizationId: string,
    adminRequest: AdminActionRequest,
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Reactivate organization
      await this.organizationService.reactivate(organizationId);

      // Log successful action
      await this.logAdminAction({
        ...adminRequest,
        action: AdminAction.TENANT_REACTIVATE,
        targetOrganizationId: organizationId,
        result: AdminActionResult.SUCCESS,
        durationMs: Date.now() - startTime,
      });

      this.logger.log(`Tenant ${organizationId} reactivated by admin ${adminRequest.adminUserId}`);

    } catch (error) {
      // Log failed action
      await this.logAdminAction({
        ...adminRequest,
        action: AdminAction.TENANT_REACTIVATE,
        targetOrganizationId: organizationId,
        result: AdminActionResult.FAILURE,
        durationMs: Date.now() - startTime,
        errorMessage: error.message,
      });

      throw error;
    }
  }

  async createSystemAlert(
    title: string,
    description: string,
    severity: AlertSeverity,
    category: AlertCategory,
    organizationId?: string,
    metadata?: Record<string, any>,
  ): Promise<SystemAlert> {
    const alert = this.systemAlertRepository.create({
      title,
      description,
      severity,
      category,
      organizationId,
      metadata,
      firstOccurredAt: new Date(),
      lastOccurredAt: new Date(),
    });

    const savedAlert = await this.systemAlertRepository.save(alert);

    this.logger.warn(`System alert created: ${title} (${severity})`);

    return savedAlert;
  }

  async getActiveAlerts(
    organizationId?: string,
    severity?: AlertSeverity,
    category?: AlertCategory,
  ): Promise<SystemAlert[]> {
    const where: any = { status: AlertStatus.ACTIVE };
    
    if (organizationId) {
      where.organizationId = organizationId;
    }
    
    if (severity) {
      where.severity = severity;
    }
    
    if (category) {
      where.category = category;
    }

    return this.systemAlertRepository.find({
      where,
      order: { severity: 'DESC', createdAt: 'DESC' },
    });
  }

  async resolveAlert(
    alertId: string,
    resolvedBy: string,
    resolutionNotes?: string,
  ): Promise<SystemAlert> {
    const alert = await this.systemAlertRepository.findOne({
      where: { id: alertId },
    });

    if (!alert) {
      throw new NotFoundException('Alert not found');
    }

    alert.status = AlertStatus.RESOLVED;
    alert.resolvedAt = new Date();
    alert.resolvedBy = resolvedBy;
    alert.resolutionNotes = resolutionNotes;

    return this.systemAlertRepository.save(alert);
  }

  async getAuditLogs(
    page = 1,
    limit = 50,
    filters?: {
      adminUserId?: string;
      organizationId?: string;
      action?: AdminAction;
      startDate?: Date;
      endDate?: Date;
    },
  ): Promise<{ logs: AdminAuditLog[]; total: number; page: number; limit: number }> {
    const where: any = {};
    
    if (filters?.adminUserId) {
      where.adminUserId = filters.adminUserId;
    }
    
    if (filters?.organizationId) {
      where.targetOrganizationId = filters.organizationId;
    }
    
    if (filters?.action) {
      where.action = filters.action;
    }
    
    if (filters?.startDate && filters?.endDate) {
      where.timestamp = Between(filters.startDate, filters.endDate);
    }

    const [logs, total] = await this.auditLogRepository.findAndCount({
      where,
      order: { timestamp: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { logs, total, page, limit };
  }

  private async logAdminAction(request: AdminActionRequest & {
    result?: AdminActionResult;
    durationMs?: number;
    errorMessage?: string;
  }): Promise<AdminAuditLog> {
    const auditLog = this.auditLogRepository.create({
      adminUserId: request.adminUserId,
      adminUserEmail: request.adminUserEmail,
      action: request.action,
      result: request.result || AdminActionResult.SUCCESS,
      targetOrganizationId: request.targetOrganizationId,
      targetUserId: request.targetUserId,
      description: request.description,
      details: request.details,
      ipAddress: request.ipAddress,
      userAgent: request.userAgent,
      durationMs: request.durationMs,
      errorMessage: request.errorMessage,
      timestamp: new Date(),
    });

    return this.auditLogRepository.save(auditLog);
  }

  private async getLatestHealthCheck(organizationId: string): Promise<TenantHealthCheck | null> {
    return this.healthCheckRepository.findOne({
      where: { organizationId },
      order: { createdAt: 'DESC' },
    });
  }
}
