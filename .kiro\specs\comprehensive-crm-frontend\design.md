# Design Document

## Overview

The OneCRM frontend will be a modern, responsive web application built with Next.js 14, TypeScript, and shadcn/ui components. The design follows a dashboard-centric approach with modular layouts, comprehensive data management interfaces, and advanced visualization capabilities. The application will provide seamless integration with the backend API while maintaining excellent user experience across all device types.

## Architecture

### Technology Stack
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript (strict mode)
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: SWR for server state, Zustand for client state
- **Forms**: react-hook-form with Zod validation
- **Tables**: @tanstack/react-table
- **Charts**: Recharts
- **Authentication**: @react-keycloak/web
- **Icons**: Lucide React
- **Drag & Drop**: @dnd-kit/core

### Application Structure
```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
├── lib/                   # Utilities and configurations
├── hooks/                 # Custom React hooks
├── stores/                # Zustand stores
├── types/                 # TypeScript definitions
└── styles/                # Global styles
```

## Components and Interfaces

### 1. Layout System

#### Main Layout (`components/layout/MainLayout.tsx`)
- **Header**: Logo, global search, user menu, notifications
- **Sidebar**: Collapsible navigation with module icons and labels
- **Main Content**: Dynamic content area with breadcrumbs
- **Footer**: Status information and quick actions

#### Sidebar Navigation Structure
```
Dashboard
├── Overview
├── Analytics
├── Reports

Contacts
├── All Contacts
├── Contact Lists
├── Import/Export

Companies
├── All Companies
├── Company Hierarchy
├── Industry Analysis

Deals
├── Pipeline (Kanban)
├── All Deals (Table)
├── Forecasting
├── Won/Lost Analysis

Activities
├── My Tasks
├── Calendar View
├── All Activities
├── Overdue Items

Settings
├── Profile
├── Preferences
├── Team Management
```

### 2. Dashboard Components

#### Dashboard Overview (`components/dashboard/DashboardOverview.tsx`)
- **KPI Cards**: Total contacts, companies, deals, revenue with trend indicators
- **Quick Stats Grid**: 2x2 or 3x2 responsive grid layout
- **Recent Activities Feed**: Timeline-style activity list
- **Upcoming Tasks Widget**: Due dates with priority indicators

#### Analytics Charts (`components/dashboard/AnalyticsCharts.tsx`)
- **Sales Pipeline Chart**: Horizontal bar chart showing deal distribution by stage
- **Revenue Trend**: Line chart with monthly/quarterly revenue progression
- **Contact Sources**: Pie chart showing lead source distribution
- **Activity Heatmap**: Calendar-style heatmap for activity frequency

### 3. Contact Management Interface

#### Contact List View (`components/contacts/ContactListView.tsx`)
- **Data Table**: Sortable columns for name, email, company, status, assigned user
- **Filter Panel**: Collapsible sidebar with filters for status, source, tags, assigned user
- **Search Bar**: Real-time search with debouncing
- **Action Toolbar**: Bulk actions, export, import buttons
- **Pagination**: Page size selector and navigation controls

#### Contact Form (`components/contacts/ContactForm.tsx`)
```typescript
interface ContactFormData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  companyId?: string;
  leadStatus?: string;
  leadSource?: string;
  assignedToId?: string;
  tags?: string[];
  notes?: string;
  customFields?: Record<string, any>;
}
```

#### Contact Detail View (`components/contacts/ContactDetailView.tsx`)
- **Contact Header**: Name, title, company with edit/delete actions
- **Contact Information Panel**: All contact details in organized sections
- **Activity Timeline**: Chronological list of all associated activities
- **Related Deals**: Cards showing associated deals with quick actions
- **Notes Section**: Rich text editor for contact notes

### 4. Company Management Interface

#### Company List View (`components/companies/CompanyListView.tsx`)
- **Data Table**: Columns for name, industry, size, revenue, employee count
- **Advanced Filters**: Industry, size, revenue range, employee count range
- **Company Cards**: Alternative card view for visual browsing
- **Hierarchy View**: Tree structure showing parent-child relationships

#### Company Form (`components/companies/CompanyForm.tsx`)
```typescript
interface CompanyFormData {
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  description?: string;
  website?: string;
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  annualRevenue?: number;
  employeeCount?: number;
  parentCompanyId?: string;
  assignedToId?: string;
  tags?: string[];
}
```

### 5. Deal Pipeline Management

#### Kanban Board (`components/deals/DealKanbanBoard.tsx`)
- **Stage Columns**: Customizable pipeline stages with deal counts and totals
- **Deal Cards**: Compact cards showing title, amount, probability, close date
- **Drag & Drop**: Smooth transitions between stages with optimistic updates
- **Stage Actions**: Add deals, edit stage properties, view stage analytics

#### Deal Table View (`components/deals/DealTableView.tsx`)
- **Sortable Columns**: Title, amount, stage, probability, close date, owner
- **Advanced Filters**: Stage, amount range, probability range, close date range
- **Bulk Actions**: Stage updates, owner assignment, export

#### Deal Form (`components/deals/DealForm.tsx`)
```typescript
interface DealFormData {
  title: string;
  amount?: number;
  currency: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date;
  description?: string;
  source?: string;
  type?: string;
  priority?: string;
  contactId?: string;
  companyId?: string;
  ownerId?: string;
  customFields?: Record<string, any>;
}
```

### 6. Activity Management Interface

#### Activity List View (`components/activities/ActivityListView.tsx`)
- **Status Indicators**: Visual badges for pending, completed, overdue
- **Type Icons**: Different icons for calls, meetings, emails, tasks
- **Quick Actions**: Mark complete, reschedule, edit buttons
- **Time-based Grouping**: Today, tomorrow, this week, overdue sections

#### Activity Form (`components/activities/ActivityForm.tsx`)
```typescript
interface ActivityFormData {
  type: string;
  subject?: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: Date;
  startDate?: Date;
  endDate?: Date;
  duration?: number;
  location?: string;
  isAllDay: boolean;
  contactId?: string;
  companyId?: string;
  dealId?: string;
  assignedToId?: string;
}
```

### 7. Shared Components

#### Data Table (`components/ui/DataTable.tsx`)
- **Generic Implementation**: Reusable across all modules
- **Column Configuration**: Sortable, filterable, resizable columns
- **Row Selection**: Single and multi-select with bulk actions
- **Pagination**: Configurable page sizes and navigation
- **Loading States**: Skeleton loaders during data fetching

#### Filter Panel (`components/ui/FilterPanel.tsx`)
- **Dynamic Filters**: Configurable filter types (text, select, date, range)
- **Filter Chips**: Visual representation of active filters
- **Save/Load**: Ability to save and reuse filter combinations
- **Clear All**: Quick reset to default state

#### Search Component (`components/ui/GlobalSearch.tsx`)
- **Unified Search**: Search across all modules simultaneously
- **Search Results**: Categorized results with quick navigation
- **Recent Searches**: History of recent search terms
- **Keyboard Navigation**: Arrow keys and enter support

## Data Models

### Frontend Type Definitions

```typescript
// Core entity types matching backend
interface Contact {
  id: string;
  orgId: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyId?: string;
  title?: string;
  notes?: string;
  tags?: string[];
  customFields?: Record<string, any>;
  leadSource?: string;
  leadStatus?: string;
  assignedToId?: string;
  createdAt: Date;
  updatedAt: Date;
  // Relations
  assignedTo?: User;
  company?: Company;
}

interface Company {
  id: string;
  orgId: string;
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  description?: string;
  website?: string;
  phone?: string;
  address?: Address;
  tags?: string[];
  customFields?: Record<string, any>;
  annualRevenue?: number;
  employeeCount?: number;
  parentCompanyId?: string;
  assignedToId?: string;
  createdAt: Date;
  updatedAt: Date;
  // Relations
  assignedTo?: User;
  parentCompany?: Company;
  subsidiaries?: Company[];
  contacts?: Contact[];
  deals?: Deal[];
}

interface Deal {
  id: string;
  orgId: string;
  title: string;
  amount?: number;
  currency: string;
  stage?: string;
  probability?: number;
  expectedCloseDate?: Date;
  description?: string;
  source?: string;
  type?: string;
  priority?: string;
  customFields?: Record<string, any>;
  contactId?: string;
  companyId?: string;
  ownerId?: string;
  createdAt: Date;
  updatedAt: Date;
  // Relations
  contact?: Contact;
  company?: Company;
  owner?: User;
}

interface Activity {
  id: string;
  orgId: string;
  type: string;
  subject?: string;
  description?: string;
  status: string;
  priority: string;
  dueDate?: Date;
  startDate?: Date;
  endDate?: Date;
  duration?: number;
  location?: string;
  isAllDay: boolean;
  isCompleted: boolean;
  customFields?: Record<string, any>;
  contactId?: string;
  companyId?: string;
  dealId?: string;
  assignedToId?: string;
  createdAt: Date;
  updatedAt: Date;
  // Relations
  contact?: Contact;
  company?: Company;
  deal?: Deal;
  assignedTo?: User;
}
```

### API Response Types

```typescript
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  details?: any;
}
```

## Error Handling

### Error Boundary Implementation
- **Global Error Boundary**: Catches unhandled React errors
- **Route-level Boundaries**: Specific error handling for each major section
- **Component-level Boundaries**: Granular error isolation for complex components

### Error States
- **Network Errors**: Retry mechanisms with exponential backoff
- **Validation Errors**: Field-level error display with clear messaging
- **Permission Errors**: Graceful degradation with appropriate messaging
- **Not Found Errors**: Helpful navigation suggestions

### Error UI Components
```typescript
interface ErrorDisplayProps {
  error: ApiError;
  onRetry?: () => void;
  showDetails?: boolean;
}

interface LoadingStateProps {
  type: 'skeleton' | 'spinner' | 'progress';
  message?: string;
}
```

## Testing Strategy

### Unit Testing
- **Component Testing**: React Testing Library for all UI components
- **Hook Testing**: Custom hooks with comprehensive test coverage
- **Utility Testing**: Pure functions and helper utilities
- **Form Validation**: Zod schema validation testing

### Integration Testing
- **API Integration**: Mock API responses for consistent testing
- **User Flows**: Complete user journey testing
- **Cross-component**: Component interaction testing

### E2E Testing
- **Critical Paths**: Authentication, CRUD operations, navigation
- **Browser Testing**: Chrome, Firefox, Safari compatibility
- **Mobile Testing**: Responsive design validation

### Performance Testing
- **Bundle Analysis**: Code splitting and lazy loading verification
- **Rendering Performance**: Component render time optimization
- **Memory Usage**: Memory leak detection and prevention

## Accessibility

### WCAG 2.1 AA Compliance
- **Keyboard Navigation**: Full keyboard accessibility for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Minimum 4.5:1 contrast ratio for all text
- **Focus Management**: Clear focus indicators and logical tab order

### Accessibility Features
- **High Contrast Mode**: Alternative color schemes for better visibility
- **Font Size Controls**: User-configurable text sizing
- **Motion Preferences**: Respect for reduced motion preferences
- **Alternative Text**: Comprehensive alt text for all images and icons

## Performance Optimization

### Code Splitting
- **Route-based Splitting**: Lazy loading for each major section
- **Component-based Splitting**: Dynamic imports for heavy components
- **Library Splitting**: Separate chunks for large dependencies

### Caching Strategy
- **SWR Configuration**: Optimized cache policies for different data types
- **Browser Caching**: Proper cache headers for static assets
- **Service Worker**: Offline capability for critical functionality

### Bundle Optimization
- **Tree Shaking**: Eliminate unused code from final bundle
- **Compression**: Gzip/Brotli compression for all assets
- **Image Optimization**: Next.js Image component with proper sizing

## Security Considerations

### Authentication & Authorization
- **Token Management**: Secure storage and automatic refresh
- **Route Protection**: Role-based access control for all routes
- **API Security**: Proper headers and CSRF protection

### Data Protection
- **Input Sanitization**: XSS prevention for all user inputs
- **Data Validation**: Client and server-side validation
- **Sensitive Data**: Proper handling of PII and business data

### Content Security Policy
- **CSP Headers**: Strict content security policies
- **HTTPS Only**: Force HTTPS for all communications
- **Secure Cookies**: Proper cookie configuration for sessions