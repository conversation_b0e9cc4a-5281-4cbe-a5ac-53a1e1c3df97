#!/bin/bash

# OneCRM Comprehensive Test Suite Runner
# This script runs all tests across the entire OneCRM platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
BACKEND_UNIT_RESULT=0
BACKEND_INTEGRATION_RESULT=0
FRONTEND_UNIT_RESULT=0
FRONTEND_E2E_RESULT=0
SECURITY_TEST_RESULT=0
PERFORMANCE_TEST_RESULT=0

echo -e "${BLUE}🚀 OneCRM Comprehensive Test Suite${NC}"
echo "=================================================="

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to print test results
print_result() {
    if [ $2 -eq 0 ]; then
        echo -e "${GREEN}✅ $1 PASSED${NC}"
    else
        echo -e "${RED}❌ $1 FAILED${NC}"
    fi
}

# Check if required tools are installed
check_dependencies() {
    print_section "🔍 Checking Dependencies"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Node.js $(node --version)${NC}"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ npm $(npm --version)${NC}"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        echo -e "${YELLOW}⚠️  Docker is not installed (required for integration tests)${NC}"
    else
        echo -e "${GREEN}✅ Docker $(docker --version | cut -d' ' -f3 | cut -d',' -f1)${NC}"
    fi
    
    # Check Playwright
    if [ -f "frontend/node_modules/.bin/playwright" ]; then
        echo -e "${GREEN}✅ Playwright installed${NC}"
    else
        echo -e "${YELLOW}⚠️  Playwright not found (required for E2E tests)${NC}"
    fi
}

# Setup test environment
setup_test_env() {
    print_section "🛠️  Setting Up Test Environment"
    
    # Create test results directory
    mkdir -p test-results
    
    # Set test environment variables
    export NODE_ENV=test
    export DATABASE_URL="postgresql://test:test@localhost:5432/onecrm_test"
    export KEYCLOAK_URL="http://localhost:8080"
    export KEYCLOAK_REALM="onecrm-test"
    export KEYCLOAK_CLIENT_ID="onecrm-test"
    
    echo -e "${GREEN}✅ Test environment configured${NC}"
}

# Run backend unit tests
run_backend_unit_tests() {
    print_section "🧪 Backend Unit Tests"
    
    cd backend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "Installing backend dependencies..."
        npm install
    fi
    
    # Run unit tests
    echo "Running backend unit tests..."
    if npm run test:unit; then
        BACKEND_UNIT_RESULT=0
        echo -e "${GREEN}✅ Backend unit tests passed${NC}"
    else
        BACKEND_UNIT_RESULT=1
        echo -e "${RED}❌ Backend unit tests failed${NC}"
    fi
    
    cd ..
}

# Run backend integration tests
run_backend_integration_tests() {
    print_section "🔗 Backend Integration Tests"
    
    cd backend
    
    # Start test database
    echo "Starting test database..."
    docker-compose -f docker-compose.test.yml up -d postgres
    
    # Wait for database to be ready
    echo "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    echo "Running database migrations..."
    npm run migration:run
    
    # Run integration tests
    echo "Running backend integration tests..."
    if npm run test:integration; then
        BACKEND_INTEGRATION_RESULT=0
        echo -e "${GREEN}✅ Backend integration tests passed${NC}"
    else
        BACKEND_INTEGRATION_RESULT=1
        echo -e "${RED}❌ Backend integration tests failed${NC}"
    fi
    
    # Cleanup
    echo "Cleaning up test database..."
    docker-compose -f docker-compose.test.yml down
    
    cd ..
}

# Run frontend unit tests
run_frontend_unit_tests() {
    print_section "⚛️  Frontend Unit Tests"
    
    cd frontend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "Installing frontend dependencies..."
        npm install
    fi
    
    # Run unit tests
    echo "Running frontend unit tests..."
    if npm run test -- --coverage --watchAll=false; then
        FRONTEND_UNIT_RESULT=0
        echo -e "${GREEN}✅ Frontend unit tests passed${NC}"
    else
        FRONTEND_UNIT_RESULT=1
        echo -e "${RED}❌ Frontend unit tests failed${NC}"
    fi
    
    cd ..
}

# Run end-to-end tests
run_e2e_tests() {
    print_section "🎭 End-to-End Tests"
    
    cd frontend
    
    # Install Playwright if needed
    if [ ! -f "node_modules/.bin/playwright" ]; then
        echo "Installing Playwright..."
        npm install @playwright/test
        npx playwright install
    fi
    
    # Start the application
    echo "Starting application for E2E tests..."
    npm run build
    npm run start &
    APP_PID=$!
    
    # Wait for application to start
    echo "Waiting for application to start..."
    sleep 30
    
    # Run E2E tests
    echo "Running E2E tests..."
    if npx playwright test; then
        FRONTEND_E2E_RESULT=0
        echo -e "${GREEN}✅ E2E tests passed${NC}"
    else
        FRONTEND_E2E_RESULT=1
        echo -e "${RED}❌ E2E tests failed${NC}"
    fi
    
    # Stop the application
    kill $APP_PID
    
    cd ..
}

# Run security tests
run_security_tests() {
    print_section "🔒 Security Tests"
    
    cd backend
    
    # Run security tests
    echo "Running security tests..."
    if npm run test:security; then
        SECURITY_TEST_RESULT=0
        echo -e "${GREEN}✅ Security tests passed${NC}"
    else
        SECURITY_TEST_RESULT=1
        echo -e "${RED}❌ Security tests failed${NC}"
    fi
    
    cd ..
}

# Run performance tests
run_performance_tests() {
    print_section "⚡ Performance Tests"
    
    # Start the application
    echo "Starting application for performance tests..."
    cd backend
    npm run start:prod &
    BACKEND_PID=$!
    cd ..
    
    cd frontend
    npm run start &
    FRONTEND_PID=$!
    cd ..
    
    # Wait for applications to start
    sleep 30
    
    # Run performance tests (using Artillery or similar)
    echo "Running performance tests..."
    if command -v artillery &> /dev/null; then
        if artillery run performance-tests/load-test.yml; then
            PERFORMANCE_TEST_RESULT=0
            echo -e "${GREEN}✅ Performance tests passed${NC}"
        else
            PERFORMANCE_TEST_RESULT=1
            echo -e "${RED}❌ Performance tests failed${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Artillery not installed, skipping performance tests${NC}"
        PERFORMANCE_TEST_RESULT=0
    fi
    
    # Stop applications
    kill $BACKEND_PID $FRONTEND_PID
}

# Generate test report
generate_report() {
    print_section "📊 Test Results Summary"
    
    echo "Test Suite Results:"
    echo "==================="
    print_result "Backend Unit Tests" $BACKEND_UNIT_RESULT
    print_result "Backend Integration Tests" $BACKEND_INTEGRATION_RESULT
    print_result "Frontend Unit Tests" $FRONTEND_UNIT_RESULT
    print_result "End-to-End Tests" $FRONTEND_E2E_RESULT
    print_result "Security Tests" $SECURITY_TEST_RESULT
    print_result "Performance Tests" $PERFORMANCE_TEST_RESULT
    
    # Calculate overall result
    TOTAL_FAILURES=$((BACKEND_UNIT_RESULT + BACKEND_INTEGRATION_RESULT + FRONTEND_UNIT_RESULT + FRONTEND_E2E_RESULT + SECURITY_TEST_RESULT + PERFORMANCE_TEST_RESULT))
    
    echo ""
    if [ $TOTAL_FAILURES -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL TESTS PASSED! OneCRM is ready for production.${NC}"
        exit 0
    else
        echo -e "${RED}💥 $TOTAL_FAILURES test suite(s) failed. Please review and fix issues.${NC}"
        exit 1
    fi
}

# Main execution
main() {
    echo -e "${BLUE}Starting OneCRM comprehensive test suite...${NC}"
    
    check_dependencies
    setup_test_env
    
    # Run tests based on arguments or run all
    if [ $# -eq 0 ]; then
        # Run all tests
        run_backend_unit_tests
        run_backend_integration_tests
        run_frontend_unit_tests
        run_e2e_tests
        run_security_tests
        run_performance_tests
    else
        # Run specific test suites
        for arg in "$@"; do
            case $arg in
                "backend-unit")
                    run_backend_unit_tests
                    ;;
                "backend-integration")
                    run_backend_integration_tests
                    ;;
                "frontend-unit")
                    run_frontend_unit_tests
                    ;;
                "e2e")
                    run_e2e_tests
                    ;;
                "security")
                    run_security_tests
                    ;;
                "performance")
                    run_performance_tests
                    ;;
                *)
                    echo -e "${RED}Unknown test suite: $arg${NC}"
                    echo "Available options: backend-unit, backend-integration, frontend-unit, e2e, security, performance"
                    exit 1
                    ;;
            esac
        done
    fi
    
    generate_report
}

# Run main function with all arguments
main "$@"
