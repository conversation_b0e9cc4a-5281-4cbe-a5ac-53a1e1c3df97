// Kong Gateway utilities and health checks

interface KongGatewayStatus {
  isHealthy: boolean;
  latency?: number;
  services: {
    crmApi: boolean;
    authService: boolean;
    tenantService: boolean;
  };
  error?: string;
}

/**
 * Check Kong Gateway health and service availability
 */
export const checkKongGatewayHealth = async (): Promise<KongGatewayStatus> => {
  const kongUrl = process.env.NEXT_PUBLIC_KONG_GATEWAY_URL || 'http://localhost:8000';
  const startTime = Date.now();
  
  try {
    // Check Kong Gateway status endpoint
    const response = await fetch(`${kongUrl}/status`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    });
    
    const latency = Date.now() - startTime;
    
    if (!response.ok) {
      return {
        isHealthy: false,
        latency,
        services: {
          crmApi: false,
          authService: false,
          tenantService: false,
        },
        error: `Kong Gateway returned ${response.status}`,
      };
    }
    
    // Check individual services through Kong
    const serviceChecks = await Promise.allSettled([
      checkServiceHealth(`${kongUrl}/api/health`), // CRM API
      checkServiceHealth(`${kongUrl}/auth/health`), // Auth Service
      checkServiceHealth(`${kongUrl}/tenants/health`), // Tenant Service
    ]);
    
    return {
      isHealthy: true,
      latency,
      services: {
        crmApi: serviceChecks[0].status === 'fulfilled',
        authService: serviceChecks[1].status === 'fulfilled',
        tenantService: serviceChecks[2].status === 'fulfilled',
      },
    };
    
  } catch (error: any) {
    return {
      isHealthy: false,
      latency: Date.now() - startTime,
      services: {
        crmApi: false,
        authService: false,
        tenantService: false,
      },
      error: error.message || 'Kong Gateway unreachable',
    };
  }
};

/**
 * Check individual service health through Kong Gateway
 */
const checkServiceHealth = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, {
      method: 'GET',
      signal: AbortSignal.timeout(3000), // 3 second timeout
    });
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Get Kong Gateway configuration info
 */
export const getKongGatewayInfo = () => {
  return {
    gatewayUrl: process.env.NEXT_PUBLIC_KONG_GATEWAY_URL || 'http://localhost:8000',
    isProduction: process.env.NODE_ENV === 'production',
    version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    services: {
      crmApi: '/api',
      authService: '/auth',
      tenantService: '/tenants',
    },
  };
};

/**
 * Kong Gateway middleware for development debugging
 */
export const logKongGatewayRequest = (url: string, method: string) => {
  if (process.env.NODE_ENV === 'development') {
    const kongInfo = getKongGatewayInfo();
    console.group(`🌉 Kong Gateway Request`);
    console.log(`Method: ${method.toUpperCase()}`);
    console.log(`URL: ${url}`);
    console.log(`Gateway: ${kongInfo.gatewayUrl}`);
    console.log(`Timestamp: ${new Date().toISOString()}`);
    console.groupEnd();
  }
};

/**
 * Extract Kong-specific headers from response
 */
export const extractKongHeaders = (headers: Headers | Record<string, string>) => {
  const kongHeaders: Record<string, string> = {};
  
  const headerNames = [
    'x-kong-upstream-latency',
    'x-kong-proxy-latency',
    'x-kong-request-id',
    'x-kong-upstream-status',
    'x-ratelimit-limit-minute',
    'x-ratelimit-remaining-minute',
  ];
  
  headerNames.forEach(name => {
    const value = headers instanceof Headers 
      ? headers.get(name) 
      : headers[name] || headers[name.toLowerCase()];
    
    if (value) {
      kongHeaders[name] = value;
    }
  });
  
  return kongHeaders;
};
