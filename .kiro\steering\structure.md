# Project Structure & Organization

OneCRM follows a strict Nx monorepo structure with clear separation of concerns and no file duplication.

## Root Directory Structure

```
onecrm/
├── .github/workflows/          # CI/CD pipelines and GitHub Actions
├── .kiro/steering/            # Kiro AI assistant steering rules
├── .nx/                       # Nx cache and workspace metadata
├── backend/                   # Backend microservices
├── frontend/                  # Next.js frontend application
├── libs/                      # Shared libraries and utilities
├── scripts/                   # Automation scripts and utilities
├── docs/                      # Project documentation
├── kong/config/               # Kong Gateway configuration
├── docker/                    # Docker configurations per service
├── infrastructure/            # Kubernetes, Terraform, monitoring
├── keycloak/                  # Keycloak realm configurations
├── package.json               # Root workspace configuration
├── nx.json                    # Nx workspace settings
└── tsconfig.base.json         # Shared TypeScript configuration
```

## Backend Structure (`/backend/`)

```
backend/
├── crm-api/                   # Main CRM API service (NestJS)
│   ├── src/
│   │   ├── modules/           # Feature modules (contacts, deals, etc.)
│   │   ├── common/            # Shared services, guards, interceptors
│   │   ├── config/            # Configuration files
│   │   ├── main.ts            # Application entry point
│   │   └── app.module.ts      # Root module
│   ├── test/                  # Integration and E2E tests
│   ├── package.json           # Service-specific dependencies
│   └── nest-cli.json          # NestJS CLI configuration
├── auth-service/              # Authentication service (planned)
└── tenant-service/            # Tenant management service (planned)
```

## Frontend Structure (`/frontend/`)

```
frontend/
├── src/
│   ├── app/                   # Next.js App Router pages
│   │   ├── dashboard/         # Dashboard pages
│   │   ├── contacts/          # Contact management pages
│   │   ├── companies/         # Company management pages
│   │   ├── deals/             # Deal pipeline pages
│   │   ├── api/               # API routes (if needed)
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── auth/              # Authentication components
│   │   ├── common/            # Shared UI components
│   │   ├── contacts/          # Contact-specific components
│   │   ├── companies/         # Company-specific components
│   │   ├── deals/             # Deal-specific components
│   │   ├── dashboard/         # Dashboard components
│   │   ├── layout/            # Layout components
│   │   └── providers/         # Context providers
│   ├── lib/                   # Utility libraries
│   ├── hooks/                 # Custom React hooks
│   ├── types/                 # TypeScript type definitions
│   └── utils/                 # Utility functions
├── public/                    # Static assets
├── tests/                     # Test files
├── package.json               # Frontend dependencies
└── next.config.js             # Next.js configuration
```

## Shared Libraries (`/libs/`)

```
libs/
├── shared/                    # Shared React components and hooks
├── types/                     # Shared TypeScript types
└── utils/                     # Shared utility functions
```

## Scripts Directory (`/scripts/`)

```
scripts/
├── db/                        # Database scripts and migrations
├── setup.sh                  # Development environment setup
├── dev-start.sh               # Start development services
├── deploy.sh                  # Deployment scripts
└── test-*.sh                  # Testing scripts
```

## Configuration Files

- **Root Level**: `package.json`, `nx.json`, `tsconfig.base.json`, `.eslintrc.json`, `.prettierrc`
- **Environment**: `.env`, `.env.development`, `.env.production`
- **Docker**: `docker-compose.dev.yml`, service-specific Dockerfiles in `/docker/`
- **Infrastructure**: Kubernetes manifests in `/infrastructure/kubernetes/`

## Module Organization Principles

### Backend Modules
- Each feature is a self-contained NestJS module
- Modules include: controller, service, entities, DTOs, and tests
- Common functionality is shared via the `common/` directory
- Multi-tenant isolation is enforced at the module level

### Frontend Components
- Components are organized by feature domain
- Shared components go in `components/common/`
- Each component directory includes: component, styles, tests, and types
- Use barrel exports (`index.ts`) for clean imports

### Shared Libraries
- Types are shared between frontend and backend via `libs/types/`
- Utility functions are shared via `libs/utils/`
- React components and hooks are shared via `libs/shared/`

## File Naming Conventions

- **Components**: PascalCase (e.g., `ContactForm.tsx`)
- **Files**: kebab-case (e.g., `contact-form.service.ts`)
- **Directories**: kebab-case (e.g., `contact-management/`)
- **Types**: PascalCase interfaces (e.g., `ContactEntity.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

## Import Path Conventions

```typescript
// Absolute imports using path mapping
import { ContactService } from '@/modules/contacts/contact.service';
import { Button } from '@/components/common/Button';
import { ContactType } from '@/types/contact.types';

// Relative imports only for same-directory files
import './component.styles.css';
```

## Anti-Patterns to Avoid

- **No File Duplication**: Refactor or replace existing files instead of duplicating
- **No Mixed Concerns**: Keep frontend and backend strictly separated
- **No Direct Database Access**: All data access goes through the API layer
- **No Hardcoded Values**: Use environment variables and configuration files
- **No Circular Dependencies**: Maintain clear dependency hierarchy

## Development Workflow

1. **Feature Development**: Create feature branch, implement in appropriate module
2. **Testing**: Write tests before implementation (TDD approach)
3. **Code Review**: All changes require PR review and passing tests
4. **Integration**: Ensure changes work across frontend and backend
5. **Documentation**: Update relevant docs when adding new features