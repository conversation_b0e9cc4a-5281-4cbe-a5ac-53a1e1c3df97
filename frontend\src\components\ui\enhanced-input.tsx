'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { Input } from './input'
import { Label } from './label'
import { Button } from './button'
import { Eye, EyeOff, Search, X, Copy, Check, AlertCircle } from 'lucide-react'

const enhancedInputVariants = cva(
  'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: '',
        filled: 'bg-muted/50 border-0 focus-visible:bg-background',
        underlined: 'border-0 border-b-2 rounded-none px-0 focus-visible:ring-0 focus-visible:border-primary',
      },
      inputSize: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-9 px-3 text-sm',
        lg: 'h-10 px-4 text-base',
      },
      state: {
        default: '',
        error: 'border-destructive focus-visible:ring-destructive',
        success: 'border-green-500 focus-visible:ring-green-500',
        warning: 'border-yellow-500 focus-visible:ring-yellow-500',
      },
    },
    defaultVariants: {
      variant: 'default',
      inputSize: 'default',
      state: 'default',
    },
  }
)

export interface EnhancedInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof enhancedInputVariants> {
  label?: string
  description?: string
  error?: string
  success?: string
  warning?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  leftAddon?: React.ReactNode
  rightAddon?: React.ReactNode
  clearable?: boolean
  copyable?: boolean
  loading?: boolean
  onClear?: () => void
  onCopy?: () => void
  containerClassName?: string
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  (
    {
      className,
      containerClassName,
      variant,
      inputSize,
      state,
      type,
      label,
      description,
      error,
      success,
      warning,
      leftIcon,
      rightIcon,
      leftAddon,
      rightAddon,
      clearable = false,
      copyable = false,
      loading = false,
      onClear,
      onCopy,
      value,
      required,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false)
    const [copied, setCopied] = React.useState(false)
    const fieldId = React.useId()

    // Determine the actual state based on props
    const actualState = error ? 'error' : success ? 'success' : warning ? 'warning' : state

    const handleClear = () => {
      onClear?.()
    }

    const handleCopy = async () => {
      if (value && typeof value === 'string') {
        try {
          await navigator.clipboard.writeText(value)
          setCopied(true)
          onCopy?.()
          setTimeout(() => setCopied(false), 2000)
        } catch (err) {
          console.error('Failed to copy text:', err)
        }
      }
    }

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword)
    }

    const inputType = type === 'password' && showPassword ? 'text' : type

    const hasValue = value !== undefined && value !== ''
    const showClearButton = clearable && hasValue && !props.disabled
    const showCopyButton = copyable && hasValue && !props.disabled
    const showPasswordToggle = type === 'password'

    return (
      <div className={cn('space-y-2', containerClassName)}>
        {label && (
          <Label 
            htmlFor={fieldId} 
            className={cn(required && 'after:content-["*"] after:ml-0.5 after:text-destructive')}
          >
            {label}
          </Label>
        )}

        <div className="relative">
          {leftAddon && (
            <div className="absolute left-0 top-0 bottom-0 flex items-center px-3 bg-muted border border-r-0 rounded-l-md">
              {leftAddon}
            </div>
          )}

          {leftIcon && !leftAddon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}

          <Input
            id={fieldId}
            ref={ref}
            type={inputType}
            className={cn(
              enhancedInputVariants({ variant, inputSize, state: actualState }),
              leftIcon && !leftAddon && 'pl-10',
              leftAddon && 'pl-12',
              (rightIcon || showClearButton || showCopyButton || showPasswordToggle || rightAddon) && 'pr-10',
              rightAddon && 'pr-12',
              className
            )}
            value={value}
            required={required}
            {...props}
          />

          <div className="absolute right-0 top-0 bottom-0 flex items-center">
            {showClearButton && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-full px-2 hover:bg-transparent"
                onClick={handleClear}
              >
                <X className="h-4 w-4 text-muted-foreground hover:text-foreground" />
              </Button>
            )}

            {showCopyButton && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-full px-2 hover:bg-transparent"
                onClick={handleCopy}
              >
                {copied ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                )}
              </Button>
            )}

            {showPasswordToggle && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-full px-2 hover:bg-transparent"
                onClick={togglePasswordVisibility}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                )}
              </Button>
            )}

            {rightIcon && !showClearButton && !showCopyButton && !showPasswordToggle && !rightAddon && (
              <div className="px-3 text-muted-foreground">
                {rightIcon}
              </div>
            )}

            {rightAddon && (
              <div className="flex items-center px-3 bg-muted border border-l-0 rounded-r-md h-full">
                {rightAddon}
              </div>
            )}
          </div>

          {loading && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
          )}
        </div>

        {(description || error || success || warning) && (
          <div className="space-y-1">
            {description && !error && !success && !warning && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
            
            {error && (
              <p className="text-sm text-destructive flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {error}
              </p>
            )}
            
            {success && !error && (
              <p className="text-sm text-green-600 flex items-center gap-1">
                <Check className="h-3 w-3" />
                {success}
              </p>
            )}
            
            {warning && !error && !success && (
              <p className="text-sm text-yellow-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                {warning}
              </p>
            )}
          </div>
        )}
      </div>
    )
  }
)

EnhancedInput.displayName = 'EnhancedInput'

// Search Input Component
export interface SearchInputProps extends Omit<EnhancedInputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void
  searchDelay?: number
}

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, searchDelay = 300, ...props }, ref) => {
    const [searchValue, setSearchValue] = React.useState(props.value || '')
    const timeoutRef = React.useRef<NodeJS.Timeout>()

    React.useEffect(() => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        onSearch?.(searchValue as string)
      }, searchDelay)

      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
      }
    }, [searchValue, onSearch, searchDelay])

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchValue(e.target.value)
      props.onChange?.(e)
    }

    return (
      <EnhancedInput
        ref={ref}
        type="search"
        leftIcon={<Search className="h-4 w-4" />}
        clearable
        {...props}
        value={searchValue}
        onChange={handleChange}
        onClear={() => {
          setSearchValue('')
          props.onClear?.()
        }}
      />
    )
  }
)

SearchInput.displayName = 'SearchInput'

// Number Input Component
export interface NumberInputProps extends Omit<EnhancedInputProps, 'type'> {
  min?: number
  max?: number
  step?: number
  precision?: number
  onValueChange?: (value: number | undefined) => void
}

export const NumberInput = React.forwardRef<HTMLInputElement, NumberInputProps>(
  ({ min, max, step = 1, precision = 0, onValueChange, ...props }, ref) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      
      if (value === '') {
        onValueChange?.(undefined)
      } else {
        const numValue = parseFloat(value)
        if (!isNaN(numValue)) {
          const clampedValue = Math.min(Math.max(numValue, min || -Infinity), max || Infinity)
          const preciseValue = precision > 0 ? 
            parseFloat(clampedValue.toFixed(precision)) : 
            Math.round(clampedValue)
          onValueChange?.(preciseValue)
        }
      }
      
      props.onChange?.(e)
    }

    return (
      <EnhancedInput
        ref={ref}
        type="number"
        min={min}
        max={max}
        step={step}
        {...props}
        onChange={handleChange}
      />
    )
  }
)

NumberInput.displayName = 'NumberInput'

export { EnhancedInput, enhancedInputVariants }
