import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

export enum HttpMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
  OPTIONS = 'OPTIONS',
  HEAD = 'HEAD',
}

@Entity('api_usage_logs')
@Index(['organizationId', 'timestamp'])
@Index(['organizationId', 'endpoint', 'timestamp'])
@Index(['organizationId', 'userId', 'timestamp'])
export class ApiUsageLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  @Column({
    type: 'enum',
    enum: HttpMethod,
  })
  method: HttpMethod;

  @Column({ type: 'varchar', length: 500 })
  endpoint: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  route: string;

  @Column({ type: 'int' })
  statusCode: number;

  @Column({ type: 'int', nullable: true })
  responseTime: number; // in milliseconds

  @Column({ type: 'bigint', nullable: true })
  requestSize: number; // in bytes

  @Column({ type: 'bigint', nullable: true })
  responseSize: number; // in bytes

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress: string;

  @Column({ type: 'text', nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  referer: string;

  @Column({ type: 'json', nullable: true })
  queryParams: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  requestHeaders: Record<string, string>;

  @Column({ type: 'json', nullable: true })
  responseHeaders: Record<string, string>;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  apiKey: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  sessionId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  requestId: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    rateLimitRemaining?: number;
    rateLimitReset?: number;
    cacheHit?: boolean;
    processingTime?: number;
    databaseQueries?: number;
    additionalData?: Record<string, any>;
  };

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  timestamp: Date;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Computed properties
  get isSuccess(): boolean {
    return this.statusCode >= 200 && this.statusCode < 300;
  }

  get isClientError(): boolean {
    return this.statusCode >= 400 && this.statusCode < 500;
  }

  get isServerError(): boolean {
    return this.statusCode >= 500;
  }

  get isSlowRequest(): boolean {
    return this.responseTime && this.responseTime > 1000; // > 1 second
  }
}
