// UI Component Types for OneCRM
import { ReactNode } from 'react'

// Base component props
export interface BaseComponentProps {
  className?: string
  children?: ReactNode
}

// Layout types
export interface LayoutProps extends BaseComponentProps {
  sidebar?: ReactNode
  header?: ReactNode
  footer?: ReactNode
}

// Navigation types
export interface NavigationItem {
  id: string
  label: string
  href: string
  icon?: ReactNode
  badge?: string | number
  children?: NavigationItem[]
}

export interface BreadcrumbItem {
  label: string
  href?: string
}

// Data display types
export interface TableColumn<T = unknown> {
  id: string
  header: string
  accessorKey?: keyof T
  cell?: (value: T) => ReactNode
  sortable?: boolean
  filterable?: boolean
  width?: string | number
}

export interface TableProps<T = unknown> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  pagination?: {
    page: number
    pageSize: number
    total: number
    onPageChange: (page: number) => void
    onPageSizeChange: (pageSize: number) => void
  }
  selection?: {
    selectedRows: string[]
    onSelectionChange: (selectedRows: string[]) => void
  }
  sorting?: {
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
    onSortChange: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  }
  filtering?: {
    filters: Record<string, unknown>
    onFilterChange: (filters: Record<string, unknown>) => void
  }
}

// Form types
export interface FormFieldProps {
  name: string
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  error?: string
  description?: string
}

export interface SelectOption {
  value: string
  label: string
  disabled?: boolean
}

// Card types
export interface CardProps extends BaseComponentProps {
  title?: string
  description?: string
  actions?: ReactNode
  footer?: ReactNode
  variant?: 'default' | 'outline' | 'ghost'
}

// Modal types
export interface ModalProps extends BaseComponentProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  description?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

// Search types
export interface SearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  suggestions?: SearchSuggestion[]
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void
  loading?: boolean
}

export interface SearchSuggestion {
  id: string
  label: string
  type: 'contact' | 'company' | 'deal' | 'action'
  icon?: ReactNode
  description?: string
}

// Filter types
export interface FilterOption {
  id: string
  label: string
  type: 'text' | 'select' | 'date' | 'number' | 'boolean'
  options?: SelectOption[]
  value?: unknown
}

export interface FilterProps {
  filters: FilterOption[]
  values: Record<string, unknown>
  onChange: (values: Record<string, unknown>) => void
  onReset: () => void
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system'

export interface ThemeContextType {
  mode: ThemeMode
  setMode: (mode: ThemeMode) => void
  isDark: boolean
}

// Status types
export type StatusVariant = 'default' | 'success' | 'warning' | 'error' | 'info'

// Loading states
export interface LoadingState {
  loading: boolean
  error?: string | null
  data?: unknown
}

// Responsive breakpoints
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

// Animation types
export type AnimationType = 'fade' | 'slide' | 'scale' | 'bounce' | 'none'

// Accessibility types
export interface AccessibilityProps {
  'aria-label'?: string
  'aria-labelledby'?: string
  'aria-describedby'?: string
  'aria-expanded'?: boolean
  'aria-selected'?: boolean
  'aria-disabled'?: boolean
  role?: string
  tabIndex?: number
}

// Event handler types
export type ClickHandler = (event: React.MouseEvent) => void
export type KeyboardHandler = (event: React.KeyboardEvent) => void
export type ChangeHandler<T = string> = (value: T) => void

// Generic component props
export interface ComponentWithVariants {
  variant?: string
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
}

// Error boundary types
export interface ErrorInfo {
  componentStack: string
}

export interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

// Performance types
export interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  interactionTime: number
}

// Virtualization types
export interface VirtualizedListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => ReactNode
  overscan?: number
}
