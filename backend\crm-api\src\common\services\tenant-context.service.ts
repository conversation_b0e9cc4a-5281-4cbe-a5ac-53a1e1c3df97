import { Injectable, Lo<PERSON>, Scope } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

export interface TenantContext {
  userId: string;
  orgId: string;
  role: string;
  email?: string;
  isAdmin: boolean;
}

@Injectable({ scope: Scope.REQUEST })
export class TenantContextService {
  private readonly logger = new Logger(TenantContextService.name);
  private context: TenantContext | null = null;

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
  ) {}

  /**
   * Set the tenant context for the current request
   */
  async setContext(context: TenantContext): Promise<void> {
    this.context = context;
    
    // Set PostgreSQL session variables for RLS
    await this.setDatabaseContext(context);
    
    this.logger.debug('Tenant context set', {
      userId: context.userId,
      orgId: context.orgId,
      role: context.role,
    });
  }

  /**
   * Get the current tenant context
   */
  getContext(): TenantContext | null {
    return this.context;
  }

  /**
   * Get the current organization ID
   */
  getOrgId(): string {
    if (!this.context) {
      throw new Error('Tenant context not set');
    }
    return this.context.orgId;
  }

  /**
   * Get the current user ID
   */
  getUserId(): string {
    if (!this.context) {
      throw new Error('Tenant context not set');
    }
    return this.context.userId;
  }

  /**
   * Check if current user is admin
   */
  isAdmin(): boolean {
    return this.context?.isAdmin || false;
  }

  /**
   * Check if current user has specific role
   */
  hasRole(role: string): boolean {
    if (!this.context) {
      return false;
    }
    
    // Admin has all roles
    if (this.context.isAdmin) {
      return true;
    }
    
    return this.context.role === role;
  }

  /**
   * Check if current user has permission
   */
  hasPermission(permission: string): boolean {
    if (!this.context) {
      return false;
    }

    // Define permission mappings
    const permissions: Record<string, string[]> = {
      'contacts:read': ['admin', 'user', 'viewer'],
      'contacts:write': ['admin', 'user'],
      'contacts:delete': ['admin'],
      'companies:read': ['admin', 'user', 'viewer'],
      'companies:write': ['admin', 'user'],
      'companies:delete': ['admin'],
      'deals:read': ['admin', 'user', 'viewer'],
      'deals:write': ['admin', 'user'],
      'deals:delete': ['admin'],
      'organizations:read': ['admin'],
      'organizations:write': ['admin'],
      'users:read': ['admin'],
      'users:write': ['admin'],
      'activities:read': ['admin', 'user', 'viewer'],
      'activities:write': ['admin', 'user'],
      'activities:delete': ['admin'],
    };

    const allowedRoles = permissions[permission] || [];
    return allowedRoles.includes(this.context.role);
  }

  /**
   * Validate that a resource belongs to the current tenant
   */
  validateTenantResource(resourceOrgId: string): boolean {
    if (!this.context) {
      throw new Error('Tenant context not set');
    }
    
    return resourceOrgId === this.context.orgId;
  }

  /**
   * Get a query builder with tenant filtering applied
   */
  createTenantQuery<T>(repository: any, alias: string): any {
    const queryBuilder = repository.createQueryBuilder(alias);
    
    if (this.context) {
      queryBuilder.where(`${alias}.orgId = :orgId`, { orgId: this.context.orgId });
    }
    
    return queryBuilder;
  }

  /**
   * Apply tenant filter to find options
   */
  applyTenantFilter(findOptions: any = {}): any {
    if (!this.context) {
      throw new Error('Tenant context not set');
    }

    return {
      ...findOptions,
      where: {
        ...findOptions.where,
        orgId: this.context.orgId,
      },
    };
  }

  /**
   * Apply tenant filter to entity data
   */
  applyTenantData<T extends Record<string, any>>(data: T): T & { orgId: string } {
    if (!this.context) {
      throw new Error('Tenant context not set');
    }

    return {
      ...data,
      orgId: this.context.orgId,
    };
  }

  /**
   * Set database context for RLS policies
   */
  private async setDatabaseContext(context: TenantContext): Promise<void> {
    try {
      const environment = process.env.NODE_ENV || 'development';
      
      await this.dataSource.query(
        'SELECT set_user_context($1, $2, $3, $4)',
        [context.userId, context.orgId, context.role, environment]
      );
      
      this.logger.debug('Database context set for RLS', {
        userId: context.userId,
        orgId: context.orgId,
        role: context.role,
        environment,
      });
    } catch (error) {
      this.logger.error('Failed to set database context', error);
      // Don't throw error in development mode
      if (process.env.NODE_ENV !== 'development') {
        throw error;
      }
    }
  }

  /**
   * Clear the tenant context
   */
  async clearContext(): Promise<void> {
    this.context = null;
    
    try {
      await this.dataSource.query('SELECT clear_user_context()');
      this.logger.debug('Tenant context cleared');
    } catch (error) {
      this.logger.error('Failed to clear database context', error);
    }
  }

  /**
   * Create tenant-aware metadata for logging
   */
  getLogMetadata(): Record<string, any> {
    if (!this.context) {
      return {};
    }

    return {
      userId: this.context.userId,
      orgId: this.context.orgId,
      role: this.context.role,
      isAdmin: this.context.isAdmin,
    };
  }

  /**
   * Validate tenant limits (for usage enforcement)
   */
  async validateTenantLimits(resource: string, count?: number): Promise<boolean> {
    if (!this.context) {
      return false;
    }

    try {
      // Get organization limits
      const org = await this.dataSource.query(
        'SELECT max_users, max_storage_gb, plan FROM organizations WHERE id = $1',
        [this.context.orgId]
      );

      if (!org || org.length === 0) {
        return false;
      }

      const limits = org[0];

      // Check specific resource limits
      switch (resource) {
        case 'users':
          if (count !== undefined) {
            return count <= limits.max_users;
          }
          break;
        case 'storage':
          if (count !== undefined) {
            return count <= (limits.max_storage_gb * 1024 * 1024 * 1024); // Convert GB to bytes
          }
          break;
        default:
          return true;
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to validate tenant limits', error);
      return false;
    }
  }
}
