# Kong Gateway Configuration for OneCRM

This directory contains comprehensive Kong Gateway configurations for the OneCRM application, supporting various deployment scenarios and architectural patterns.

## 📁 Configuration Files

### Core Configurations

| File | Purpose | Environment |
|------|---------|-------------|
| `kong.yml` | Basic development setup | Development |
| `kong-production.yml` | Production-ready configuration | Production |
| `kong-comprehensive.yml` | Full-featured configuration | All environments |
| `kong-staging.yml` | Staging environment setup | Staging |

### Specialized Configurations

| File | Purpose | Use Case |
|------|---------|----------|
| `kong-microservices.yml` | Microservices architecture | Service decomposition |
| `kong-versioning.yml` | API versioning strategies | Version management |

## 🚀 Quick Start

### Development Setup

1. **Start Kong with basic configuration:**
   ```bash
   kong start -c kong.yml
   ```

2. **Verify Kong is running:**
   ```bash
   curl -i http://localhost:8001/
   ```

3. **Test API routing:**
   ```bash
   curl -i http://localhost:8000/api/health
   ```

### Production Deployment

1. **Use production configuration:**
   ```bash
   kong start -c kong-production.yml
   ```

2. **Configure SSL certificates:**
   ```bash
   # Update certificates section in kong-production.yml
   # Add your SSL certificate and private key
   ```

3. **Set environment variables:**
   ```bash
   export KEYCLOAK_CLIENT_SECRET="your-secret"
   export SESSION_SECRET="your-session-secret"
   export REDIS_PASSWORD="your-redis-password"
   ```

## 🏗️ Architecture Overview

### Service Routing

```
Frontend (localhost:3000)
    ↓
Kong Gateway (localhost:8000)
    ↓
┌─────────────────────────────────────┐
│  /api        → CRM API (3001)       │
│  /auth       → Auth Service (3002)  │
│  /tenants    → Tenant Service (3003)│
│  /files      → File Service (3004)  │
│  /notifications → Notification (3005)│
└─────────────────────────────────────┘
```

### Microservices Architecture

```
Kong Gateway (localhost:8000)
    ↓
┌─────────────────────────────────────┐
│  /api/users      → User Service     │
│  /api/contacts   → Contact Service  │
│  /api/companies  → Company Service  │
│  /api/deals      → Deal Service     │
│  /api/analytics  → Analytics Service│
│  /api/email      → Email Service    │
│  /api/search     → Search Service   │
│  /api/webhooks   → Webhook Service  │
│  /api/audit      → Audit Service    │
│  /api/integrations → Integration    │
└─────────────────────────────────────┘
```

## 🔧 Configuration Features

### Security Features

- **CORS Configuration**: Proper cross-origin resource sharing
- **Rate Limiting**: Prevent API abuse and ensure fair usage
- **Request/Response Transformation**: Header manipulation and security
- **IP Restrictions**: Control access by IP address (production)
- **SSL/TLS Termination**: HTTPS support with certificate management

### Monitoring & Observability

- **Prometheus Metrics**: Comprehensive metrics collection
- **Request ID Tracking**: Unique request identification
- **Correlation ID**: Distributed tracing support
- **Health Checks**: Upstream service monitoring
- **Logging**: Request/response logging for debugging

### Performance Optimization

- **Load Balancing**: Round-robin and weighted algorithms
- **Health Checks**: Automatic failover for unhealthy services
- **Caching**: Response caching for improved performance
- **Request Size Limiting**: Prevent large payload attacks
- **Connection Pooling**: Efficient upstream connections

## 📋 Environment-Specific Settings

### Development (`kong.yml`)
- HTTP-only for simplicity
- Local service URLs (`host.docker.internal`)
- Relaxed rate limiting
- Debug logging enabled
- CORS allows localhost origins

### Staging (`kong-staging.yml`)
- HTTPS enforced
- OAuth2 authentication
- Redis-based rate limiting
- External monitoring integration
- Staging-specific domains

### Production (`kong-production.yml`)
- Full security hardening
- Keycloak OIDC integration
- Strict rate limiting
- SSL certificate management
- Production monitoring

## 🔄 API Versioning

The `kong-versioning.yml` configuration supports multiple versioning strategies:

### Path-based Versioning
```
/api/v1/contacts  → Version 1 (deprecated)
/api/v2/contacts  → Version 2 (current)
/api/v3/contacts  → Version 3 (beta)
```

### Header-based Versioning
```
Accept-Version: 1.0  → Version 1
Accept-Version: 2.0  → Version 2
Accept-Version: 3.0  → Version 3 beta
```

### Version Lifecycle
- **v1**: Deprecated (sunset: 2025-12-31)
- **v2**: Current stable version
- **v3**: Beta version (features may change)

## 🛠️ Customization Guide

### Adding New Services

1. **Define the service:**
   ```yaml
   services:
     - name: new-service
       url: http://host.docker.internal:3020
       tags:
         - new
         - service
   ```

2. **Add routing:**
   ```yaml
   routes:
     - name: new-service-route
       paths:
         - /api/new-service
       protocols:
         - http
         - https
   ```

3. **Configure plugins:**
   ```yaml
   plugins:
     - name: rate-limiting
       config:
         minute: 100
         hour: 1000
   ```

### Modifying Rate Limits

Update the rate limiting configuration:
```yaml
plugins:
  - name: rate-limiting
    config:
      minute: 200    # Requests per minute
      hour: 2000     # Requests per hour
      day: 20000     # Requests per day
      policy: local  # or 'redis' for distributed
```

### Adding Authentication

For OAuth2 authentication:
```yaml
plugins:
  - name: oauth2
    config:
      scopes:
        - read
        - write
      mandatory_scope: true
      provision_key: "your_provision_key"
```

## 🔍 Monitoring & Debugging

### Health Check Endpoints

- Kong Admin API: `http://localhost:8001/`
- Kong Status: `http://localhost:8001/status`
- Service Health: `http://localhost:8000/api/health`

### Metrics Collection

Prometheus metrics available at:
- `http://localhost:8001/metrics`

Key metrics:
- Request count by service
- Response latency
- Error rates
- Upstream health status

### Debugging

1. **Check Kong logs:**
   ```bash
   tail -f /usr/local/kong/logs/error.log
   ```

2. **Verify service configuration:**
   ```bash
   curl -i http://localhost:8001/services
   ```

3. **Test routing:**
   ```bash
   curl -i -H "Host: api.onecrm.com" http://localhost:8000/api/health
   ```

## 🚨 Troubleshooting

### Common Issues

1. **Service Unreachable**
   - Check if backend service is running
   - Verify network connectivity
   - Check Kong upstream configuration

2. **CORS Errors**
   - Verify CORS plugin configuration
   - Check allowed origins
   - Ensure preflight requests are handled

3. **Rate Limiting Issues**
   - Check rate limit configuration
   - Verify Redis connectivity (if using Redis policy)
   - Monitor rate limit headers

4. **SSL/TLS Issues**
   - Verify certificate configuration
   - Check certificate expiration
   - Ensure proper SNI configuration

### Performance Optimization

1. **Enable caching:**
   ```yaml
   plugins:
     - name: proxy-cache
       config:
         cache_ttl: 300
         strategy: memory
   ```

2. **Optimize health checks:**
   ```yaml
   healthchecks:
     active:
       interval: 30
       successes: 1
   ```

3. **Configure connection pooling:**
   ```yaml
   services:
     - name: service-name
       connect_timeout: 60000
       write_timeout: 60000
       read_timeout: 60000
   ```

## 📚 Additional Resources

- [Kong Gateway Documentation](https://docs.konghq.com/gateway/)
- [Kong Plugin Hub](https://docs.konghq.com/hub/)
- [Kong Admin API Reference](https://docs.konghq.com/gateway/admin-api/)
- [Kong Configuration Reference](https://docs.konghq.com/gateway/reference/configuration/)

## 🤝 Contributing

When modifying Kong configurations:

1. Test changes in development first
2. Validate configuration syntax
3. Update documentation
4. Test all affected routes
5. Monitor metrics after deployment

## 📞 Support

For Kong Gateway issues:
- Check Kong logs first
- Verify service connectivity
- Review configuration syntax
- Consult Kong documentation
- Contact OneCRM development team
