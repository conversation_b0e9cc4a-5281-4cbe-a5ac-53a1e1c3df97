import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Organization } from '../organizations/organization.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { Deal } from '../deals/deal.entity';

@Entity('users')
export class User {
  @ApiProperty({ description: 'Unique identifier for the user' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Keycloak subject identifier' })
  @Column({ name: 'keycloak_sub', length: 255, unique: true })
  keycloakSub: string;

  @ApiProperty({ description: 'User email address' })
  @Column({ length: 255, unique: true })
  email: string;

  @ApiProperty({ description: 'User first name', required: false })
  @Column({ name: 'first_name', length: 100, nullable: true })
  firstName?: string;

  @ApiProperty({ description: 'User last name', required: false })
  @Column({ name: 'last_name', length: 100, nullable: true })
  lastName?: string;

  @ApiProperty({ description: 'Organization ID' })
  @Column({ name: 'org_id' })
  orgId: string;

  @ApiProperty({ description: 'User role', enum: ['admin', 'user', 'viewer'] })
  @Column({ length: 50, default: 'user' })
  role: string;

  @ApiProperty({ description: 'Whether the user is active' })
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Last login timestamp', required: false })
  @Column({ name: 'last_login_at', type: 'timestamp with time zone', nullable: true })
  lastLoginAt?: Date;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Organization, (organization) => organization.users)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @OneToMany(() => Contact, (contact) => contact.createdBy)
  createdContacts: Contact[];

  @OneToMany(() => Contact, (contact) => contact.assignedTo)
  assignedContacts: Contact[];

  @OneToMany(() => Company, (company) => company.createdBy)
  createdCompanies: Company[];

  @OneToMany(() => Company, (company) => company.assignedTo)
  assignedCompanies: Company[];

  @OneToMany(() => Deal, (deal) => deal.owner)
  ownedDeals: Deal[];

  @OneToMany(() => Deal, (deal) => deal.createdBy)
  createdDeals: Deal[];
}
