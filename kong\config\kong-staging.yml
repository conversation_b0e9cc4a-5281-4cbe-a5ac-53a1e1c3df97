# Kong Gateway Configuration for Staging Environment
# OneCRM Staging Environment Configuration
# Version: 3.0

_format_version: "3.0"
_transform: true

# ============================================================================
# SERVICES CONFIGURATION - STAGING
# ============================================================================

services:
  # CRM API Service - Staging
  - name: crm-api-staging
    url: https://api-staging.onecrm.com
    protocol: https
    host: api-staging.onecrm.com
    port: 443
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    tags:
      - crm
      - api
      - staging
    routes:
      - name: crm-api-staging-route
        paths:
          - /api
        methods:
          - GET
          - POST
          - PUT
          - PATCH
          - DELETE
          - OPTIONS
        strip_path: false
        preserve_host: false
        protocols:
          - https
        tags:
          - api
          - staging
    plugins:
      # OAuth2 Authentication for Staging
      - name: oauth2
        config:
          scopes:
            - read
            - write
            - admin
          mandatory_scope: true
          provision_key: "staging_provision_key"
          token_expiration: 3600
          enable_authorization_code: true
          enable_client_credentials: true
          enable_implicit_grant: false
          enable_password_grant: false
          hide_credentials: true
          accept_http_if_already_terminated: true
      
      # CORS Configuration for Staging
      - name: cors
        config:
          origins:
            - "https://staging.onecrm.com"
            - "https://app-staging.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-Type
            - Date
            - Authorization
            - X-Org-Id
            - X-User-Id
          exposed_headers:
            - X-Auth-Token
            - X-Tenant-Id
            - X-Kong-Request-ID
            - X-RateLimit-Limit
            - X-RateLimit-Remaining
          credentials: true
          max_age: 3600
      
      # Rate Limiting for Staging
      - name: rate-limiting
        config:
          minute: 200
          hour: 2000
          day: 20000
          policy: redis
          redis_host: "redis-staging.onecrm.com"
          redis_port: 6379
          redis_password: "{{ REDIS_PASSWORD }}"
          redis_database: 1
          hide_client_headers: false
          fault_tolerant: true
      
      # Request Transformation
      - name: request-transformer
        config:
          add:
            headers:
              - "X-Kong-Request-ID:$(kong.request.get_header('kong-request-id'))"
              - "X-Environment:staging"
              - "X-Forwarded-Proto:https"
          remove:
            headers:
              - X-Client-Org-Id
      
      # Response Transformation with Security Headers
      - name: response-transformer
        config:
          add:
            headers:
              - "X-Kong-Upstream-Latency:$(kong.response.get_header('x-kong-upstream-latency'))"
              - "X-Content-Type-Options:nosniff"
              - "X-Frame-Options:SAMEORIGIN"
              - "X-XSS-Protection:1; mode=block"
              - "Strict-Transport-Security:max-age=31536000; includeSubDomains"
              - "Content-Security-Policy:default-src 'self'"
      
      # Request Size Limiting
      - name: request-size-limiting
        config:
          allowed_payload_size: 10
          size_unit: megabytes

  # Auth Service - Staging
  - name: auth-service-staging
    url: https://auth-staging.onecrm.com
    protocol: https
    host: auth-staging.onecrm.com
    port: 443
    tags:
      - auth
      - service
      - staging
    routes:
      - name: auth-service-staging-route
        paths:
          - /auth
        protocols:
          - https
        tags:
          - auth
          - staging
    plugins:
      # CORS for Auth Service
      - name: cors
        config:
          origins:
            - "https://staging.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          credentials: true

  # Tenant Service - Staging
  - name: tenant-service-staging
    url: https://tenant-staging.onecrm.com
    protocol: https
    host: tenant-staging.onecrm.com
    port: 443
    tags:
      - tenant
      - service
      - staging
    routes:
      - name: tenant-service-staging-route
        paths:
          - /tenants
        protocols:
          - https
        tags:
          - tenant
          - staging
    plugins:
      # OAuth2 for Tenant Service
      - name: oauth2
        config:
          scopes:
            - read
            - write
          mandatory_scope: true
          hide_credentials: true

# ============================================================================
# GLOBAL PLUGINS - STAGING
# ============================================================================

plugins:
  # Prometheus Metrics
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
  
  # Request ID
  - name: request-id
    config:
      header_name: "Kong-Request-ID"
      generator: "uuid"
      echo_downstream: true
  
  # Correlation ID
  - name: correlation-id
    config:
      header_name: "Kong-Correlation-ID"
      generator: "uuid"
      echo_downstream: true
  
  # HTTP Log to External Service
  - name: http-log
    config:
      http_endpoint: "https://logs-staging.onecrm.com/kong"
      method: "POST"
      timeout: 10000
      keepalive: 60000
      content_type: "application/json"
      flush_timeout: 2
      retry_count: 5
  
  # Datadog Integration
  - name: datadog
    config:
      host: "datadog-agent-staging"
      port: 8125
      prefix: "kong.staging"
      metrics:
        - name: "request_count"
          stat_type: "counter"
          sample_rate: 1
        - name: "latency"
          stat_type: "timer"
          sample_rate: 1

# ============================================================================
# CONSUMERS - STAGING
# ============================================================================

consumers:
  # Staging Frontend Consumer
  - username: "onecrm-frontend-staging"
    custom_id: "frontend-staging-001"
    tags:
      - frontend
      - staging
    
  # Staging Mobile Consumer
  - username: "onecrm-mobile-staging"
    custom_id: "mobile-staging-001"
    tags:
      - mobile
      - staging
    
  # QA Testing Consumer
  - username: "qa-testing"
    custom_id: "qa-001"
    tags:
      - qa
      - testing
      - staging

# ============================================================================
# UPSTREAMS - STAGING
# ============================================================================

upstreams:
  # CRM API Upstream - Staging
  - name: crm-api-staging-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/api/health"
        https_verify_certificate: true
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    tags:
      - crm
      - api
      - staging
    targets:
      - target: "api-staging.onecrm.com:443"
        weight: 100
        tags:
          - primary
          - staging

# ============================================================================
# CERTIFICATES - STAGING
# ============================================================================

certificates:
  # Staging SSL Certificate (Let's Encrypt)
  # - cert: "{{ STAGING_SSL_CERT }}"
  #   key: "{{ STAGING_SSL_KEY }}"
  #   tags:
  #     - staging
  #     - letsencrypt

# ============================================================================
# SNI - STAGING
# ============================================================================

snis:
  # - name: "api-staging.onecrm.com"
  #   certificate:
  #     id: "staging-cert-id"
  #   tags:
  #     - staging

# ============================================================================
# VAULTS - STAGING
# ============================================================================

vaults:
  # Environment Variables
  - name: env
    prefix: env
    config:
      prefix: "KONG_STAGING_"
    tags:
      - staging
      - environment

# Configuration Notes:
# - Uses HTTPS for all services
# - OAuth2 authentication enabled
# - Redis-based rate limiting
# - Enhanced security headers
# - External logging to staging monitoring
# - Datadog metrics collection
# - SSL certificate management
# - Health checks with certificate verification
