'use client';

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Avatar,
} from '@mui/material';
import {
  TrendingUp,
  AttachMoney,
  Assessment,
  Timeline,
  EmojiEvents,
  Schedule,
} from '@mui/icons-material';

interface DealStats {
  totalValue: number;
  totalDeals: number;
  wonDeals: number;
  lostDeals: number;
  averageDealSize: number;
  conversionRate: number;
  pipelineValue: number;
  expectedRevenue: number;
  stageBreakdown: Array<{
    stage: string;
    count: number;
    value: number;
    percentage: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    deals: number;
    value: number;
  }>;
}

interface DealsStatsProps {
  stats?: DealStats;
  loading?: boolean;
}

export const DealsStats: React.FC<DealsStatsProps> = ({
  stats,
  loading = false,
}) => {
  // Default stats if none provided
  const defaultStats: DealStats = {
    totalValue: 2450000,
    totalDeals: 89,
    wonDeals: 23,
    lostDeals: 8,
    averageDealSize: 75000,
    conversionRate: 74.2,
    pipelineValue: 1850000,
    expectedRevenue: 1245000,
    stageBreakdown: [
      { stage: 'Prospecting', count: 25, value: 650000, percentage: 35.1 },
      { stage: 'Qualification', count: 18, value: 480000, percentage: 25.9 },
      { stage: 'Proposal', count: 12, value: 320000, percentage: 17.3 },
      { stage: 'Negotiation', count: 8, value: 240000, percentage: 13.0 },
      { stage: 'Closed Won', count: 6, value: 160000, percentage: 8.6 },
    ],
    monthlyTrend: [
      { month: 'Oct 2024', deals: 8, value: 245000 },
      { month: 'Nov 2024', deals: 12, value: 380000 },
      { month: 'Dec 2024', deals: 15, value: 425000 },
    ],
  };

  const statsData = stats || defaultStats;
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getStageColor = (stage: string) => {
    const colors: Record<string, string> = {
      'Prospecting': '#2196f3',
      'Qualification': '#ff9800',
      'Proposal': '#9c27b0',
      'Negotiation': '#f44336',
      'Closed Won': '#4caf50',
      'Closed Lost': '#757575',
    };
    return colors[stage] || '#2196f3';
  };

  if (loading) {
    return (
      <Grid container spacing={3}>
        {[1, 2, 3, 4].map((i) => (
          <Grid item xs={12} sm={6} md={3} key={i}>
            <Card>
              <CardContent>
                <Box sx={{ height: 80 }}>
                  <LinearProgress />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Total Pipeline Value */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                <AttachMoney />
              </Avatar>
              <Typography variant="h6" color="text.secondary">
                Pipeline Value
              </Typography>
            </Box>
            <Typography variant="h4" fontWeight="bold" color="primary">
              {formatCurrency(statsData.pipelineValue)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {statsData.totalDeals} active deals
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Expected Revenue */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                <TrendingUp />
              </Avatar>
              <Typography variant="h6" color="text.secondary">
                Expected Revenue
              </Typography>
            </Box>
            <Typography variant="h4" fontWeight="bold" color="success.main">
              {formatCurrency(statsData.expectedRevenue)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Weighted by probability
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Conversion Rate */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                <Assessment />
              </Avatar>
              <Typography variant="h6" color="text.secondary">
                Conversion Rate
              </Typography>
            </Box>
            <Typography variant="h4" fontWeight="bold" color="warning.main">
              {formatPercentage(statsData.conversionRate)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {statsData.wonDeals} won / {statsData.wonDeals + statsData.lostDeals} closed
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Average Deal Size */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                <Timeline />
              </Avatar>
              <Typography variant="h6" color="text.secondary">
                Avg Deal Size
              </Typography>
            </Box>
            <Typography variant="h4" fontWeight="bold" color="info.main">
              {formatCurrency(statsData.averageDealSize)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Based on closed deals
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Pipeline Breakdown */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmojiEvents />
              Pipeline Breakdown by Stage
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              {statsData.stageBreakdown.map((stage) => (
                <Box key={stage.stage} sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={stage.stage}
                        size="small"
                        sx={{ 
                          bgcolor: getStageColor(stage.stage),
                          color: 'white',
                          minWidth: 100
                        }}
                      />
                      <Typography variant="body2">
                        {stage.count} deals
                      </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight="bold">
                      {formatCurrency(stage.value)}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stage.percentage}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: 'grey.200',
                      '& .MuiLinearProgress-bar': {
                        bgcolor: getStageColor(stage.stage),
                        borderRadius: 4,
                      },
                    }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {formatPercentage(stage.percentage)} of total pipeline
                  </Typography>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Recent Performance */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Schedule />
              Recent Performance
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              {statsData.monthlyTrend.slice(-3).map((month, index) => (
                <Box key={month.month} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {month.month}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {month.deals} deals closed
                  </Typography>
                  <Typography variant="body2" fontWeight="bold" color="success.main">
                    {formatCurrency(month.value)}
                  </Typography>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};
