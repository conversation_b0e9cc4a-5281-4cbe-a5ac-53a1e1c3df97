'use client';

import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Container } from '@/components/layout/app-shell';
import {
  Building2,
  Users,
  TrendingUp,
  BarChart3,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export default function HomePage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  const isAuthenticated = status === 'authenticated';
  const isLoading = status === 'loading';

  useEffect(() => {
    // Redirect authenticated users to dashboard
    if (!isLoading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <Container className="py-8">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Loading OneCRM...</p>
        </div>
      </Container>
    );
  }

  if (isAuthenticated) {
    return null; // Will redirect to dashboard
  }

  return (
    <Container className="py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold tracking-tight text-primary mb-4">
          OneCRM
        </h1>
        <h2 className="text-xl text-muted-foreground mb-6">
          Enterprise Customer Relationship Management
        </h2>
        <p className="text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          Streamline your business relationships with our comprehensive CRM solution.
          Manage contacts, track deals, and grow your business with powerful analytics.
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <Card className="text-center">
          <CardHeader>
            <Users className="h-12 w-12 text-primary mx-auto mb-4" />
            <CardTitle>Contact Management</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Organize and manage all your customer relationships in one place
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <Building2 className="h-12 w-12 text-primary mx-auto mb-4" />
            <CardTitle>Company Tracking</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Track companies and organizational hierarchies effectively
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <TrendingUp className="h-12 w-12 text-primary mx-auto mb-4" />
            <CardTitle>Sales Pipeline</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Visual deal tracking and sales forecasting tools
            </CardDescription>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardHeader>
            <BarChart3 className="h-12 w-12 text-primary mx-auto mb-4" />
            <CardTitle>Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Comprehensive reporting and business insights
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      {/* Call to Action */}
      <Card className="p-8 bg-accent/50 text-center mb-12">
        <CardHeader>
          <CardTitle className="text-3xl">Ready to Get Started?</CardTitle>
          <CardDescription className="text-lg">
            Sign in to access your CRM dashboard and start managing your business relationships.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 justify-center flex-wrap">
            <Button
              size="lg"
              onClick={() => router.push('/login')}
              className="min-w-[120px]"
            >
              Sign In
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => router.push('/dashboard')}
              className="min-w-[120px]"
            >
              View Demo
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Features List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Why Choose OneCRM?</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Modern, intuitive interface</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Enterprise-grade security</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Multi-tenant architecture</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span>Real-time collaboration</span>
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Built with Modern Tech</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500" />
              <span>Next.js 15 & React 19</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500" />
              <span>TypeScript & Zod validation</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500" />
              <span>shadcn/ui components</span>
            </div>
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500" />
              <span>Tailwind CSS styling</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-muted-foreground">
        <p>Built with Next.js, React, TypeScript, and shadcn/ui</p>
        <p>Enterprise-ready CRM solution</p>
      </div>
    </Container>
  );
}
