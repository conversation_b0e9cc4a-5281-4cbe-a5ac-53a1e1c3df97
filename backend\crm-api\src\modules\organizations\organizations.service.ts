import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from './organization.entity';
import { User } from '../users/user.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { InviteUserDto } from './dto/invite-user.dto';

@Injectable()
export class OrganizationsService {
  private readonly logger = new Logger(OrganizationsService.name);

  constructor(
    @InjectRepository(Organization)
    private organizationsRepository: Repository<Organization>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private tenantContextService: TenantContextService,
  ) {}

  async findById(id: string): Promise<Organization> {
    const organization = await this.organizationsRepository.findOne({
      where: { id },
      relations: ['users'],
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID ${id} not found`);
    }

    // Validate tenant access
    if (!this.tenantContextService.validateTenantResource(organization.id)) {
      throw new ForbiddenException('Access denied to this organization');
    }

    return organization;
  }

  async findBySlug(slug: string): Promise<Organization | null> {
    return this.organizationsRepository.findOne({
      where: { slug },
      relations: ['users'],
    });
  }

  async getCurrentOrganization(): Promise<Organization> {
    const orgId = this.tenantContextService.getOrgId();
    return this.findById(orgId);
  }

  async create(createDto: CreateOrganizationDto): Promise<Organization> {
    // Check if slug is already taken
    const existingOrg = await this.findBySlug(createDto.slug);
    if (existingOrg) {
      throw new ConflictException(`Organization with slug '${createDto.slug}' already exists`);
    }

    // Set default values based on plan
    const planDefaults = this.getPlanDefaults(createDto.plan || 'free');

    const organization = this.organizationsRepository.create({
      ...createDto,
      plan: createDto.plan || 'free',
      maxUsers: createDto.maxUsers || planDefaults.maxUsers,
      maxStorageGb: createDto.maxStorageGb || planDefaults.maxStorageGb,
      features: { ...planDefaults.features, ...createDto.features },
    });

    const savedOrg = await this.organizationsRepository.save(organization) as Organization;

    this.logger.log(`Organization created: ${savedOrg.name} (${savedOrg.id})`);

    return savedOrg;
  }

  async update(updateData: UpdateOrganizationDto): Promise<Organization> {
    const orgId = this.tenantContextService.getOrgId();

    // Only admins can update organization
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can update organization settings');
    }

    // If slug is being updated, check for conflicts
    if (updateData.slug) {
      const existingOrg = await this.findBySlug(updateData.slug);
      if (existingOrg && existingOrg.id !== orgId) {
        throw new ConflictException(`Organization with slug '${updateData.slug}' already exists`);
      }
    }

    await this.organizationsRepository.update(orgId, updateData);

    this.logger.log(`Organization updated: ${orgId}`);

    return this.findById(orgId);
  }

  async updateSettings(settings: Record<string, any>): Promise<Organization> {
    const orgId = this.tenantContextService.getOrgId();

    // Only admins can update settings
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can update organization settings');
    }

    await this.organizationsRepository.update(orgId, { settings });

    this.logger.log(`Organization settings updated: ${orgId}`);

    return this.findById(orgId);
  }

  async getOrganizationStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    totalContacts: number;
    totalCompanies: number;
    totalDeals: number;
    storageUsed: number;
  }> {
    const orgId = this.tenantContextService.getOrgId();

    // Get user counts
    const totalUsers = await this.usersRepository.count({ where: { orgId } });
    const activeUsers = await this.usersRepository.count({ where: { orgId, isActive: true } });

    // Note: These would need to be implemented when we have the other entities
    const totalContacts = 0; // await this.contactsRepository.count({ where: { orgId } });
    const totalCompanies = 0; // await this.companiesRepository.count({ where: { orgId } });
    const totalDeals = 0; // await this.dealsRepository.count({ where: { orgId } });
    const storageUsed = 0; // Calculate actual storage usage

    return {
      totalUsers,
      activeUsers,
      totalContacts,
      totalCompanies,
      totalDeals,
      storageUsed,
    };
  }

  async validateLimits(resource: string, currentCount?: number): Promise<boolean> {
    const orgId = this.tenantContextService.getOrgId();
    const organization = await this.findById(orgId);

    switch (resource) {
      case 'users':
        const userCount = currentCount ?? await this.usersRepository.count({ where: { orgId } });
        return userCount < organization.maxUsers;

      case 'storage':
        // Implement storage calculation
        return true; // For now, always allow

      default:
        return true;
    }
  }

  async inviteUser(inviteDto: InviteUserDto): Promise<{ message: string; invitationId: string }> {
    const orgId = this.tenantContextService.getOrgId();

    // Only admins can invite users
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can invite users');
    }

    // Check if user limit would be exceeded
    if (!(await this.validateLimits('users'))) {
      throw new BadRequestException('User limit exceeded for this organization');
    }

    // Check if user is already in the organization
    const existingUser = await this.usersRepository.findOne({
      where: { email: inviteDto.email, orgId },
    });

    if (existingUser) {
      throw new ConflictException('User is already a member of this organization');
    }

    // TODO: Implement invitation logic
    // This would create an invitation record and send an email

    this.logger.log(`User invited: ${inviteDto.email} to organization ${orgId}`);

    return {
      message: 'User invitation sent successfully',
      invitationId: 'temp-invitation-id', // Replace with actual invitation ID
    };
  }

  private getPlanDefaults(plan: string): {
    maxUsers: number;
    maxStorageGb: number;
    features: Record<string, boolean>;
  } {
    switch (plan) {
      case 'enterprise':
        return {
          maxUsers: 1000,
          maxStorageGb: 1000,
          features: {
            advanced_reporting: true,
            api_access: true,
            custom_fields: true,
            sso: true,
            audit_logs: true,
          },
        };

      case 'pro':
        return {
          maxUsers: 50,
          maxStorageGb: 100,
          features: {
            advanced_reporting: true,
            api_access: true,
            custom_fields: true,
            sso: false,
            audit_logs: false,
          },
        };

      case 'free':
      default:
        return {
          maxUsers: 5,
          maxStorageGb: 5,
          features: {
            advanced_reporting: false,
            api_access: false,
            custom_fields: false,
            sso: false,
            audit_logs: false,
          },
        };
    }
  }

  /**
   * Test method to get organizations data directly without tenant context
   */
  async getTestData() {
    try {
      const organizations = await this.organizationsRepository
        .createQueryBuilder('organization')
        .leftJoinAndSelect('organization.users', 'users')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: organizations.length,
        data: organizations,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
