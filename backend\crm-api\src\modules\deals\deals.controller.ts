import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { DealsService } from './deals.service';
import { Deal } from './deal.entity';
import { CreateDealDto } from './dto/create-deal.dto';
import { UpdateDealDto } from './dto/update-deal.dto';
import { SearchDealsDto, DealsResponseDto, DealsPipelineDto, DealsForecastDto } from './dto/search-deals.dto';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('deals')
@ApiBearerAuth()
@Controller('deals')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class DealsController {
  constructor(private readonly dealsService: DealsService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint for deals module' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Deals module is working',
      timestamp: new Date().toISOString(),
    };
  }

  @Public()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get deals data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.dealsService.getTestData();
  }

  @Post()
  // @RequirePermissions('deals:write')
  @ApiOperation({ summary: 'Create a new deal' })
  @ApiResponse({ status: 201, description: 'Deal created successfully', type: Deal })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(@Body() createDealDto: CreateDealDto): Promise<Deal> {
    return this.dealsService.create(createDealDto);
  }

  @Get()
  // @RequirePermissions('deals:read')
  @ApiOperation({ summary: 'Get all deals with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Deals retrieved successfully', type: DealsResponseDto })
  @ApiQuery({ name: 'search', required: false, description: 'Search query for title or description' })
  @ApiQuery({ name: 'stage', required: false, description: 'Filter by deal stage' })
  @ApiQuery({ name: 'ownerId', required: false, description: 'Filter by deal owner ID' })
  @ApiQuery({ name: 'contactId', required: false, description: 'Filter by contact ID' })
  @ApiQuery({ name: 'companyId', required: false, description: 'Filter by company ID' })
  @ApiQuery({ name: 'source', required: false, description: 'Filter by deal source' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by deal type' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by deal priority' })
  @ApiQuery({ name: 'minAmount', required: false, description: 'Minimum deal amount', type: Number })
  @ApiQuery({ name: 'maxAmount', required: false, description: 'Maximum deal amount', type: Number })
  @ApiQuery({ name: 'minProbability', required: false, description: 'Minimum probability', type: Number })
  @ApiQuery({ name: 'maxProbability', required: false, description: 'Maximum probability', type: Number })
  @ApiQuery({ name: 'expectedCloseDateFrom', required: false, description: 'Expected close date from' })
  @ApiQuery({ name: 'expectedCloseDateTo', required: false, description: 'Expected close date to' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  async findAll(@Query() searchDto: SearchDealsDto): Promise<DealsResponseDto> {
    return this.dealsService.findAll(searchDto);
  }

  @Get('pipeline')
  // @RequirePermissions('deals:read')
  @ApiOperation({ summary: 'Get deals pipeline statistics' })
  @ApiResponse({ status: 200, description: 'Pipeline statistics retrieved successfully', type: DealsPipelineDto })
  async getPipeline(): Promise<DealsPipelineDto> {
    return this.dealsService.getPipeline();
  }

  @Get('forecast')
  // @RequirePermissions('deals:read')
  @ApiOperation({ summary: 'Get deals forecast' })
  @ApiResponse({ status: 200, description: 'Forecast retrieved successfully', type: DealsForecastDto })
  async getForecast(): Promise<DealsForecastDto> {
    return this.dealsService.getForecast();
  }

  @Get(':id')
  // @RequirePermissions('deals:read')
  @ApiOperation({ summary: 'Get deal by ID' })
  @ApiResponse({ status: 200, description: 'Deal retrieved successfully', type: Deal })
  @ApiResponse({ status: 404, description: 'Deal not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Deal> {
    return this.dealsService.findById(id);
  }

  @Put(':id')
  // @RequirePermissions('deals:write')
  @ApiOperation({ summary: 'Update deal by ID' })
  @ApiResponse({ status: 200, description: 'Deal updated successfully', type: Deal })
  @ApiResponse({ status: 404, description: 'Deal not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDealDto: UpdateDealDto
  ): Promise<Deal> {
    return this.dealsService.update(id, updateDealDto);
  }

  @Delete(':id')
  // @RequirePermissions('deals:delete')
  @ApiOperation({ summary: 'Delete deal by ID (soft delete)' })
  @ApiResponse({ status: 200, description: 'Deal deleted successfully' })
  @ApiResponse({ status: 404, description: 'Deal not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.dealsService.remove(id);
    return { message: 'Deal deleted successfully' };
  }
}
