import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';

export interface CreateCustomerDto {
  email: string;
  name: string;
  organizationId: string;
  address?: Stripe.AddressParam;
  phone?: string;
  metadata?: Record<string, string>;
}

export interface CreateSubscriptionDto {
  customerId: string;
  priceId: string;
  trialPeriodDays?: number;
  metadata?: Record<string, string>;
  paymentMethodId?: string;
}

export interface UpdateSubscriptionDto {
  subscriptionId: string;
  priceId?: string;
  quantity?: number;
  metadata?: Record<string, string>;
  cancelAtPeriodEnd?: boolean;
}

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private readonly stripe: Stripe;

  constructor(private readonly configService: ConfigService) {
    const secretKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY is required');
    }

    this.stripe = new Stripe(secretKey, {
      apiVersion: '2023-10-16',
    });
  }

  async createCustomer(dto: CreateCustomerDto): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.create({
        email: dto.email,
        name: dto.name,
        address: dto.address,
        phone: dto.phone,
        metadata: {
          organizationId: dto.organizationId,
          ...dto.metadata,
        },
      });

      this.logger.log(`Created Stripe customer: ${customer.id} for organization: ${dto.organizationId}`);
      return customer;
    } catch (error) {
      this.logger.error(`Failed to create Stripe customer: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateCustomer(customerId: string, updates: Partial<CreateCustomerDto>): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.update(customerId, {
        email: updates.email,
        name: updates.name,
        address: updates.address,
        phone: updates.phone,
        metadata: updates.metadata,
      });

      this.logger.log(`Updated Stripe customer: ${customerId}`);
      return customer;
    } catch (error) {
      this.logger.error(`Failed to update Stripe customer: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createSubscription(dto: CreateSubscriptionDto): Promise<Stripe.Subscription> {
    try {
      const subscriptionData: Stripe.SubscriptionCreateParams = {
        customer: dto.customerId,
        items: [{ price: dto.priceId }],
        metadata: dto.metadata,
        expand: ['latest_invoice.payment_intent'],
      };

      if (dto.trialPeriodDays) {
        subscriptionData.trial_period_days = dto.trialPeriodDays;
      }

      if (dto.paymentMethodId) {
        subscriptionData.default_payment_method = dto.paymentMethodId;
      }

      const subscription = await this.stripe.subscriptions.create(subscriptionData);

      this.logger.log(`Created Stripe subscription: ${subscription.id} for customer: ${dto.customerId}`);
      return subscription;
    } catch (error) {
      this.logger.error(`Failed to create Stripe subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateSubscription(dto: UpdateSubscriptionDto): Promise<Stripe.Subscription> {
    try {
      const updateData: Stripe.SubscriptionUpdateParams = {
        metadata: dto.metadata,
      };

      if (dto.priceId) {
        // Get current subscription to update items
        const currentSub = await this.stripe.subscriptions.retrieve(dto.subscriptionId);
        updateData.items = [
          {
            id: currentSub.items.data[0].id,
            price: dto.priceId,
            quantity: dto.quantity || 1,
          },
        ];
      }

      if (dto.cancelAtPeriodEnd !== undefined) {
        updateData.cancel_at_period_end = dto.cancelAtPeriodEnd;
      }

      const subscription = await this.stripe.subscriptions.update(dto.subscriptionId, updateData);

      this.logger.log(`Updated Stripe subscription: ${dto.subscriptionId}`);
      return subscription;
    } catch (error) {
      this.logger.error(`Failed to update Stripe subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  async cancelSubscription(subscriptionId: string, immediately = false): Promise<Stripe.Subscription> {
    try {
      let subscription: Stripe.Subscription;

      if (immediately) {
        subscription = await this.stripe.subscriptions.cancel(subscriptionId);
      } else {
        subscription = await this.stripe.subscriptions.update(subscriptionId, {
          cancel_at_period_end: true,
        });
      }

      this.logger.log(`Canceled Stripe subscription: ${subscriptionId} (immediate: ${immediately})`);
      return subscription;
    } catch (error) {
      this.logger.error(`Failed to cancel Stripe subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createPaymentMethod(customerId: string, paymentMethodId: string): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      this.logger.log(`Attached payment method: ${paymentMethodId} to customer: ${customerId}`);
      return paymentMethod;
    } catch (error) {
      this.logger.error(`Failed to attach payment method: ${error.message}`, error.stack);
      throw error;
    }
  }

  async setDefaultPaymentMethod(customerId: string, paymentMethodId: string): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });

      this.logger.log(`Set default payment method: ${paymentMethodId} for customer: ${customerId}`);
      return customer;
    } catch (error) {
      this.logger.error(`Failed to set default payment method: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
        usage: 'off_session',
      });

      this.logger.log(`Created setup intent: ${setupIntent.id} for customer: ${customerId}`);
      return setupIntent;
    } catch (error) {
      this.logger.error(`Failed to create setup intent: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCustomerPaymentMethods(customerId: string): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods.data;
    } catch (error) {
      this.logger.error(`Failed to get customer payment methods: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSubscription(subscriptionId: string): Promise<Stripe.Subscription> {
    try {
      return await this.stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['latest_invoice', 'customer'],
      });
    } catch (error) {
      this.logger.error(`Failed to get Stripe subscription: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInvoice(invoiceId: string): Promise<Stripe.Invoice> {
    try {
      return await this.stripe.invoices.retrieve(invoiceId);
    } catch (error) {
      this.logger.error(`Failed to get Stripe invoice: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCustomerInvoices(customerId: string, limit = 10): Promise<Stripe.Invoice[]> {
    try {
      const invoices = await this.stripe.invoices.list({
        customer: customerId,
        limit,
      });

      return invoices.data;
    } catch (error) {
      this.logger.error(`Failed to get customer invoices: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createBillingPortalSession(customerId: string, returnUrl: string): Promise<Stripe.BillingPortal.Session> {
    try {
      const session = await this.stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      this.logger.log(`Created billing portal session for customer: ${customerId}`);
      return session;
    } catch (error) {
      this.logger.error(`Failed to create billing portal session: ${error.message}`, error.stack);
      throw error;
    }
  }

  async constructWebhookEvent(payload: string | Buffer, signature: string): Promise<Stripe.Event> {
    const webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');
    if (!webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET is required for webhook verification');
    }

    try {
      return this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);
    } catch (error) {
      this.logger.error(`Failed to construct webhook event: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Product and Price management
  async createProduct(name: string, description?: string): Promise<Stripe.Product> {
    try {
      const product = await this.stripe.products.create({
        name,
        description,
      });

      this.logger.log(`Created Stripe product: ${product.id}`);
      return product;
    } catch (error) {
      this.logger.error(`Failed to create Stripe product: ${error.message}`, error.stack);
      throw error;
    }
  }

  async createPrice(
    productId: string,
    unitAmount: number,
    currency = 'usd',
    interval: 'month' | 'year' = 'month',
  ): Promise<Stripe.Price> {
    try {
      const price = await this.stripe.prices.create({
        product: productId,
        unit_amount: unitAmount,
        currency,
        recurring: {
          interval,
        },
      });

      this.logger.log(`Created Stripe price: ${price.id} for product: ${productId}`);
      return price;
    } catch (error) {
      this.logger.error(`Failed to create Stripe price: ${error.message}`, error.stack);
      throw error;
    }
  }
}
