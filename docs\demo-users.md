# Demo Users for OneCRM Testing

## Demo User Organization Structure

```mermaid
graph TB
    subgraph "System Level"
        SA[Super Admin<br/>superadmin]
    end

    subgraph "TechCorp Organization"
        OA1[Org Admin<br/>admin.techcorp]

        subgraph "Sales Department"
            SM1[Sales Manager<br/>manager.sales]
            SR1[Senior Sales Rep<br/>sales.senior]
            SR2[Junior Sales Rep<br/>sales.junior]
        end

        subgraph "Marketing Department"
            MM1[Marketing Manager<br/>manager.marketing]
            MU1[Marketing Specialist<br/>marketing.specialist]
        end

        subgraph "Support Department"
            SPM1[Support Manager<br/>manager.support]
            SP1[Support Agent L1<br/>support.l1]
            SP2[Support Agent L2<br/>support.l2]
        end

        EV1[Executive Viewer<br/>executive.viewer]
        CON1[External Consultant<br/>consultant.external]
        INT1[Intern<br/>intern.summer]
    end

    subgraph "StartupInc Organization"
        OA2[Org Admin<br/>admin.startup]
        SR3[Sales Rep<br/>sales.startup]
    end

    SA --> OA1
    SA --> OA2
    OA1 --> SM1
    OA1 --> MM1
    OA1 --> SPM1
    SM1 --> SR1
    SM1 --> SR2
    MM1 --> MU1
    SPM1 --> SP1
    SPM1 --> SP2
    OA1 --> EV1
    OA1 --> CON1
    SR1 --> INT1
    OA2 --> SR3

    style SA fill:#ff6b6b
    style OA1 fill:#4ecdc4
    style OA2 fill:#4ecdc4
    style SM1 fill:#45b7d1
    style MM1 fill:#45b7d1
    style SPM1 fill:#45b7d1
```

## User Creation Instructions

### Navigate to Keycloak Admin Console
```
URL: http://localhost:8080/admin
Realm: onecrm
Go to: Users → Create User
```

## Demo Users List

### 1. Super Admin
```
Username: superadmin
Email: <EMAIL>
First Name: Super
Last Name: Administrator
☑ Email Verified: ON
☑ Enabled: ON

Password: Super@123
☐ Temporary: OFF

Roles:
- super-admin

Attributes:
organization_id: system
department: IT
security_clearance: maximum
data_access_level: global
```

### 2. Organization Admin - TechCorp
```
Username: admin.techcorp
Email: <EMAIL>
First Name: John
Last Name: Smith
☑ Email Verified: ON
☑ Enabled: ON

Password: Admin@123
☐ Temporary: OFF

Roles:
- org-admin

Attributes:
organization_id: org-techcorp
department: Administration
territory: Global
security_clearance: high
data_access_level: organization
cost_center: CC-ADMIN
```

### 3. Organization Admin - StartupInc
```
Username: admin.startup
Email: <EMAIL>
First Name: Sarah
Last Name: Johnson
☑ Email Verified: ON
☑ Enabled: ON

Password: Admin@123
☐ Temporary: OFF

Roles:
- org-admin

Attributes:
organization_id: org-startup
department: Administration
territory: North America
security_clearance: high
data_access_level: organization
cost_center: CC-ADMIN
```

### 4. Sales Manager - TechCorp
```
Username: manager.sales
Email: <EMAIL>
First Name: Michael
Last Name: Brown
☑ Email Verified: ON
☑ Enabled: ON

Password: Manager@123
☐ Temporary: OFF

Roles:
- dept-manager

Attributes:
organization_id: org-techcorp
department: Sales
territory: North America
manager_id: admin.techcorp
security_clearance: standard
data_access_level: department
cost_center: CC-SALES
team_size: 8
```

### 5. Marketing Manager - TechCorp
```
Username: manager.marketing
Email: <EMAIL>
First Name: Lisa
Last Name: Davis
☑ Email Verified: ON
☑ Enabled: ON

Password: Manager@123
☐ Temporary: OFF

Roles:
- dept-manager

Attributes:
organization_id: org-techcorp
department: Marketing
territory: Global
manager_id: admin.techcorp
security_clearance: standard
data_access_level: department
cost_center: CC-MARKETING
team_size: 5
```

### 6. Support Manager - TechCorp
```
Username: manager.support
Email: <EMAIL>
First Name: David
Last Name: Wilson
☑ Email Verified: ON
☑ Enabled: ON

Password: Manager@123
☐ Temporary: OFF

Roles:
- dept-manager

Attributes:
organization_id: org-techcorp
department: Support
territory: Global
manager_id: admin.techcorp
security_clearance: standard
data_access_level: department
cost_center: CC-SUPPORT
team_size: 12
```

### 7. Senior Sales Rep - TechCorp
```
Username: sales.senior
Email: <EMAIL>
First Name: Robert
Last Name: Taylor
☑ Email Verified: ON
☑ Enabled: ON

Password: Sales@123
☐ Temporary: OFF

Roles:
- sales-rep

Attributes:
organization_id: org-techcorp
department: Sales
territory: West Coast
manager_id: manager.sales
security_clearance: standard
data_access_level: assigned
cost_center: CC-SALES
quota: 500000
commission_rate: 0.08
```

### 8. Junior Sales Rep - TechCorp
```
Username: sales.junior
Email: <EMAIL>
First Name: Emily
Last Name: Anderson
☑ Email Verified: ON
☑ Enabled: ON

Password: Sales@123
☐ Temporary: OFF

Roles:
- sales-rep

Attributes:
organization_id: org-techcorp
department: Sales
territory: East Coast
manager_id: manager.sales
security_clearance: standard
data_access_level: assigned
cost_center: CC-SALES
quota: 300000
commission_rate: 0.06
```

### 9. Sales Rep - StartupInc
```
Username: sales.startup
Email: <EMAIL>
First Name: Alex
Last Name: Martinez
☑ Email Verified: ON
☑ Enabled: ON

Password: Sales@123
☐ Temporary: OFF

Roles:
- sales-rep

Attributes:
organization_id: org-startup
department: Sales
territory: North America
manager_id: admin.startup
security_clearance: standard
data_access_level: assigned
cost_center: CC-SALES
quota: 200000
commission_rate: 0.10
```

### 10. Marketing Specialist - TechCorp
```
Username: marketing.specialist
Email: <EMAIL>
First Name: Jessica
Last Name: Lee
☑ Email Verified: ON
☑ Enabled: ON

Password: Marketing@123
☐ Temporary: OFF

Roles:
- marketing-user

Attributes:
organization_id: org-techcorp
department: Marketing
territory: Global
manager_id: manager.marketing
security_clearance: standard
data_access_level: department
cost_center: CC-MARKETING
specialization: Digital Marketing
```

### 11. Support Agent Level 1
```
Username: support.l1
Email: <EMAIL>
First Name: Kevin
Last Name: Garcia
☑ Email Verified: ON
☑ Enabled: ON

Password: Support@123
☐ Temporary: OFF

Roles:
- support-agent

Attributes:
organization_id: org-techcorp
department: Support
territory: North America
manager_id: manager.support
security_clearance: standard
data_access_level: assigned
cost_center: CC-SUPPORT
support_level: L1
shift: Day
```

### 12. Support Agent Level 2
```
Username: support.l2
Email: <EMAIL>
First Name: Amanda
Last Name: Rodriguez
☑ Email Verified: ON
☑ Enabled: ON

Password: Support@123
☐ Temporary: OFF

Roles:
- support-agent

Attributes:
organization_id: org-techcorp
department: Support
territory: Global
manager_id: manager.support
security_clearance: standard
data_access_level: assigned
cost_center: CC-SUPPORT
support_level: L2
shift: Night
```

### 13. Executive Viewer
```
Username: executive.viewer
Email: <EMAIL>
First Name: Richard
Last Name: Thompson
☑ Email Verified: ON
☑ Enabled: ON

Password: Executive@123
☐ Temporary: OFF

Roles:
- viewer

Attributes:
organization_id: org-techcorp
department: Executive
territory: Global
security_clearance: high
data_access_level: organization
cost_center: CC-EXEC
position: VP Sales
```

### 14. External Consultant
```
Username: consultant.external
Email: <EMAIL>
First Name: Maria
Last Name: Gonzalez
☑ Email Verified: ON
☑ Enabled: ON

Password: Consultant@123
☐ Temporary: OFF

Roles:
- viewer

Attributes:
organization_id: org-techcorp
department: Consulting
territory: North America
security_clearance: limited
data_access_level: restricted
cost_center: CC-EXTERNAL
contract_end: 2024-12-31
```

### 15. Intern
```
Username: intern.summer
Email: <EMAIL>
First Name: James
Last Name: Wilson
☑ Email Verified: ON
☑ Enabled: ON

Password: Intern@123
☐ Temporary: OFF

Roles:
- viewer

Attributes:
organization_id: org-techcorp
department: Sales
territory: West Coast
manager_id: sales.senior
security_clearance: limited
data_access_level: restricted
cost_center: CC-SALES
internship_end: 2024-08-31
```

## Bulk User Creation Script

### CSV Format for Import
```csv
username,email,firstName,lastName,enabled,emailVerified,password,roles,organization_id,department,territory,manager_id
superadmin,<EMAIL>,Super,Administrator,true,true,Super@123,super-admin,system,IT,Global,
admin.techcorp,<EMAIL>,John,Smith,true,true,Admin@123,org-admin,org-techcorp,Administration,Global,
manager.sales,<EMAIL>,Michael,Brown,true,true,Manager@123,dept-manager,org-techcorp,Sales,North America,admin.techcorp
sales.senior,<EMAIL>,Robert,Taylor,true,true,Sales@123,sales-rep,org-techcorp,Sales,West Coast,manager.sales
```

## Testing Scenarios

```mermaid
graph TB
    subgraph "Test Scenarios"
        T1[Cross-Organization<br/>Access Test]
        T2[Department<br/>Isolation Test]
        T3[Hierarchical<br/>Access Test]
        T4[Manager<br/>Access Test]
        T5[Role Permission<br/>Test]
        T6[Viewer Restriction<br/>Test]
    end

    subgraph "Test Cases"
        T1 --> TC1[admin.techcorp tries to<br/>access StartupInc data<br/>❌ Should be denied]
        T2 --> TC2[manager.sales tries to<br/>access Support data<br/>❌ Should be denied]
        T3 --> TC3[sales.senior tries to<br/>access sales.junior data<br/>❌ Should be denied]
        T4 --> TC4[manager.sales accesses<br/>all sales team data<br/>✅ Should be allowed]
        T5 --> TC5[support.l1 tries to<br/>create a deal<br/>❌ Should be denied]
        T6 --> TC6[executive.viewer tries to<br/>edit contact<br/>❌ Should be denied]
    end

    style TC1 fill:#ffebee
    style TC2 fill:#ffebee
    style TC3 fill:#ffebee
    style TC4 fill:#e8f5e8
    style TC5 fill:#ffebee
    style TC6 fill:#ffebee
```

### 1. Cross-Organization Access Test
- Login as `admin.techcorp`
- Try to access StartupInc data
- Should be denied

### 2. Department Isolation Test
- Login as `manager.sales`
- Try to access Support department data
- Should be denied

### 3. Hierarchical Access Test
- Login as `sales.senior`
- Try to access `sales.junior` data
- Should be denied (peer level)

### 4. Manager Access Test
- Login as `manager.sales`
- Access all sales team data
- Should be allowed

### 5. Role Permission Test
- Login as `support.l1`
- Try to create a deal
- Should be denied

### 6. Viewer Restriction Test
- Login as `executive.viewer`
- Try to edit contact
- Should be denied (read-only)

## Password Policy Recommendations

```
Minimum Length: 8 characters
Require Uppercase: Yes
Require Lowercase: Yes
Require Numbers: Yes
Require Special Characters: Yes
Password History: 5 passwords
Max Age: 90 days
Account Lockout: 5 failed attempts
```
