# Docker Compose for OneCRM Development Environment

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: onecrm-postgres
    environment:
      POSTGRES_DB: onecrm
      POSTGRES_USER: onecrm
      POSTGRES_PASSWORD: onecrm_dev_password
    ports:
      - "5433:5432"  # Changed from 5432 to 5433 to avoid conflicts
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - onecrm-network

  # Redis for caching and sessions
  redis:
    image: redis:alpine
    container_name: onecrm-redis
    ports:
      - "6380:6379"  # Changed from 6379 to 6380 to avoid conflicts
    volumes:
      - redis_data:/data
    networks:
      - onecrm-network

  # Keycloak for authentication
  keycloak:
    image: quay.io/keycloak/keycloak:22.0
    container_name: onecrm-keycloak
    environment:
      KEYCLOAK_ADMIN: admin
      <PERSON>_ADMIN_PASSWORD: admin
      <PERSON>: postgres
      KC_DB_URL: ****************************************
      KC_DB_USERNAME: onecrm
      KC_DB_PASSWORD: onecrm_dev_password
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8080
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    command: start-dev
    volumes:
      - keycloak_data:/opt/keycloak/data
    networks:
      - onecrm-network

  # Kong Gateway
  kong:
    image: kong/kong-gateway:3.4
    container_name: onecrm-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /kong/kong.yml
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
      KONG_ADMIN_GUI_LISTEN: 0.0.0.0:8002
    ports:
      - "8000:8000"  # Kong proxy
      - "8001:8001"  # Kong admin API
      - "8002:8002"  # Kong Manager
      - "8443:8443"  # Kong proxy SSL
      - "8444:8444"  # Kong admin API SSL
    volumes:
      - ./kong/config/kong.yml:/kong/kong.yml:ro
    networks:
      - onecrm-network

volumes:
  postgres_data:
  redis_data:
  keycloak_data:

networks:
  onecrm-network:
    driver: bridge
