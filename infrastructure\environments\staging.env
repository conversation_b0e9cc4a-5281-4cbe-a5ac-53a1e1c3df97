# OneCRM Staging Environment Configuration
# This file contains non-sensitive configuration for staging

# Application Configuration
NODE_ENV=staging
LOG_LEVEL=debug
DEBUG=true

# Server Configuration
PORT=8000
FRONTEND_PORT=3000
HOST=0.0.0.0

# Domain Configuration
DOMAIN=staging.onecrm.example.com
API_DOMAIN=api-staging.onecrm.example.com
AUTH_DOMAIN=auth-staging.onecrm.example.com
FRONTEND_URL=https://staging.onecrm.example.com
API_URL=https://api-staging.onecrm.example.com
KEYCLOAK_URL=https://auth-staging.onecrm.example.com

# CORS Configuration
CORS_ORIGIN=https://staging.onecrm.example.com
CORS_CREDENTIALS=true

# Database Configuration
DB_HOST=postgres-service
DB_PORT=5432
DB_NAME=onecrm_staging
DB_SSL=false
DB_POOL_SIZE=10
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=10000

# Redis Configuration
REDIS_HOST=redis-service
REDIS_PORT=6379
REDIS_DB=1
REDIS_TTL=1800

# JWT Configuration
JWT_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=1d
JWT_ISSUER=onecrm-staging
JWT_AUDIENCE=onecrm-staging-users

# Keycloak Configuration
KEYCLOAK_REALM=onecrm-staging
KEYCLOAK_CLIENT_ID=onecrm-frontend-staging
KEYCLOAK_ADMIN_CLIENT_ID=onecrm-admin-staging

# Rate Limiting (More lenient for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,csv,txt,zip
UPLOAD_PATH=/tmp/uploads

# Email Configuration (Test SMTP)
EMAIL_ENABLED=true
EMAIL_TEMPLATE_PATH=./templates/email

# Feature Flags (All enabled for testing)
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_FILE_UPLOAD=true
FEATURE_EXPORT=true
FEATURE_IMPORT=true
FEATURE_WEBHOOKS=true
FEATURE_API_RATE_LIMITING=false

# Security Configuration (Relaxed for testing)
HELMET_ENABLED=true
CSRF_ENABLED=false
SESSION_SECURE=false
COOKIE_SECURE=false
COOKIE_SAME_SITE=lax

# Monitoring Configuration
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true
PROMETHEUS_ENABLED=true

# Logging Configuration
LOG_FORMAT=pretty
LOG_TIMESTAMP=true
LOG_COLORIZE=true
LOG_MAX_FILES=5
LOG_MAX_SIZE=5m

# Cache Configuration
CACHE_TTL=60
CACHE_MAX_ITEMS=500

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=7

# Performance Configuration
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=3
KEEP_ALIVE_TIMEOUT=5000
HEADERS_TIMEOUT=60000

# SSL/TLS Configuration
SSL_ENABLED=true
FORCE_HTTPS=false
HSTS_ENABLED=false
HSTS_MAX_AGE=0

# API Configuration
API_VERSION=v1
API_PREFIX=/api
API_DOCUMENTATION_ENABLED=true
API_THROTTLE_TTL=60
API_THROTTLE_LIMIT=1000

# Webhook Configuration
WEBHOOK_TIMEOUT=10000
WEBHOOK_RETRY_ATTEMPTS=1
WEBHOOK_RETRY_DELAY=500

# Search Configuration
SEARCH_ENABLED=true
SEARCH_INDEX_BATCH_SIZE=50

# Notification Configuration
NOTIFICATION_QUEUE_ENABLED=true
NOTIFICATION_BATCH_SIZE=10

# Audit Configuration
AUDIT_ENABLED=true
AUDIT_RETENTION_DAYS=30

# Timezone Configuration
TZ=UTC
DEFAULT_TIMEZONE=UTC

# Locale Configuration
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR,de-DE

# Third-party Integrations (Test keys)
GOOGLE_MAPS_ENABLED=true
STRIPE_ENABLED=true
SENDGRID_ENABLED=false

# Development Tools (Enabled in Staging)
SWAGGER_ENABLED=true
GRAPHQL_PLAYGROUND_ENABLED=true
DEBUG_ROUTES_ENABLED=true

# Test Data Configuration
SEED_TEST_DATA=true
TEST_USER_EMAIL=<EMAIL>
TEST_ORG_NAME=Test Organization

# Performance Testing
LOAD_TEST_ENABLED=true
STRESS_TEST_ENABLED=true
