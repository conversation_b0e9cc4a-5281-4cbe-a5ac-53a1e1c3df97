'use client';

import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  MenuItem,
  Box,
  Alert,
} from '@mui/material';
import { useNotifications } from '../common/NotificationSystem';

interface CompanyFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  initialData?: any;
  company?: any;
  loading?: boolean;
}

const industries = [
  'Technology',
  'Healthcare',
  'Finance',
  'Manufacturing',
  'Retail',
  'Education',
  'Real Estate',
  'Consulting',
  'Other',
];

export const CompanyForm: React.FC<CompanyFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  company,
  loading = false,
}) => {
  const { success, error } = useNotifications();
  const [formData, setFormData] = useState({
    name: initialData?.name || company?.name || '',
    industry: initialData?.industry || company?.industry || '',
    website: initialData?.website || company?.website || '',
    employees: initialData?.employees || company?.employees || '',
    revenue: initialData?.revenue || company?.revenue || '',
    description: initialData?.description || company?.description || '',
    ...initialData,
    ...company,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: event.target.value,
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (!formData.industry) {
      newErrors.industry = 'Industry is required';
    }

    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = 'Please enter a valid website URL';
    }

    if (formData.employees && isNaN(Number(formData.employees))) {
      newErrors.employees = 'Please enter a valid number';
    }

    if (formData.revenue && isNaN(Number(formData.revenue))) {
      newErrors.revenue = 'Please enter a valid number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      error('Please fix the form errors');
      return;
    }

    const submitData = {
      ...formData,
      employees: formData.employees ? Number(formData.employees) : null,
      revenue: formData.revenue ? Number(formData.revenue) : null,
    };

    if (onSubmit) {
      onSubmit(submitData);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      industry: '',
      website: '',
      employees: '',
      revenue: '',
      description: '',
    });
    setErrors({});
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <form onSubmit={handleSubmit}>
        <DialogTitle>
          {initialData ? 'Edit Company' : 'Add New Company'}
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Company Name"
                  value={formData.name}
                  onChange={handleChange('name')}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  select
                  label="Industry"
                  value={formData.industry}
                  onChange={handleChange('industry')}
                  error={!!errors.industry}
                  helperText={errors.industry}
                  required
                >
                  {industries.map((industry) => (
                    <MenuItem key={industry} value={industry}>
                      {industry}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Website"
                  value={formData.website}
                  onChange={handleChange('website')}
                  error={!!errors.website}
                  helperText={errors.website}
                  placeholder="https://example.com"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Number of Employees"
                  type="number"
                  value={formData.employees}
                  onChange={handleChange('employees')}
                  error={!!errors.employees}
                  helperText={errors.employees}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Annual Revenue (USD)"
                  type="number"
                  value={formData.revenue}
                  onChange={handleChange('revenue')}
                  error={!!errors.revenue}
                  helperText={errors.revenue}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  label="Description"
                  value={formData.description}
                  onChange={handleChange('description')}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={loading}
          >
            {loading ? 'Saving...' : (initialData ? 'Update' : 'Create')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
