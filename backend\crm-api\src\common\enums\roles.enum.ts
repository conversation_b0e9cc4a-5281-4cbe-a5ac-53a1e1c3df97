export enum UserRole {
  // System-wide roles
  SUPER_ADMIN = 'super_admin',
  SYSTEM_ADMIN = 'system_admin',
  
  // Organization-level roles
  ORG_ADMIN = 'org_admin',
  ORG_MANAGER = 'org_manager',
  ORG_USER = 'org_user',
  ORG_VIEWER = 'org_viewer',
  
  // Department-specific roles
  SALES_MANAGER = 'sales_manager',
  SALES_REP = 'sales_rep',
  MARKETING_MANAGER = 'marketing_manager',
  MARKETING_USER = 'marketing_user',
  SUPPORT_MANAGER = 'support_manager',
  SUPPORT_AGENT = 'support_agent',
  
  // Special roles
  API_USER = 'api_user',
  GUEST = 'guest'
}

export enum Permission {
  // Organization permissions
  ORG_CREATE = 'org:create',
  ORG_READ = 'org:read',
  ORG_UPDATE = 'org:update',
  ORG_DELETE = 'org:delete',
  ORG_MANAGE_USERS = 'org:manage_users',
  ORG_MANAGE_SETTINGS = 'org:manage_settings',
  
  // User permissions
  USER_CREATE = 'user:create',
  USER_READ = 'user:read',
  USER_UPDATE = 'user:update',
  USER_DELETE = 'user:delete',
  USER_MANAGE_ROLES = 'user:manage_roles',
  
  // Contact permissions
  CONTACT_CREATE = 'contact:create',
  CONTACT_READ = 'contact:read',
  CONTACT_UPDATE = 'contact:update',
  CONTACT_DELETE = 'contact:delete',
  CONTACT_EXPORT = 'contact:export',
  CONTACT_IMPORT = 'contact:import',
  
  // Company permissions
  COMPANY_CREATE = 'company:create',
  COMPANY_READ = 'company:read',
  COMPANY_UPDATE = 'company:update',
  COMPANY_DELETE = 'company:delete',
  COMPANY_EXPORT = 'company:export',
  COMPANY_IMPORT = 'company:import',
  
  // Deal permissions
  DEAL_CREATE = 'deal:create',
  DEAL_READ = 'deal:read',
  DEAL_UPDATE = 'deal:update',
  DEAL_DELETE = 'deal:delete',
  DEAL_MANAGE_PIPELINE = 'deal:manage_pipeline',
  DEAL_VIEW_REPORTS = 'deal:view_reports',
  
  // Activity permissions
  ACTIVITY_CREATE = 'activity:create',
  ACTIVITY_READ = 'activity:read',
  ACTIVITY_UPDATE = 'activity:update',
  ACTIVITY_DELETE = 'activity:delete',
  ACTIVITY_ASSIGN = 'activity:assign',
  
  // Reporting permissions
  REPORT_VIEW_BASIC = 'report:view_basic',
  REPORT_VIEW_ADVANCED = 'report:view_advanced',
  REPORT_CREATE = 'report:create',
  REPORT_EXPORT = 'report:export',
  
  // System permissions
  SYSTEM_ADMIN = 'system:admin',
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_LOGS = 'system:logs',
  SYSTEM_BACKUP = 'system:backup',
  
  // API permissions
  API_ACCESS = 'api:access',
  API_ADMIN = 'api:admin'
}

// Role hierarchy - higher roles inherit permissions from lower roles
export const ROLE_HIERARCHY: Record<UserRole, UserRole[]> = {
  [UserRole.SUPER_ADMIN]: [
    UserRole.SYSTEM_ADMIN,
    UserRole.ORG_ADMIN,
    UserRole.ORG_MANAGER,
    UserRole.ORG_USER,
    UserRole.ORG_VIEWER,
    UserRole.SALES_MANAGER,
    UserRole.SALES_REP,
    UserRole.MARKETING_MANAGER,
    UserRole.MARKETING_USER,
    UserRole.SUPPORT_MANAGER,
    UserRole.SUPPORT_AGENT,
    UserRole.API_USER
  ],
  [UserRole.SYSTEM_ADMIN]: [
    UserRole.ORG_ADMIN,
    UserRole.ORG_MANAGER,
    UserRole.ORG_USER,
    UserRole.ORG_VIEWER,
    UserRole.API_USER
  ],
  [UserRole.ORG_ADMIN]: [
    UserRole.ORG_MANAGER,
    UserRole.ORG_USER,
    UserRole.ORG_VIEWER,
    UserRole.SALES_MANAGER,
    UserRole.SALES_REP,
    UserRole.MARKETING_MANAGER,
    UserRole.MARKETING_USER,
    UserRole.SUPPORT_MANAGER,
    UserRole.SUPPORT_AGENT
  ],
  [UserRole.ORG_MANAGER]: [
    UserRole.ORG_USER,
    UserRole.ORG_VIEWER,
    UserRole.SALES_REP,
    UserRole.MARKETING_USER,
    UserRole.SUPPORT_AGENT
  ],
  [UserRole.ORG_USER]: [UserRole.ORG_VIEWER],
  [UserRole.ORG_VIEWER]: [],
  [UserRole.SALES_MANAGER]: [UserRole.SALES_REP, UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.SALES_REP]: [UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.MARKETING_MANAGER]: [UserRole.MARKETING_USER, UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.MARKETING_USER]: [UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.SUPPORT_MANAGER]: [UserRole.SUPPORT_AGENT, UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.SUPPORT_AGENT]: [UserRole.ORG_USER, UserRole.ORG_VIEWER],
  [UserRole.API_USER]: [UserRole.ORG_VIEWER],
  [UserRole.GUEST]: []
};
