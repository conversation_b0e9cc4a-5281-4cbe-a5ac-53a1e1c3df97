import { <PERSON>String, IsEmail, IsO<PERSON>al, IsUUID, MaxLength, IsArray, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateContactDto {
  @ApiProperty({ description: 'Contact first name', example: '<PERSON>', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  firstName?: string;

  @ApiProperty({ description: 'Contact last name', example: 'Doe', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  lastName?: string;

  @ApiProperty({ description: 'Contact email address', example: '<EMAIL>', required: false })
  @IsOptional()
  @IsEmail()
  @MaxLength(255)
  email?: string;

  @ApiProperty({ description: 'Contact phone number', example: '******-123-4567', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  phone?: string;

  @ApiProperty({ description: 'Company name', example: 'Acme Corporation', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  company?: string;

  @ApiProperty({ description: 'Job title', example: 'Software Engineer', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiProperty({ description: 'Notes about the contact', required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: 'Contact tags', example: ['prospect', 'vip'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Custom fields as key-value pairs', required: false })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Lead source', example: 'website', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  leadSource?: string;

  @ApiProperty({ description: 'Lead status', example: 'qualified', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  leadStatus?: string;

  @ApiProperty({ description: 'User ID to assign contact to', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Company ID to associate contact with', required: false })
  @IsOptional()
  @IsUUID()
  companyId?: string;
}
