'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Fab,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  AccountTree as HierarchyIcon,
} from '@mui/icons-material';
import { CompaniesTable } from '../../components/companies/CompaniesTable';
import { CompaniesStats } from '../../components/companies/CompaniesStats';
import { CompanyForm } from '../../components/companies/CompanyForm';
import { CompanyFilters } from '../../components/companies/CompanyFilters';
import { CompanyHierarchy } from '../../components/companies/CompanyHierarchy';
import { AppLayout } from '../../components/layout/AppLayout';

export default function CompaniesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    industry: '',
    size: '',
    assignedToId: '',
    tags: [] as string[],
    minRevenue: '',
    maxRevenue: '',
    minEmployees: '',
    maxEmployees: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showCompanyForm, setShowCompanyForm] = useState(false);
  const [showHierarchy, setShowHierarchy] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleCreateCompany = () => {
    setSelectedCompany(null);
    setShowCompanyForm(true);
  };

  const handleEditCompany = (company: any) => {
    setSelectedCompany(company);
    setShowCompanyForm(true);
  };

  const handleCloseForm = () => {
    setShowCompanyForm(false);
    setSelectedCompany(null);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleExport = () => {
    handleMenuClose();
    console.log('Export companies');
  };

  const handleImport = () => {
    handleMenuClose();
    console.log('Import companies');
  };

  const handleShowHierarchy = () => {
    handleMenuClose();
    setShowHierarchy(true);
  };

  return (
    <AppLayout>
      <Container maxWidth="xl">
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1" fontWeight={600}>
            Companies
          </Typography>
          
          <Box display="flex" gap={2}>
            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateCompany}
            >
              Add Company
            </Button>
          </Box>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleShowHierarchy}>
              <HierarchyIcon sx={{ mr: 1 }} />
              Company Hierarchy
            </MenuItem>
            <MenuItem onClick={handleExport}>
              <DownloadIcon sx={{ mr: 1 }} />
              Export Companies
            </MenuItem>
            <MenuItem onClick={handleImport}>
              <UploadIcon sx={{ mr: 1 }} />
              Import Companies
            </MenuItem>
          </Menu>
        </Box>

        {/* Stats */}
        <Box mb={3}>
          <CompaniesStats />
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search companies by name, domain, or industry..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box display="flex" gap={1} alignItems="center" flexWrap="wrap">
                  <Button
                    variant={showFilters ? 'contained' : 'outlined'}
                    startIcon={<FilterIcon />}
                    onClick={() => setShowFilters(!showFilters)}
                    size="small"
                  >
                    Filters
                  </Button>
                  
                  {/* Active Filters */}
                  {filters.industry && (
                    <Chip
                      label={`Industry: ${filters.industry}`}
                      onDelete={() => setFilters({ ...filters, industry: '' })}
                      size="small"
                    />
                  )}
                  
                  {filters.size && (
                    <Chip
                      label={`Size: ${filters.size}`}
                      onDelete={() => setFilters({ ...filters, size: '' })}
                      size="small"
                    />
                  )}
                  
                  {filters.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={`Tag: ${tag}`}
                      onDelete={() => setFilters({
                        ...filters,
                        tags: filters.tags.filter(t => t !== tag)
                      })}
                      size="small"
                    />
                  ))}
                </Box>
              </Grid>
            </Grid>

            {/* Expandable Filters */}
            {showFilters && (
              <Box mt={2}>
                <CompanyFilters
                  filters={filters}
                  onChange={handleFilterChange}
                />
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Companies Table */}
        <CompaniesTable
          searchQuery={searchQuery}
          filters={filters}
          onEditCompany={handleEditCompany}
        />

        {/* Floating Action Button for Mobile */}
        <Fab
          color="primary"
          aria-label="add company"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' },
          }}
          onClick={handleCreateCompany}
        >
          <AddIcon />
        </Fab>

        {/* Company Form Dialog */}
        {showCompanyForm && (
          <CompanyForm
            open={showCompanyForm}
            company={selectedCompany}
            onClose={handleCloseForm}
          />
        )}

        {/* Company Hierarchy Dialog */}
        {showHierarchy && (
          <CompanyHierarchy
            open={showHierarchy}
            onClose={() => setShowHierarchy(false)}
          />
        )}
      </Container>
    </AppLayout>
  );
}
