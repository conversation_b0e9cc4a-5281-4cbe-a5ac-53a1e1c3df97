import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { TenantContextService } from '../services/tenant-context.service';

@Injectable()
export class TenantGuard implements CanActivate {
  private readonly logger = new Logger(TenantGuard.name);

  constructor(
    private reflector: Reflector,
    private tenantContextService: TenantContextService,
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();

    // Check if endpoint requires tenant context
    const requiresTenant = this.reflector.getAllAndOverride<boolean>('requiresTenant', [
      context.getHandler(),
      context.getClass(),
    ]);

    // Skip tenant check for public endpoints
    if (requiresTenant === false) {
      this.logger.debug('Tenant guard bypassed for public endpoint');
      return true;
    }

    // Skip tenant check in development mode as per guidelines
    if (process.env.NODE_ENV === 'development') {
      this.logger.debug('Tenant guard bypassed in development mode');
      return true;
    }

    // Check if tenant context was set by the interceptor
    const tenantContext = this.tenantContextService.getContext();

    if (!tenantContext) {
      this.logger.warn('No tenant context found', {
        path: request.path,
        user: request.user,
      });
      throw new ForbiddenException('Organization context required');
    }

    // Validate tenant context
    if (!tenantContext.orgId || !tenantContext.userId) {
      this.logger.warn('Invalid tenant context', {
        tenantContext,
        path: request.path,
      });
      throw new ForbiddenException('Invalid organization context');
    }

    this.logger.debug('Tenant guard passed', {
      orgId: tenantContext.orgId,
      userId: tenantContext.userId,
      role: tenantContext.role,
      path: request.path,
    });

    return true;
  }
}
