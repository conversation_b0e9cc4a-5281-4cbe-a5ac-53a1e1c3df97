import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';

describe('Security Tests', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let jwtService: JwtService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
    jwtService = moduleFixture.get<JwtService>(JwtService);
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Authentication Security', () => {
    it('should reject requests without authentication', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .expect(401);
    });

    it('should reject requests with invalid JWT token', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should reject requests with expired JWT token', async () => {
      // Create an expired token
      const expiredToken = jwtService.sign(
        { sub: 'user-id', orgId: 'org-id' },
        { expiresIn: '-1h' }
      );

      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);
    });

    it('should reject requests with malformed Authorization header', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', 'InvalidFormat token')
        .expect(401);
    });
  });

  describe('Authorization Security', () => {
    const validToken = 'Bearer valid-jwt-token'; // Mock valid token

    it('should reject requests without organization header', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', validToken)
        .expect(403);
    });

    it('should reject requests with invalid organization ID', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', validToken)
        .set('X-Org-Id', 'invalid-org-id')
        .expect(403);
    });

    it('should prevent access to resources from different organization', async () => {
      // This test would require setting up data in different orgs
      // and verifying isolation
      await request(app.getHttpServer())
        .get('/contacts/some-contact-id')
        .set('Authorization', validToken)
        .set('X-Org-Id', 'different-org-id')
        .expect(404); // Should not find resource from different org
    });
  });

  describe('Input Validation Security', () => {
    const validToken = 'Bearer valid-jwt-token';
    const validOrgId = 'valid-org-id';

    it('should prevent SQL injection in search parameters', async () => {
      const sqlInjectionAttempt = "'; DROP TABLE contacts; --";
      
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .query({ search: sqlInjectionAttempt })
        .expect(200); // Should handle safely without error
    });

    it('should prevent XSS in input fields', async () => {
      const xssPayload = '<script>alert("xss")</script>';
      
      await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .send({
          firstName: xssPayload,
          lastName: 'Test',
          email: '<EMAIL>',
        })
        .expect(400); // Should reject invalid input
    });

    it('should validate email format strictly', async () => {
      const invalidEmails = [
        'invalid-email',
        'test@',
        '@example.com',
        '<EMAIL>',
        'test@example',
      ];

      for (const email of invalidEmails) {
        await request(app.getHttpServer())
          .post('/contacts')
          .set('Authorization', validToken)
          .set('X-Org-Id', validOrgId)
          .send({
            firstName: 'Test',
            lastName: 'User',
            email,
          })
          .expect(400);
      }
    });

    it('should prevent oversized payloads', async () => {
      const largeString = 'a'.repeat(10000); // 10KB string
      
      await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .send({
          firstName: largeString,
          lastName: 'Test',
          email: '<EMAIL>',
        })
        .expect(400);
    });
  });

  describe('Rate Limiting Security', () => {
    const validToken = 'Bearer valid-jwt-token';
    const validOrgId = 'valid-org-id';

    it('should implement rate limiting for API endpoints', async () => {
      // Make multiple rapid requests
      const requests = Array.from({ length: 100 }, () =>
        request(app.getHttpServer())
          .get('/contacts')
          .set('Authorization', validToken)
          .set('X-Org-Id', validOrgId)
      );

      const responses = await Promise.all(requests);
      
      // Should have some rate limited responses (429)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Data Exposure Security', () => {
    const validToken = 'Bearer valid-jwt-token';
    const validOrgId = 'valid-org-id';

    it('should not expose sensitive data in error messages', async () => {
      await request(app.getHttpServer())
        .get('/contacts/non-existent-id')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .expect(404)
        .expect((res) => {
          // Error message should not contain sensitive information
          expect(res.body.message).not.toContain('database');
          expect(res.body.message).not.toContain('sql');
          expect(res.body.message).not.toContain('password');
        });
    });

    it('should not expose internal server errors', async () => {
      // Trigger an internal error (this would need specific setup)
      await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .send({
          // Invalid data that might cause internal error
          invalidField: 'value',
        })
        .expect((res) => {
          if (res.status === 500) {
            expect(res.body.message).not.toContain('stack');
            expect(res.body.message).not.toContain('file');
            expect(res.body.message).not.toContain('line');
          }
        });
    });
  });

  describe('CORS Security', () => {
    it('should have proper CORS headers', async () => {
      await request(app.getHttpServer())
        .options('/contacts')
        .expect(200)
        .expect((res) => {
          expect(res.headers['access-control-allow-origin']).toBeDefined();
          expect(res.headers['access-control-allow-methods']).toBeDefined();
          expect(res.headers['access-control-allow-headers']).toBeDefined();
        });
    });

    it('should reject requests from unauthorized origins', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('Origin', 'https://malicious-site.com')
        .expect((res) => {
          // Should not include CORS headers for unauthorized origin
          expect(res.headers['access-control-allow-origin']).not.toBe('https://malicious-site.com');
        });
    });
  });

  describe('Content Security', () => {
    it('should have security headers', async () => {
      await request(app.getHttpServer())
        .get('/health')
        .expect((res) => {
          expect(res.headers['x-content-type-options']).toBe('nosniff');
          expect(res.headers['x-frame-options']).toBe('DENY');
          expect(res.headers['x-xss-protection']).toBe('1; mode=block');
        });
    });

    it('should prevent clickjacking', async () => {
      await request(app.getHttpServer())
        .get('/health')
        .expect((res) => {
          expect(res.headers['x-frame-options']).toBeDefined();
        });
    });
  });

  describe('File Upload Security', () => {
    const validToken = 'Bearer valid-jwt-token';
    const validOrgId = 'valid-org-id';

    it('should validate file types for uploads', async () => {
      // Test with executable file
      await request(app.getHttpServer())
        .post('/media/upload')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .attach('file', Buffer.from('fake exe content'), 'malicious.exe')
        .expect(400);
    });

    it('should limit file size for uploads', async () => {
      // Test with oversized file
      const largeBuffer = Buffer.alloc(50 * 1024 * 1024); // 50MB
      
      await request(app.getHttpServer())
        .post('/media/upload')
        .set('Authorization', validToken)
        .set('X-Org-Id', validOrgId)
        .attach('file', largeBuffer, 'large-file.jpg')
        .expect(413); // Payload too large
    });
  });

  describe('API Versioning Security', () => {
    it('should handle API version headers properly', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('API-Version', 'v1')
        .expect((res) => {
          // Should handle version header without errors
          expect(res.status).not.toBe(500);
        });
    });

    it('should reject unsupported API versions', async () => {
      await request(app.getHttpServer())
        .get('/contacts')
        .set('API-Version', 'v999')
        .expect(400);
    });
  });

  describe('Logging Security', () => {
    it('should not log sensitive information', async () => {
      // This test would require checking actual log output
      // In a real implementation, you'd verify that passwords,
      // tokens, and other sensitive data are not logged
      
      await request(app.getHttpServer())
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'sensitive-password',
        });
      
      // Verify logs don't contain the password
      // This would require access to the logging system
    });
  });
});
