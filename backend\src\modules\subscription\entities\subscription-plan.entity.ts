import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Subscription } from './subscription.entity';

export enum PlanType {
  FREE = 'free',
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom',
}

export enum BillingInterval {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export interface PlanFeatures {
  maxUsers: number;
  maxContacts: number;
  maxDeals: number;
  maxStorage: number; // in GB
  apiCallsPerMonth: number;
  customFields: boolean;
  advancedReporting: boolean;
  integrations: boolean;
  prioritySupport: boolean;
  sso: boolean;
  auditLogs: boolean;
  customBranding: boolean;
  webhooks: boolean;
  apiAccess: boolean;
}

export interface PlanLimits {
  maxUsers: number;
  maxContacts: number;
  maxDeals: number;
  maxStorageGB: number;
  apiCallsPerMonth: number;
  maxCustomFields: number;
  maxIntegrations: number;
  maxWebhooks: number;
}

@Entity('subscription_plans')
export class SubscriptionPlan {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: PlanType,
  })
  type: PlanType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({
    type: 'enum',
    enum: BillingInterval,
    default: BillingInterval.MONTHLY,
  })
  billingInterval: BillingInterval;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripeProductId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  stripePriceId: string;

  @Column({ type: 'json' })
  features: PlanFeatures;

  @Column({ type: 'json' })
  limits: PlanLimits;

  @Column({ type: 'boolean', default: true })
  active: boolean;

  @Column({ type: 'boolean', default: false })
  popular: boolean;

  @Column({ type: 'int', default: 0 })
  sortOrder: number;

  @Column({ type: 'int', nullable: true })
  trialDays: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;

  @OneToMany(() => Subscription, subscription => subscription.plan)
  subscriptions: Subscription[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
