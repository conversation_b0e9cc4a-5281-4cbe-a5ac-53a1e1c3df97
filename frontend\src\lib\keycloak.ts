// Keycloak will be imported dynamically on client side
import Keycloak from 'keycloak-js';

// Keycloak configuration
const keycloakConfig = {
  url: process.env.NEXT_PUBLIC_KEYCLOAK_URL || 'http://localhost:8080',
  realm: process.env.NEXT_PUBLIC_KEYCLOAK_REALM || 'onecrm',
  clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID || 'onecrm-frontend',
};

// Singleton Keycloak instance management
class KeycloakSingleton {
  private static instance: Keycloak | null = null;
  private static isInitialized = false;
  private static isInitializing = false;

  static getInstance(): Keycloak | null {
    // Only run on client side
    if (typeof window === 'undefined') return null;

    // Check if already exists in window (for hot reload and React StrictMode)
    if ((window as any).__keycloak_singleton) {
      this.instance = (window as any).__keycloak_singleton;
      this.isInitialized = true;
      return this.instance;
    }

    // Return existing instance if already created
    if (this.instance && this.isInitialized) {
      return this.instance;
    }

    // Prevent multiple simultaneous initializations
    if (this.isInitializing) {
      return this.instance;
    }

    // Create new instance only if none exists
    if (!this.instance && !this.isInitialized && !this.isInitializing) {
      try {
        this.isInitializing = true;
        this.instance = new Keycloak(keycloakConfig);
        this.isInitialized = true;
        this.isInitializing = false;

        // Store in window for development hot reload and React StrictMode prevention
        (window as any).__keycloak_singleton = this.instance;

        console.log('Keycloak instance created successfully');
      } catch (error) {
        console.error('Failed to create Keycloak instance:', error);
        this.isInitialized = false;
        this.isInitializing = false;
        this.instance = null;
        return null;
      }
    }

    return this.instance;
  }

  static reset() {
    this.instance = null;
    this.isInitialized = false;
    this.isInitializing = false;
    if (typeof window !== 'undefined') {
      delete (window as any).__keycloak_singleton;
    }
  }
}

const getKeycloak = (): Keycloak | null => {
  return KeycloakSingleton.getInstance();
};

// Keycloak initialization options
export const keycloakInitOptions = {
  onLoad: 'check-sso' as const,
  silentCheckSsoRedirectUri: typeof window !== 'undefined' 
    ? `${window.location.origin}/silent-check-sso.html` 
    : undefined,
  pkceMethod: 'S256' as const,
  checkLoginIframe: true,
  checkLoginIframeInterval: 30,
  enableLogging: process.env.NODE_ENV === 'development',
};

// Token refresh configuration
export const keycloakTokenRefreshOptions = {
  minValidity: 30, // Refresh token when it expires in 30 seconds
  timeSkew: 0,
};

// Reset function for development
export const resetKeycloak = () => {
  if (process.env.NODE_ENV === 'development') {
    keycloak = null;
    isInitialized = false;
    if (typeof window !== 'undefined') {
      delete (window as any).__keycloak_instance;
    }
  }
};

export default getKeycloak;
