import { Injectable, NotFoundException, ForbiddenException, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, ILike, In, Between } from 'typeorm';
import { Company } from './company.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';
import { CreateCompanyDto } from './dto/create-company.dto';
import { UpdateCompanyDto } from './dto/update-company.dto';
import { SearchCompaniesDto, CompaniesResponseDto } from './dto/search-companies.dto';

@Injectable()
export class CompaniesService {
  private readonly logger = new Logger(CompaniesService.name);

  constructor(
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Contact)
    private contactsRepository: Repository<Contact>,
    private tenantContextService: TenantContextService,
  ) {}

  async create(createCompanyDto: CreateCompanyDto): Promise<Company> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Validate assigned user belongs to the same organization
    if (createCompanyDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: createCompanyDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate parent company belongs to the same organization
    if (createCompanyDto.parentCompanyId) {
      const parentCompany = await this.companiesRepository.findOne({
        where: { id: createCompanyDto.parentCompanyId, orgId },
      });
      if (!parentCompany) {
        throw new BadRequestException('Parent company not found in your organization');
      }
    }

    const company = this.companiesRepository.create({
      ...createCompanyDto,
      orgId,
      createdById: currentUserId,
      updatedById: currentUserId,
    });

    const savedCompany = await this.companiesRepository.save(company);
    
    this.logger.log(`Company created: ${savedCompany.id} by user ${currentUserId}`);
    
    return this.findById(savedCompany.id);
  }

  async findAll(searchDto: SearchCompaniesDto): Promise<CompaniesResponseDto> {
    const orgId = this.tenantContextService.getOrgId();
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = searchDto;

    const queryBuilder = this.companiesRepository
      .createQueryBuilder('company')
      .leftJoinAndSelect('company.assignedTo', 'assignedTo')
      .leftJoinAndSelect('company.createdBy', 'createdBy')
      .leftJoinAndSelect('company.parentCompany', 'parentCompany')
      .leftJoinAndSelect('company.subsidiaries', 'subsidiaries')
      .leftJoinAndSelect('company.contacts', 'contacts')
      .where('company.orgId = :orgId', { orgId });

    // Apply search filters
    this.applySearchFilters(queryBuilder, searchDto);

    // Apply sorting
    queryBuilder.orderBy(`company.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [companies, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      companies,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findById(id: string): Promise<Company> {
    const orgId = this.tenantContextService.getOrgId();
    
    const company = await this.companiesRepository.findOne({
      where: { id, orgId },
      relations: [
        'assignedTo', 
        'createdBy', 
        'updatedBy', 
        'parentCompany', 
        'subsidiaries',
        'contacts',
        'deals'
      ],
    });

    if (!company) {
      throw new NotFoundException(`Company with ID ${id} not found`);
    }

    return company;
  }

  async update(id: string, updateCompanyDto: UpdateCompanyDto): Promise<Company> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if company exists and belongs to the organization
    const existingCompany = await this.findById(id);

    // Check permissions - users can update companies they created or are assigned to, admins can update all
    const canUpdate = this.tenantContextService.isAdmin() || 
                     existingCompany.createdById === currentUserId ||
                     existingCompany.assignedToId === currentUserId;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this company');
    }

    // Validate assigned user belongs to the same organization
    if (updateCompanyDto.assignedToId) {
      const assignedUser = await this.usersRepository.findOne({
        where: { id: updateCompanyDto.assignedToId, orgId },
      });
      if (!assignedUser) {
        throw new BadRequestException('Assigned user not found in your organization');
      }
    }

    // Validate parent company belongs to the same organization and prevent circular references
    if (updateCompanyDto.parentCompanyId) {
      if (updateCompanyDto.parentCompanyId === id) {
        throw new BadRequestException('Company cannot be its own parent');
      }

      const parentCompany = await this.companiesRepository.findOne({
        where: { id: updateCompanyDto.parentCompanyId, orgId },
      });
      if (!parentCompany) {
        throw new BadRequestException('Parent company not found in your organization');
      }

      // Check for circular reference
      if (await this.wouldCreateCircularReference(id, updateCompanyDto.parentCompanyId)) {
        throw new BadRequestException('This would create a circular reference in the company hierarchy');
      }
    }

    await this.companiesRepository.update(
      { id, orgId },
      { ...updateCompanyDto, updatedById: currentUserId }
    );

    this.logger.log(`Company updated: ${id} by user ${currentUserId}`);

    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if company exists and belongs to the organization
    const existingCompany = await this.findById(id);

    // Check permissions - users can delete companies they created, admins can delete all
    const canDelete = this.tenantContextService.isAdmin() || 
                     existingCompany.createdById === currentUserId;

    if (!canDelete) {
      throw new ForbiddenException('You do not have permission to delete this company');
    }

    // Check if company has subsidiaries
    const subsidiariesCount = await this.companiesRepository.count({
      where: { parentCompanyId: id, orgId },
    });

    if (subsidiariesCount > 0) {
      throw new BadRequestException('Cannot delete company with subsidiaries. Please reassign or delete subsidiaries first.');
    }

    // Soft delete
    await this.companiesRepository.softDelete({ id, orgId });

    this.logger.log(`Company deleted: ${id} by user ${currentUserId}`);
  }

  async getCompanyStats(): Promise<{
    total: number;
    byIndustry: Record<string, number>;
    bySize: Record<string, number>;
    totalRevenue: number;
    averageRevenue: number;
    recentlyCreated: number;
  }> {
    const orgId = this.tenantContextService.getOrgId();

    // Get total count
    const total = await this.companiesRepository.count({ where: { orgId } });

    // Get counts by industry
    const industryCounts = await this.companiesRepository
      .createQueryBuilder('company')
      .select('company.industry', 'industry')
      .addSelect('COUNT(*)', 'count')
      .where('company.orgId = :orgId', { orgId })
      .groupBy('company.industry')
      .getRawMany();

    const byIndustry = industryCounts.reduce((acc, item) => {
      acc[item.industry || 'unknown'] = parseInt(item.count);
      return acc;
    }, {});

    // Get counts by size
    const sizeCounts = await this.companiesRepository
      .createQueryBuilder('company')
      .select('company.size', 'size')
      .addSelect('COUNT(*)', 'count')
      .where('company.orgId = :orgId', { orgId })
      .groupBy('company.size')
      .getRawMany();

    const bySize = sizeCounts.reduce((acc, item) => {
      acc[item.size || 'unknown'] = parseInt(item.count);
      return acc;
    }, {});

    // Get revenue statistics
    const revenueStats = await this.companiesRepository
      .createQueryBuilder('company')
      .select('SUM(company.annualRevenue)', 'totalRevenue')
      .addSelect('AVG(company.annualRevenue)', 'averageRevenue')
      .where('company.orgId = :orgId', { orgId })
      .andWhere('company.annualRevenue IS NOT NULL')
      .getRawOne();

    // Get recently created count (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentlyCreated = await this.companiesRepository.count({
      where: {
        orgId,
        createdAt: { $gte: thirtyDaysAgo } as any,
      },
    });

    return {
      total,
      byIndustry,
      bySize,
      totalRevenue: parseFloat(revenueStats?.totalRevenue || '0'),
      averageRevenue: parseFloat(revenueStats?.averageRevenue || '0'),
      recentlyCreated,
    };
  }

  private async wouldCreateCircularReference(companyId: string, parentId: string): Promise<boolean> {
    const orgId = this.tenantContextService.getOrgId();
    
    // Check if the proposed parent has the current company as an ancestor
    let currentParentId = parentId;
    const visited = new Set<string>();

    while (currentParentId && !visited.has(currentParentId)) {
      if (currentParentId === companyId) {
        return true; // Circular reference detected
      }

      visited.add(currentParentId);

      const parent = await this.companiesRepository.findOne({
        where: { id: currentParentId, orgId },
        select: ['parentCompanyId'],
      });

      currentParentId = parent?.parentCompanyId || null;
    }

    return false;
  }

  private applySearchFilters(
    queryBuilder: SelectQueryBuilder<Company>,
    searchDto: SearchCompaniesDto
  ): void {
    const { 
      search, 
      industry, 
      size, 
      assignedToId, 
      tags, 
      minRevenue,
      maxRevenue,
      minEmployees,
      maxEmployees,
      includeDeleted 
    } = searchDto;

    // Include deleted companies if requested
    if (!includeDeleted) {
      queryBuilder.andWhere('company.deletedAt IS NULL');
    }

    // General search across name, domain, and industry
    if (search) {
      queryBuilder.andWhere(
        '(company.name ILIKE :search OR company.domain ILIKE :search OR company.industry ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Filter by industry
    if (industry) {
      queryBuilder.andWhere('company.industry ILIKE :industry', { industry: `%${industry}%` });
    }

    // Filter by size
    if (size) {
      queryBuilder.andWhere('company.size = :size', { size });
    }

    // Filter by assigned user
    if (assignedToId) {
      queryBuilder.andWhere('company.assignedToId = :assignedToId', { assignedToId });
    }

    // Filter by tags
    if (tags && tags.length > 0) {
      queryBuilder.andWhere('company.tags && :tags', { tags });
    }

    // Filter by revenue range
    if (minRevenue !== undefined || maxRevenue !== undefined) {
      if (minRevenue !== undefined && maxRevenue !== undefined) {
        queryBuilder.andWhere('company.annualRevenue BETWEEN :minRevenue AND :maxRevenue', {
          minRevenue,
          maxRevenue,
        });
      } else if (minRevenue !== undefined) {
        queryBuilder.andWhere('company.annualRevenue >= :minRevenue', { minRevenue });
      } else if (maxRevenue !== undefined) {
        queryBuilder.andWhere('company.annualRevenue <= :maxRevenue', { maxRevenue });
      }
    }

    // Filter by employee count range
    if (minEmployees !== undefined || maxEmployees !== undefined) {
      if (minEmployees !== undefined && maxEmployees !== undefined) {
        queryBuilder.andWhere('company.employeeCount BETWEEN :minEmployees AND :maxEmployees', {
          minEmployees,
          maxEmployees,
        });
      } else if (minEmployees !== undefined) {
        queryBuilder.andWhere('company.employeeCount >= :minEmployees', { minEmployees });
      } else if (maxEmployees !== undefined) {
        queryBuilder.andWhere('company.employeeCount <= :maxEmployees', { maxEmployees });
      }
    }
  }

  /**
   * Test method to get companies data directly without tenant context
   */
  async getTestData() {
    try {
      const companies = await this.companiesRepository
        .createQueryBuilder('company')
        .leftJoinAndSelect('company.assignedTo', 'assignedTo')
        .leftJoinAndSelect('company.createdBy', 'createdBy')
        .leftJoinAndSelect('company.contacts', 'contacts')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: companies.length,
        data: companies,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
