import { Injectable, NotFoundException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';
import { Organization } from '../organizations/organization.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Organization)
    private organizationsRepository: Repository<Organization>,
    private tenantContextService: TenantContextService,
  ) {}

  async findByKeycloakSub(keycloakSub: string): Promise<User | null> {
    return this.usersRepository.findOne({
      where: { keycloakSub },
      relations: ['organization'],
    });
  }

  async findById(id: string, orgId?: string): Promise<User> {
    const tenantOrgId = orgId || this.tenantContextService.getOrgId();

    const user = await this.usersRepository.findOne({
      where: { id, orgId: tenantOrgId },
      relations: ['organization'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    // Validate tenant access
    if (!this.tenantContextService.validateTenantResource(user.orgId)) {
      throw new ForbiddenException('Access denied to this user');
    }

    return user;
  }

  async findByOrganization(orgId?: string): Promise<User[]> {
    const tenantOrgId = orgId || this.tenantContextService.getOrgId();

    return this.usersRepository.find({
      where: { orgId: tenantOrgId },
      relations: ['organization'],
      order: { createdAt: 'DESC' },
    });
  }

  async createFromKeycloak(keycloakData: {
    sub: string;
    email: string;
    given_name?: string;
    family_name?: string;
    org_id: string;
    role?: string;
  }): Promise<User> {
    this.logger.log(`Creating user from Keycloak data: ${keycloakData.email}`);

    // Find or create organization
    let organization = await this.organizationsRepository.findOne({
      where: { id: keycloakData.org_id },
    });

    if (!organization) {
      // Create default organization if it doesn't exist
      organization = this.organizationsRepository.create({
        id: keycloakData.org_id,
        name: 'Default Organization',
        slug: 'default-org',
        plan: 'free',
      });
      organization = await this.organizationsRepository.save(organization);
    }

    // Create user
    const user = this.usersRepository.create({
      keycloakSub: keycloakData.sub,
      email: keycloakData.email,
      firstName: keycloakData.given_name,
      lastName: keycloakData.family_name,
      orgId: keycloakData.org_id,
      role: keycloakData.role || 'user',
      isActive: true,
      lastLoginAt: new Date(),
    });

    const savedUser = await this.usersRepository.save(user);
    
    this.logger.log(`User created successfully: ${savedUser.id}`);
    
    return this.findById(savedUser.id, savedUser.orgId);
  }

  async updateLastLogin(id: string, orgId: string): Promise<void> {
    await this.usersRepository.update(
      { id, orgId },
      { lastLoginAt: new Date() }
    );
  }

  async updateProfile(
    id: string,
    updateData: Partial<Pick<User, 'firstName' | 'lastName'>>,
    orgId?: string
  ): Promise<User> {
    const tenantOrgId = orgId || this.tenantContextService.getOrgId();

    // Validate that user can update this profile
    const currentUserId = this.tenantContextService.getUserId();
    const isAdmin = this.tenantContextService.isAdmin();

    if (!isAdmin && currentUserId !== id) {
      throw new ForbiddenException('You can only update your own profile');
    }

    await this.usersRepository.update({ id, orgId: tenantOrgId }, updateData);
    return this.findById(id, tenantOrgId);
  }

  async deactivateUser(id: string, orgId?: string): Promise<void> {
    const tenantOrgId = orgId || this.tenantContextService.getOrgId();

    // Only admins can deactivate users
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can deactivate users');
    }

    await this.usersRepository.update(
      { id, orgId: tenantOrgId },
      { isActive: false }
    );
  }

  async activateUser(id: string, orgId?: string): Promise<void> {
    const tenantOrgId = orgId || this.tenantContextService.getOrgId();

    // Only admins can activate users
    if (!this.tenantContextService.isAdmin()) {
      throw new ForbiddenException('Only administrators can activate users');
    }

    await this.usersRepository.update(
      { id, orgId: tenantOrgId },
      { isActive: true }
    );
  }

  /**
   * Test method to get users data directly without tenant context
   */
  async getTestData() {
    try {
      const users = await this.usersRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.organization', 'organization')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: users.length,
        data: users,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
