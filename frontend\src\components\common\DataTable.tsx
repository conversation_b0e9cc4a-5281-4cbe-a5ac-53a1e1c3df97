'use client';

import React, { useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
  PaginationState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TextField,
  Box,
  IconButton,
  Typography,
  TablePagination,
  Chip,
} from '@mui/material';
import {
  ArrowUpward,
  ArrowDownward,
  FilterList,
  Search,
} from '@mui/icons-material';

interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  title?: string;
  searchable?: boolean;
  filterable?: boolean;
  pagination?: boolean;
  pageSize?: number;
  loading?: boolean;
  onRowClick?: (row: T) => void;
  onRowSelect?: (selectedRows: T[]) => void;
}

export function DataTable<T>({
  data,
  columns,
  title,
  searchable = true,
  filterable = true,
  pagination = true,
  pageSize = 10,
  loading = false,
  onRowClick,
  onRowSelect,
}: DataTableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [paginationState, setPaginationState] = useState<PaginationState>({
    pageIndex: 0,
    pageSize,
  });

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination: paginationState,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onPaginationChange: setPaginationState,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleRowClick = (row: T) => {
    if (onRowClick) {
      onRowClick(row);
    }
  };

  return (
    <Paper elevation={1} sx={{ width: '100%' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          {title && (
            <Typography variant="h6" component="h2">
              {title}
            </Typography>
          )}
          
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <Chip 
              label={`${table.getFilteredRowModel().rows.length} records`} 
              size="small" 
              variant="outlined" 
            />
          </Box>
        </Box>

        {/* Global Search */}
        {searchable && (
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="Search all columns..."
              value={globalFilter ?? ''}
              onChange={(e) => setGlobalFilter(e.target.value)}
              size="small"
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              sx={{ minWidth: 300 }}
            />
          </Box>
        )}
      </Box>

      {/* Table */}
      <TableContainer>
        <Table stickyHeader>
          <TableHead>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableCell
                    key={header.id}
                    sx={{
                      cursor: header.column.getCanSort() ? 'pointer' : 'default',
                      userSelect: 'none',
                      fontWeight: 'bold',
                      backgroundColor: 'grey.50',
                    }}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      
                      {header.column.getCanSort() && (
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          {header.column.getIsSorted() === 'asc' && (
                            <ArrowUpward sx={{ fontSize: 16 }} />
                          )}
                          {header.column.getIsSorted() === 'desc' && (
                            <ArrowDownward sx={{ fontSize: 16 }} />
                          )}
                          {!header.column.getIsSorted() && (
                            <FilterList sx={{ fontSize: 16, opacity: 0.3 }} />
                          )}
                        </Box>
                      )}
                    </Box>

                    {/* Column Filter */}
                    {filterable && header.column.getCanFilter() && (
                      <TextField
                        placeholder={`Filter ${header.column.columnDef.header}...`}
                        value={(header.column.getFilterValue() ?? '') as string}
                        onChange={(e) => header.column.setFilterValue(e.target.value)}
                        size="small"
                        sx={{ mt: 1, width: '100%' }}
                        onClick={(e) => e.stopPropagation()}
                      />
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>

          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center" sx={{ py: 4 }}>
                  <Typography color="text.secondary">Loading...</Typography>
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center" sx={{ py: 4 }}>
                  <Typography color="text.secondary">No data available</Typography>
                </TableCell>
              </TableRow>
            ) : (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  hover
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: onRowClick ? 'action.hover' : 'inherit',
                    },
                  }}
                  onClick={() => handleRowClick(row.original)}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {pagination && (
        <TablePagination
          component="div"
          count={table.getFilteredRowModel().rows.length}
          page={paginationState.pageIndex}
          onPageChange={(_, newPage) =>
            setPaginationState((prev) => ({ ...prev, pageIndex: newPage }))
          }
          rowsPerPage={paginationState.pageSize}
          onRowsPerPageChange={(e) =>
            setPaginationState((prev) => ({
              ...prev,
              pageSize: parseInt(e.target.value, 10),
              pageIndex: 0,
            }))
          }
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
        />
      )}
    </Paper>
  );
}

// Helper function to create common column definitions
export const createColumnHelper = <T,>() => {
  return {
    accessor: (accessor: keyof T, options?: Partial<ColumnDef<T>>) => ({
      accessorKey: accessor as string,
      ...options,
    }),
    display: (options: Partial<ColumnDef<T>>) => ({
      ...options,
    }),
  };
};
