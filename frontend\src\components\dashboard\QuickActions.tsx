'use client';

import React from 'react';
import {
  Box,
  Button,
  Stack,
  Typography,
  Divider,
} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  AttachMoney as MoneyIcon,
  Search as SearchIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';

export const QuickActions: React.FC = () => {
  const router = useRouter();

  const actions = [
    {
      label: 'Add Contact',
      icon: <PersonIcon />,
      color: 'primary' as const,
      onClick: () => router.push('/contacts?action=add'),
    },
    {
      label: 'Add Company',
      icon: <BusinessIcon />,
      color: 'secondary' as const,
      onClick: () => router.push('/companies?action=add'),
    },
    {
      label: 'Create Deal',
      icon: <MoneyIcon />,
      color: 'success' as const,
      onClick: () => router.push('/deals?action=add'),
    },
  ];

  const secondaryActions = [
    {
      label: 'Global Search',
      icon: <SearchIcon />,
      onClick: () => router.push('/search'),
    },
    {
      label: 'View Analytics',
      icon: <AnalyticsIcon />,
      onClick: () => router.push('/analytics'),
    },
  ];

  return (
    <Box>
      <Stack spacing={2}>
        {/* Primary Actions */}
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="contained"
            color={action.color}
            startIcon={action.icon}
            fullWidth
            onClick={action.onClick}
            sx={{
              justifyContent: 'flex-start',
              py: 1.5,
              textTransform: 'none',
            }}
          >
            {action.label}
          </Button>
        ))}

        <Divider sx={{ my: 2 }} />

        {/* Secondary Actions */}
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          Quick Access
        </Typography>
        
        {secondaryActions.map((action, index) => (
          <Button
            key={index}
            variant="outlined"
            startIcon={action.icon}
            fullWidth
            onClick={action.onClick}
            sx={{
              justifyContent: 'flex-start',
              py: 1,
              textTransform: 'none',
              color: 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                borderColor: 'primary.main',
                backgroundColor: 'primary.50',
              },
            }}
          >
            {action.label}
          </Button>
        ))}

        <Divider sx={{ my: 2 }} />

        {/* Import/Export */}
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          Data Management
        </Typography>
        
        <Button
          variant="text"
          size="small"
          onClick={() => router.push('/import')}
          sx={{ textTransform: 'none', justifyContent: 'flex-start' }}
        >
          Import Data
        </Button>
        
        <Button
          variant="text"
          size="small"
          onClick={() => router.push('/export')}
          sx={{ textTransform: 'none', justifyContent: 'flex-start' }}
        >
          Export Data
        </Button>
      </Stack>
    </Box>
  );
};
