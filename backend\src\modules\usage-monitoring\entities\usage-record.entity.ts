import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';

export enum UsageMetricType {
  USERS = 'users',
  CONTACTS = 'contacts',
  COMPANIES = 'companies',
  DEALS = 'deals',
  ACTIVITIES = 'activities',
  API_CALLS = 'api_calls',
  STORAGE_BYTES = 'storage_bytes',
  EMAIL_SENDS = 'email_sends',
  WEBHOOKS = 'webhooks',
  INTEGRATIONS = 'integrations',
  CUSTOM_FIELDS = 'custom_fields',
  REPORTS_GENERATED = 'reports_generated',
  EXPORTS = 'exports',
  IMPORTS = 'imports',
}

export enum UsageRecordType {
  SNAPSHOT = 'snapshot',
  INCREMENT = 'increment',
  DECREMENT = 'decrement',
  RESET = 'reset',
}

@Entity('usage_records')
@Index(['organizationId', 'metricType', 'recordedAt'])
@Index(['organizationId', 'metricType', 'period'])
export class UsageRecord {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({
    type: 'enum',
    enum: UsageMetricType,
  })
  metricType: UsageMetricType;

  @Column({
    type: 'enum',
    enum: UsageRecordType,
    default: UsageRecordType.SNAPSHOT,
  })
  recordType: UsageRecordType;

  @Column({ type: 'bigint' })
  value: number;

  @Column({ type: 'bigint', nullable: true })
  previousValue: number;

  @Column({ type: 'varchar', length: 50 })
  period: string; // e.g., '2024-01', '2024-01-15', '2024-01-15-14'

  @Column({ type: 'timestamp', name: 'recorded_at' })
  recordedAt: Date;

  @Column({ type: 'json', nullable: true })
  metadata: {
    source?: string;
    userId?: string;
    endpoint?: string;
    userAgent?: string;
    ipAddress?: string;
    additionalData?: Record<string, any>;
  };

  @Column({ type: 'varchar', length: 255, nullable: true })
  source: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
