import { registerAs } from '@nestjs/config';

export const keycloakConfig = registerAs('keycloak', () => ({
  authServerUrl: process.env.KEYCLOAK_URL || 'https://stgsso.cubeone.in',
  realm: process.env.KEYCLOAK_REALM || 'Service',
  clientId: process.env.KEYCLOAK_CLIENT_ID || 'onecrm-backend',
  secret: process.env.KEYCLOAK_CLIENT_SECRET || 'hhj319oCJP1JkFks9G8L7unkAwzbtfFn',
  // Disable auth in development mode as per guidelines
  policyEnforcement: process.env.NODE_ENV === 'development' ? 'PERMISSIVE' : 'ENFORCING',
  cookieKey: 'KEYCLOAK_JWT',
  logLevels: ['verbose'],
  useNestLogger: true,
  bearerOnly: true,
  serverUrl: process.env.KEYCLOAK_URL || 'https://stgsso.cubeone.in',
  realmPublicKey: process.env.KEYCLOAK_REALM_PUBLIC_KEY,
}));
