import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { FeatureGatingService } from '../feature-gating.service';
import { FEATURE_GATE_KEY, FeatureGateOptions } from '../decorators/feature-gate.decorator';

@Injectable()
export class FeatureGateGuard implements CanActivate {
  private readonly logger = new Logger(FeatureGateGuard.name);

  constructor(
    private readonly reflector: Reflector,
    private readonly featureGatingService: FeatureGatingService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const featureGateOptions = this.reflector.getAllAndOverride<FeatureGateOptions>(
      FEATURE_GATE_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!featureGateOptions) {
      // No feature gate applied, allow access
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    const organizationId = user?.organizationId || request.headers['x-organization-id'];

    if (!organizationId) {
      this.logger.warn('Feature gate check failed: No organization ID found');
      throw new ForbiddenException('Organization context required');
    }

    try {
      const featureCheck = await this.featureGatingService.checkFeature({
        organizationId,
        featureKey: featureGateOptions.feature,
        userId: user?.id,
        context: {
          endpoint: request.route?.path,
          method: request.method,
          userAgent: request.headers['user-agent'],
          ip: request.ip,
        },
      });

      // Check if feature is enabled
      if (!featureCheck.enabled) {
        return this.handleFeatureDisabled(featureGateOptions, featureCheck);
      }

      // Check required value if specified
      if (featureGateOptions.requireValue !== undefined) {
        if (featureCheck.value !== featureGateOptions.requireValue) {
          return this.handleFeatureDisabled(featureGateOptions, featureCheck);
        }
      }

      // Check trial restrictions
      if (!featureGateOptions.allowTrial && featureCheck.metadata?.daysUntilExpiry !== undefined) {
        const isInTrial = featureCheck.metadata.daysUntilExpiry > 0;
        if (isInTrial) {
          return this.handleFeatureDisabled(featureGateOptions, {
            ...featureCheck,
            reason: 'Feature not available during trial period',
          });
        }
      }

      // Feature check passed
      this.logger.debug(`Feature gate passed: ${featureGateOptions.feature} for organization ${organizationId}`);
      
      // Add feature info to request for potential use in controllers
      request.featureInfo = {
        feature: featureGateOptions.feature,
        value: featureCheck.value,
        metadata: featureCheck.metadata,
      };

      return true;

    } catch (error) {
      this.logger.error(`Feature gate check failed: ${error.message}`, error.stack);
      
      if (featureGateOptions.gracefulDegradation) {
        // Allow access but mark as degraded
        request.featureDegraded = true;
        request.featureInfo = {
          feature: featureGateOptions.feature,
          degraded: true,
          error: error.message,
        };
        return true;
      }

      throw new ForbiddenException('Feature access check failed');
    }
  }

  private handleFeatureDisabled(
    options: FeatureGateOptions,
    featureCheck: any,
  ): boolean {
    if (options.gracefulDegradation) {
      // Allow access but indicate feature is disabled
      return true;
    }

    // Determine error message
    let message = options.customMessage || featureCheck.reason || 'Feature not available';
    
    if (options.upgradePrompt && featureCheck.metadata?.upgradeUrl) {
      message += `. Upgrade your plan to access this feature.`;
    }

    // Log the denial
    this.logger.warn(`Feature gate denied: ${options.feature} - ${message}`);

    // Create detailed error response
    const errorData: any = {
      feature: options.feature,
      reason: featureCheck.reason,
    };

    if (featureCheck.metadata) {
      errorData.metadata = featureCheck.metadata;
    }

    if (options.upgradePrompt) {
      errorData.upgradeRequired = true;
      errorData.upgradeUrl = featureCheck.metadata?.upgradeUrl || '/billing/upgrade';
    }

    throw new ForbiddenException({
      message,
      error: 'Feature Not Available',
      statusCode: 403,
      data: errorData,
    });
  }
}
