import { test, expect } from '@playwright/test'

test.describe('Detailed UI Positioning and Layout Check', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize({ width: 1920, height: 1080 })
  })

  test('Check sidebar positioning and layout', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check sidebar is properly positioned
    const sidebar = page.locator('[data-testid="sidebar"]')
    await expect(sidebar).toBeVisible()
    
    // Check sidebar width and positioning
    const sidebarBox = await sidebar.boundingBox()
    expect(sidebarBox?.width).toBeGreaterThan(250) // Should be around 288px (w-72)
    expect(sidebarBox?.x).toBe(0) // Should be at left edge
    
    // Check sidebar navigation items
    const navItems = sidebar.locator('a, button').filter({ hasText: /Dashboard|Contacts|Companies|Deals/ })
    const navCount = await navItems.count()
    expect(navCount).toBeGreaterThan(3)
    
    // Check each nav item is properly positioned
    for (let i = 0; i < navCount; i++) {
      const item = navItems.nth(i)
      await expect(item).toBeVisible()
      const itemBox = await item.boundingBox()
      expect(itemBox?.width).toBeGreaterThan(200) // Should span most of sidebar width
    }
    
    console.log('Sidebar positioning check passed')
  })

  test('Check main content area positioning', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check main content is properly offset from sidebar
    const mainContent = page.locator('main')
    await expect(mainContent).toBeVisible()
    
    const mainBox = await mainContent.boundingBox()
    expect(mainBox?.x).toBeGreaterThan(250) // Should be offset by sidebar width
    
    // Check content container
    const container = mainContent.locator('.mx-auto').first()
    await expect(container).toBeVisible()
    
    const containerBox = await container.boundingBox()
    expect(containerBox?.width).toBeLessThan(1400) // Should have max-width constraint
    
    console.log('Main content positioning check passed')
  })

  test('Check metric cards grid layout', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    const metricsGrid = page.locator('[data-testid="metric-cards"]')
    await expect(metricsGrid).toBeVisible()
    
    // Check grid has proper spacing
    const gridBox = await metricsGrid.boundingBox()
    expect(gridBox?.width).toBeGreaterThan(800) // Should span available width
    
    // Check individual metric cards
    const metricCards = metricsGrid.locator('> div')
    const cardCount = await metricCards.count()
    expect(cardCount).toBe(4) // Should have 4 metric cards
    
    // Check cards are properly spaced
    for (let i = 0; i < cardCount; i++) {
      const card = metricCards.nth(i)
      await expect(card).toBeVisible()
      
      const cardBox = await card.boundingBox()
      expect(cardBox?.height).toBeGreaterThan(100) // Should have reasonable height
      expect(cardBox?.width).toBeGreaterThan(200) // Should have reasonable width
    }
    
    console.log('Metric cards layout check passed')
  })

  test('Check responsive layout on tablet', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 })
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // On tablet, sidebar should be hidden and main content should span full width
    const sidebar = page.locator('[data-testid="sidebar"]')
    await expect(sidebar).not.toBeVisible()
    
    // Check mobile menu button is visible
    const mobileMenuButton = page.locator('button[aria-label="Open navigation menu"]')
    await expect(mobileMenuButton).toBeVisible()
    
    // Check main content spans full width
    const mainContent = page.locator('main')
    const mainBox = await mainContent.boundingBox()
    expect(mainBox?.x).toBeLessThan(50) // Should be near left edge
    
    // Check metric cards adapt to smaller screen
    const metricsGrid = page.locator('[data-testid="metric-cards"]')
    await expect(metricsGrid).toBeVisible()
    
    console.log('Tablet responsive layout check passed')
  })

  test('Check responsive layout on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check mobile menu button is visible
    const mobileMenuButton = page.locator('button[aria-label="Open navigation menu"]')
    await expect(mobileMenuButton).toBeVisible()
    
    // Check metric cards stack vertically on mobile
    const metricsGrid = page.locator('[data-testid="metric-cards"]')
    await expect(metricsGrid).toBeVisible()
    
    const metricCards = metricsGrid.locator('> div')
    const firstCard = metricCards.first()
    const secondCard = metricCards.nth(1)
    
    const firstCardBox = await firstCard.boundingBox()
    const secondCardBox = await secondCard.boundingBox()
    
    // On mobile, cards should stack vertically (second card should be below first)
    expect(secondCardBox?.y).toBeGreaterThan((firstCardBox?.y || 0) + (firstCardBox?.height || 0))
    
    console.log('Mobile responsive layout check passed')
  })

  test('Check form components alignment', async ({ page }) => {
    await page.goto('/component-test')
    await page.waitForLoadState('networkidle')
    
    // Check form is properly aligned
    const form = page.locator('form').first()
    await expect(form).toBeVisible()
    
    // Check form fields are properly spaced
    const formFields = form.locator('label')
    const fieldCount = await formFields.count()
    expect(fieldCount).toBeGreaterThan(3)
    
    // Check each field has proper spacing
    for (let i = 0; i < Math.min(fieldCount, 3); i++) {
      const field = formFields.nth(i)
      await expect(field).toBeVisible()
      
      const fieldBox = await field.boundingBox()
      expect(fieldBox?.width).toBeGreaterThan(100)
    }
    
    console.log('Form components alignment check passed')
  })

  test('Check button components spacing', async ({ page }) => {
    await page.goto('/component-test')
    await page.waitForLoadState('networkidle')
    
    // Find button sections
    const buttonSections = page.locator('h3:has-text("Button")').locator('..')
    const sectionCount = await buttonSections.count()
    expect(sectionCount).toBeGreaterThan(0)
    
    // Check first button section
    const firstSection = buttonSections.first()
    const buttons = firstSection.locator('button')
    const buttonCount = await buttons.count()
    expect(buttonCount).toBeGreaterThan(3)
    
    // Check buttons have proper spacing
    for (let i = 0; i < Math.min(buttonCount, 3); i++) {
      const button = buttons.nth(i)
      await expect(button).toBeVisible()
      
      const buttonBox = await button.boundingBox()
      expect(buttonBox?.height).toBeGreaterThan(30) // Reasonable button height
      expect(buttonBox?.width).toBeGreaterThan(60) // Reasonable button width
    }
    
    console.log('Button components spacing check passed')
  })

  test('Check header and navigation alignment', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check page header
    const pageHeader = page.locator('h1').first()
    await expect(pageHeader).toBeVisible()
    await expect(pageHeader).toHaveText('UI Test Dashboard')
    
    // Check header is properly positioned
    const headerBox = await pageHeader.boundingBox()
    expect(headerBox?.x).toBeGreaterThan(300) // Should be in main content area
    
    // Check actions in header
    const headerActions = page.locator('h1').locator('..').locator('..').locator('div').last()
    await expect(headerActions).toBeVisible()
    
    console.log('Header and navigation alignment check passed')
  })

  test('Check card components layout consistency', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check all cards have consistent styling
    const cards = page.locator('[class*="border"][class*="rounded"]').filter({ hasText: /Recent Activity|Quick Actions|Analytics/ })
    const cardCount = await cards.count()
    expect(cardCount).toBeGreaterThan(2)
    
    // Check each card has proper dimensions and spacing
    for (let i = 0; i < cardCount; i++) {
      const card = cards.nth(i)
      await expect(card).toBeVisible()
      
      const cardBox = await card.boundingBox()
      expect(cardBox?.height).toBeGreaterThan(200) // Cards should have reasonable height
      expect(cardBox?.width).toBeGreaterThan(250) // Cards should have reasonable width
    }
    
    console.log('Card components layout consistency check passed')
  })

  test('Check spacing and margins throughout the page', async ({ page }) => {
    await page.goto('/ui-test')
    await page.waitForLoadState('networkidle')
    
    // Check main container has proper padding
    const container = page.locator('main .mx-auto').first()
    await expect(container).toBeVisible()
    
    const containerBox = await container.boundingBox()
    expect(containerBox?.x).toBeGreaterThan(320) // Should have left margin from sidebar + padding
    
    // Check sections have proper spacing
    const sections = container.locator('> div, > section').filter({ hasText: /.+/ })
    const sectionCount = await sections.count()
    expect(sectionCount).toBeGreaterThan(3)
    
    // Check vertical spacing between sections
    for (let i = 0; i < Math.min(sectionCount - 1, 3); i++) {
      const currentSection = sections.nth(i)
      const nextSection = sections.nth(i + 1)
      
      const currentBox = await currentSection.boundingBox()
      const nextBox = await nextSection.boundingBox()
      
      // There should be spacing between sections
      const spacing = (nextBox?.y || 0) - ((currentBox?.y || 0) + (currentBox?.height || 0))
      expect(spacing).toBeGreaterThan(10) // Should have at least some spacing
    }
    
    console.log('Spacing and margins check passed')
  })
})
