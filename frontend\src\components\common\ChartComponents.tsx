'use client';

import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Box, Paper, Typography, useTheme } from '@mui/material';

interface ChartProps {
  data: any[];
  title?: string;
  height?: number;
  loading?: boolean;
}

interface LineChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  lineColor?: string;
}

interface AreaChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  areaColor?: string;
}

interface BarChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  barColor?: string;
}

interface PieChartProps extends ChartProps {
  dataKey: string;
  nameKey: string;
  colors?: string[];
}

const ChartContainer: React.FC<{
  title?: string;
  height?: number;
  loading?: boolean;
  children: React.ReactNode;
}> = ({ title, height = 300, loading, children }) => {
  return (
    <Paper elevation={1} sx={{ p: 2 }}>
      {title && (
        <Typography variant="h6" component="h3" gutterBottom>
          {title}
        </Typography>
      )}
      
      <Box sx={{ width: '100%', height }}>
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
            }}
          >
            <Typography color="text.secondary">Loading chart...</Typography>
          </Box>
        ) : (
          <ResponsiveContainer width="100%" height="100%">
            {children as React.ReactElement}
          </ResponsiveContainer>
        )}
      </Box>
    </Paper>
  );
};

export const CustomLineChart: React.FC<LineChartProps> = ({
  data,
  title,
  height,
  loading,
  xKey,
  yKey,
  lineColor,
}) => {
  const theme = useTheme();
  const color = lineColor || theme.palette.primary.main;

  return (
    <ChartContainer {...(title && { title })} height={height || 300} loading={loading || false}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
        <XAxis 
          dataKey={xKey} 
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <YAxis 
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: theme.shape.borderRadius,
          }}
        />
        <Legend />
        <Line
          type="monotone"
          dataKey={yKey}
          stroke={color}
          strokeWidth={2}
          dot={{ fill: color, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
        />
      </LineChart>
    </ChartContainer>
  );
};

export const CustomAreaChart: React.FC<AreaChartProps> = ({
  data,
  title,
  height,
  loading,
  xKey,
  yKey,
  areaColor,
}) => {
  const theme = useTheme();
  const color = areaColor || theme.palette.primary.main;

  return (
    <ChartContainer {...(title && { title })} height={height || 300} loading={loading || false}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
        <XAxis
          dataKey={xKey}
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <YAxis
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: theme.shape.borderRadius,
          }}
        />
        <Legend />
        <Area
          type="monotone"
          dataKey={yKey}
          stroke={color}
          fill={color}
          fillOpacity={0.3}
          strokeWidth={2}
        />
      </AreaChart>
    </ChartContainer>
  );
};

export const CustomBarChart: React.FC<BarChartProps> = ({
  data,
  title,
  height,
  loading,
  xKey,
  yKey,
  barColor,
}) => {
  const theme = useTheme();
  const color = barColor || theme.palette.primary.main;

  return (
    <ChartContainer {...(title && { title })} height={height || 300} loading={loading || false}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
        <XAxis 
          dataKey={xKey} 
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <YAxis 
          stroke={theme.palette.text.secondary}
          fontSize={12}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: theme.shape.borderRadius,
          }}
        />
        <Legend />
        <Bar dataKey={yKey} fill={color} radius={[4, 4, 0, 0]} />
      </BarChart>
    </ChartContainer>
  );
};

export const CustomPieChart: React.FC<PieChartProps> = ({
  data,
  title,
  height,
  loading,
  dataKey,
  nameKey,
  colors,
}) => {
  const theme = useTheme();
  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
    theme.palette.success.main,
  ];
  const chartColors = colors || defaultColors;

  return (
    <ChartContainer {...(title && { title })} height={height || 300} loading={loading || false}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          outerRadius={80}
          fill="#8884d8"
          dataKey={dataKey}
          nameKey={nameKey}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={chartColors[index % chartColors.length]} 
            />
          ))}
        </Pie>
        <Tooltip
          contentStyle={{
            backgroundColor: theme.palette.background.paper,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: theme.shape.borderRadius,
          }}
        />
        <Legend />
      </PieChart>
    </ChartContainer>
  );
};

// Utility function to format chart data
export const formatChartData = (
  data: any[],
  xKey: string,
  yKey: string,
  formatX?: (value: any) => string,
  formatY?: (value: any) => number
) => {
  return data.map((item) => ({
    [xKey]: formatX ? formatX(item[xKey]) : item[xKey],
    [yKey]: formatY ? formatY(item[yKey]) : item[yKey],
    ...item,
  }));
};

// Common chart configurations for CRM data
export const CRMChartConfigs = {
  salesTrend: {
    xKey: 'month',
    yKey: 'sales',
    title: 'Sales Trend',
  },
  dealsPipeline: {
    dataKey: 'value',
    nameKey: 'stage',
    title: 'Deals Pipeline',
  },
  contactsGrowth: {
    xKey: 'month',
    yKey: 'contacts',
    title: 'Contacts Growth',
  },
  revenueBySource: {
    dataKey: 'revenue',
    nameKey: 'source',
    title: 'Revenue by Source',
  },
};
