import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const databaseConfig = registerAs('database', (): TypeOrmModuleOptions => {
  const isProduction = process.env.NODE_ENV === 'production';
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';

  // Validate required environment variables
  if (!process.env.DB_PASSWORD && isProduction) {
    throw new Error('DB_PASSWORD is required in production environment');
  }

  // PostgreSQL configuration for development, test, and production
  const config: TypeOrmModuleOptions = {
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || (isDevelopment ? '5433' : '5432'), 10),
    username: process.env.DB_USER || process.env.DB_USERNAME || 'onecrm',
    password: process.env.DB_PASSWORD || (isDevelopment ? 'onecrm_dev_password' : undefined),
    database: process.env.DB_NAME || (isTest ? 'onecrm_test' : 'onecrm'),

    // Entity discovery
    entities: [__dirname + '/../**/*.entity{.ts,.js}'],

    // Schema synchronization - DISABLED in production for safety
    synchronize: isDevelopment && process.env.DB_DISABLE_SYNC !== 'true',

    // Migrations - enabled for production
    migrationsRun: isProduction,
    migrations: [__dirname + '/../migrations/*{.ts,.js}'],

    // Logging configuration
    logging: isDevelopment ? ['query', 'error', 'warn', 'info'] : ['error', 'warn'],
    logger: 'advanced-console',

    // Connection pool settings - optimized for production
    extra: {
      max: parseInt(process.env.DB_MAX_CONNECTIONS || (isProduction ? '50' : '20'), 10),
      min: parseInt(process.env.DB_MIN_CONNECTIONS || (isProduction ? '10' : '5'), 10),
      acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000', 10),
      idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '10000', 10),
      createTimeoutMillis: parseInt(process.env.DB_CREATE_TIMEOUT || '30000', 10),
    },

    // SSL settings - required for production
    ssl: isProduction || process.env.DB_SSL === 'true'
      ? {
          rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
          ca: process.env.DB_SSL_CA,
          cert: process.env.DB_SSL_CERT,
          key: process.env.DB_SSL_KEY,
        }
      : false,

    // Connection timeout
    connectTimeoutMS: parseInt(process.env.DB_CONNECT_TIMEOUT || '10000', 10),

    // Retry settings - more aggressive in production
    retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || (isProduction ? '5' : '3'), 10),
    retryDelay: parseInt(process.env.DB_RETRY_DELAY || '3000', 10),

    // Auto load entities
    autoLoadEntities: true,

    // Cache settings for production performance
    cache: isProduction ? {
      type: 'redis',
      options: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || (isDevelopment ? '6380' : '6379'), 10),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '1', 10), // Use different DB for cache
      },
      duration: parseInt(process.env.DB_CACHE_DURATION || '30000', 10), // 30 seconds
    } : false,

    // Additional production settings
    maxQueryExecutionTime: parseInt(process.env.DB_MAX_QUERY_TIME || '5000', 10), // 5 seconds
  };

  return config;
});
