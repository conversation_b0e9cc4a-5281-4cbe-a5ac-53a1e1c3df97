import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { OrganizationFeature } from './organization-feature.entity';

export enum FeatureType {
  BOOLEAN = 'boolean',
  NUMERIC = 'numeric',
  STRING = 'string',
  JSON = 'json',
}

export enum FeatureScope {
  GLOBAL = 'global',
  ORGANIZATION = 'organization',
  USER = 'user',
}

export enum FeatureCategory {
  CORE = 'core',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
  BETA = 'beta',
  EXPERIMENTAL = 'experimental',
}

@Entity('feature_flags')
@Index(['key'], { unique: true })
export class FeatureFlag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  key: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: FeatureType,
    default: FeatureType.BOOLEAN,
  })
  type: FeatureType;

  @Column({
    type: 'enum',
    enum: FeatureScope,
    default: FeatureScope.ORGANIZATION,
  })
  scope: FeatureScope;

  @Column({
    type: 'enum',
    enum: FeatureCategory,
    default: FeatureCategory.CORE,
  })
  category: FeatureCategory;

  @Column({ type: 'json', nullable: true })
  defaultValue: any;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ type: 'json', nullable: true })
  constraints: {
    minPlanType?: string;
    maxUsers?: number;
    requiredFeatures?: string[];
    excludedFeatures?: string[];
    validationRules?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  rolloutStrategy: {
    percentage?: number;
    userSegments?: string[];
    organizationIds?: string[];
    gradualRollout?: {
      enabled: boolean;
      startPercentage: number;
      endPercentage: number;
      incrementPercentage: number;
      incrementInterval: number; // in hours
    };
  };

  @Column({ type: 'timestamp', nullable: true })
  enabledAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  disabledAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @OneToMany(() => OrganizationFeature, orgFeature => orgFeature.featureFlag)
  organizationFeatures: OrganizationFeature[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get isGloballyEnabled(): boolean {
    return this.enabled && this.scope === FeatureScope.GLOBAL;
  }

  get requiresPremium(): boolean {
    return this.category === FeatureCategory.PREMIUM || 
           this.category === FeatureCategory.ENTERPRISE;
  }

  get isBeta(): boolean {
    return this.category === FeatureCategory.BETA || 
           this.category === FeatureCategory.EXPERIMENTAL;
  }
}
