import { Controller, Get } from '@nestjs/common';
import { Public } from '../../common/decorators/public.decorator';
import { Unprotected } from 'nest-keycloak-connect';

@Controller('health')
export class HealthController {
  @Public()
  @Unprotected()
  @Get()
  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'onecrm-api',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
    };
  }

  @Get('ready')
  getReadiness() {
    return {
      status: 'ready',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok',
        keycloak: 'ok',
      },
    };
  }

  @Get('live')
  getLiveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    };
  }

  @Get('modules')
  getModulesStatus() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'onecrm-api',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      modules: {
        health: { status: 'active', endpoints: ['GET /api/health', 'GET /api/health/modules'] },
        organizations: { status: 'active', endpoints: ['GET /api/organizations/test'] },
        contacts: { status: 'active', endpoints: ['GET /api/contacts/test'] },
        companies: { status: 'active', endpoints: ['GET /api/companies/test'] },
        deals: { status: 'active', endpoints: ['GET /api/deals'] },
        users: { status: 'active', endpoints: ['GET /api/users/profile'] },
        activities: { status: 'active', endpoints: ['GET /api/activities'] },
      },
      database: {
        status: 'connected',
        type: 'postgresql',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432', 10),
      },
      logging: {
        status: 'active',
        level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      },
      features: {
        hotReload: process.env.NODE_ENV === 'development',
        swagger: true,
        authentication: false, // Disabled for development
      },
    };
  }
}
