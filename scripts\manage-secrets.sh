#!/bin/bash

# OneCRM Secrets Management Script
# This script helps manage secrets for different environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

ENVIRONMENT=${1:-staging}
ACTION=${2:-generate}

echo -e "${BLUE}🔐 OneCRM Secrets Management${NC}"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Action: $ACTION"
echo ""

# Function to generate random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -hex 32
}

# Function to generate encryption key
generate_encryption_key() {
    openssl rand -hex 32
}

# Function to create secrets file
create_secrets_file() {
    local env=$1
    local secrets_file="infrastructure/kubernetes/secrets-$env.yaml"
    
    echo -e "${BLUE}Creating secrets file for $env environment...${NC}"
    
    # Generate random values
    local db_password=$(generate_password 24)
    local redis_password=$(generate_password 16)
    local jwt_secret=$(generate_jwt_secret)
    local jwt_refresh_secret=$(generate_jwt_secret)
    local keycloak_admin_password=$(generate_password 16)
    local keycloak_secret=$(generate_password 32)
    local encryption_key=$(generate_encryption_key)
    local grafana_password=$(generate_password 16)
    
    # Create secrets file from template
    cat > $secrets_file << EOF
apiVersion: v1
kind: Secret
metadata:
  name: onecrm-secrets
  namespace: onecrm$([ "$env" = "staging" ] && echo "-staging" || echo "")
  labels:
    app: onecrm
    environment: $env
type: Opaque
stringData:
  # Database Configuration
  database-url: "***********************************************************/onecrm$([ "$env" = "staging" ] && echo "_staging" || echo "")"
  postgres-user: "onecrm_$env"
  postgres-password: "$db_password"
  postgres-db: "onecrm$([ "$env" = "staging" ] && echo "_staging" || echo "")"
  
  # Redis Configuration
  redis-url: "redis://:$redis_password@redis-service:6379"
  redis-password: "$redis_password"
  
  # JWT Configuration
  jwt-secret: "$jwt_secret"
  jwt-refresh-secret: "$jwt_refresh_secret"
  
  # Keycloak Configuration
  keycloak-admin: "admin"
  keycloak-admin-password: "$keycloak_admin_password"
  keycloak-secret: "$keycloak_secret"
  keycloak-db: "keycloak$([ "$env" = "staging" ] && echo "_staging" || echo "")"
  
  # Email Configuration
  smtp-host: "$([ "$env" = "staging" ] && echo "smtp.mailtrap.io" || echo "smtp.gmail.com")"
  smtp-port: "587"
  smtp-user: "REPLACE_WITH_SMTP_USER"
  smtp-password: "REPLACE_WITH_SMTP_PASSWORD"
  smtp-from: "$([ "$env" = "staging" ] && echo "staging" || echo "noreply")@onecrm.example.com"
  
  # File Storage Configuration
  s3-access-key: "REPLACE_WITH_S3_ACCESS_KEY"
  s3-secret-key: "REPLACE_WITH_S3_SECRET_KEY"
  s3-bucket: "onecrm-files$([ "$env" = "staging" ] && echo "-staging" || echo "")"
  s3-region: "us-east-1"
  s3-endpoint: "https://s3.amazonaws.com"
  
  # Monitoring Configuration
  grafana-admin-user: "admin"
  grafana-admin-password: "$grafana_password"
  
  # External API Keys (Placeholders)
  google-maps-api-key: "REPLACE_WITH_GOOGLE_MAPS_API_KEY"
  sendgrid-api-key: "REPLACE_WITH_SENDGRID_API_KEY"
  stripe-secret-key: "REPLACE_WITH_STRIPE_SECRET_KEY"
  stripe-webhook-secret: "REPLACE_WITH_STRIPE_WEBHOOK_SECRET"
  
  # Encryption Key
  encryption-key: "$encryption_key"
EOF

    echo -e "${GREEN}✅ Secrets file created: $secrets_file${NC}"
    echo -e "${YELLOW}⚠️  Please update the placeholder values with actual secrets${NC}"
    
    # Create a summary file with generated values
    cat > "secrets-summary-$env.txt" << EOF
OneCRM $env Environment - Generated Secrets Summary
Generated on: $(date)

Database Password: $db_password
Redis Password: $redis_password
JWT Secret: $jwt_secret
JWT Refresh Secret: $jwt_refresh_secret
Keycloak Admin Password: $keycloak_admin_password
Keycloak Client Secret: $keycloak_secret
Encryption Key: $encryption_key
Grafana Password: $grafana_password

IMPORTANT: Store these values securely and delete this file after use!
EOF
    
    echo -e "${GREEN}✅ Secrets summary saved: secrets-summary-$env.txt${NC}"
    echo -e "${RED}🚨 IMPORTANT: Store the secrets securely and delete the summary file!${NC}"
}

# Function to apply secrets to Kubernetes
apply_secrets() {
    local env=$1
    local secrets_file="infrastructure/kubernetes/secrets-$env.yaml"
    local namespace="onecrm$([ "$env" = "staging" ] && echo "-staging" || echo "")"
    
    if [ ! -f "$secrets_file" ]; then
        echo -e "${RED}❌ Secrets file not found: $secrets_file${NC}"
        echo "Run: $0 $env generate"
        exit 1
    fi
    
    echo -e "${BLUE}Applying secrets to $namespace namespace...${NC}"
    
    # Check if namespace exists
    if ! kubectl get namespace $namespace >/dev/null 2>&1; then
        echo -e "${YELLOW}Creating namespace $namespace...${NC}"
        kubectl create namespace $namespace
    fi
    
    # Apply secrets
    kubectl apply -f $secrets_file
    
    echo -e "${GREEN}✅ Secrets applied successfully${NC}"
}

# Function to view secrets (masked)
view_secrets() {
    local env=$1
    local namespace="onecrm$([ "$env" = "staging" ] && echo "-staging" || echo "")"
    
    echo -e "${BLUE}Secrets in $namespace namespace:${NC}"
    kubectl get secrets -n $namespace
    
    echo -e "\n${BLUE}Secret details (values masked):${NC}"
    kubectl describe secret onecrm-secrets -n $namespace
}

# Function to backup secrets
backup_secrets() {
    local env=$1
    local namespace="onecrm$([ "$env" = "staging" ] && echo "-staging" || echo "")"
    local backup_file="secrets-backup-$env-$(date +%Y%m%d-%H%M%S).yaml"
    
    echo -e "${BLUE}Backing up secrets from $namespace...${NC}"
    
    kubectl get secret onecrm-secrets -n $namespace -o yaml > $backup_file
    
    echo -e "${GREEN}✅ Secrets backed up to: $backup_file${NC}"
}

# Function to restore secrets
restore_secrets() {
    local env=$1
    local backup_file=$2
    
    if [ -z "$backup_file" ]; then
        echo -e "${RED}❌ Please specify backup file${NC}"
        echo "Usage: $0 $env restore <backup-file>"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}❌ Backup file not found: $backup_file${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}Restoring secrets from $backup_file...${NC}"
    
    kubectl apply -f $backup_file
    
    echo -e "${GREEN}✅ Secrets restored successfully${NC}"
}

# Function to rotate secrets
rotate_secrets() {
    local env=$1
    
    echo -e "${YELLOW}⚠️  This will generate new secrets and update the cluster${NC}"
    echo -e "${RED}🚨 Make sure to update your applications with new secrets!${NC}"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled"
        exit 0
    fi
    
    # Backup current secrets
    backup_secrets $env
    
    # Generate new secrets
    create_secrets_file $env
    
    # Apply new secrets
    apply_secrets $env
    
    echo -e "${GREEN}✅ Secrets rotated successfully${NC}"
    echo -e "${YELLOW}⚠️  Remember to restart your applications to use new secrets${NC}"
}

# Function to validate secrets
validate_secrets() {
    local env=$1
    local namespace="onecrm$([ "$env" = "staging" ] && echo "-staging" || echo "")"
    
    echo -e "${BLUE}Validating secrets in $namespace...${NC}"
    
    # Check if secret exists
    if ! kubectl get secret onecrm-secrets -n $namespace >/dev/null 2>&1; then
        echo -e "${RED}❌ Secret 'onecrm-secrets' not found in namespace $namespace${NC}"
        exit 1
    fi
    
    # Check required keys
    local required_keys=(
        "database-url"
        "redis-url"
        "jwt-secret"
        "keycloak-secret"
        "encryption-key"
    )
    
    local missing_keys=()
    
    for key in "${required_keys[@]}"; do
        if ! kubectl get secret onecrm-secrets -n $namespace -o jsonpath="{.data.$key}" >/dev/null 2>&1; then
            missing_keys+=($key)
        fi
    done
    
    if [ ${#missing_keys[@]} -eq 0 ]; then
        echo -e "${GREEN}✅ All required secrets are present${NC}"
    else
        echo -e "${RED}❌ Missing required secrets:${NC}"
        for key in "${missing_keys[@]}"; do
            echo -e "  - $key"
        done
        exit 1
    fi
}

# Main function
main() {
    case $ACTION in
        "generate")
            create_secrets_file $ENVIRONMENT
            ;;
        "apply")
            apply_secrets $ENVIRONMENT
            ;;
        "view")
            view_secrets $ENVIRONMENT
            ;;
        "backup")
            backup_secrets $ENVIRONMENT
            ;;
        "restore")
            restore_secrets $ENVIRONMENT $3
            ;;
        "rotate")
            rotate_secrets $ENVIRONMENT
            ;;
        "validate")
            validate_secrets $ENVIRONMENT
            ;;
        *)
            echo "Usage: $0 <environment> <action> [options]"
            echo ""
            echo "Environments:"
            echo "  staging     - Staging environment"
            echo "  production  - Production environment"
            echo ""
            echo "Actions:"
            echo "  generate    - Generate new secrets file"
            echo "  apply       - Apply secrets to Kubernetes"
            echo "  view        - View current secrets (masked)"
            echo "  backup      - Backup current secrets"
            echo "  restore     - Restore secrets from backup"
            echo "  rotate      - Rotate all secrets"
            echo "  validate    - Validate required secrets exist"
            echo ""
            echo "Examples:"
            echo "  $0 staging generate"
            echo "  $0 production apply"
            echo "  $0 staging backup"
            echo "  $0 production restore secrets-backup-production-20240101-120000.yaml"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
