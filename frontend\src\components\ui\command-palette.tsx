'use client'

import * as React from 'react'
import { useRouter } from 'next/navigation'
import { 
  Search, 
  Users, 
  Building2, 
  TrendingUp, 
  Settings, 
  Plus, 
  Calendar,
  FileText,
  BarChart3,
  Command as CommandIcon,
  Clock,
  Star
} from 'lucide-react'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
  CommandShortcut,
} from '@/components/ui/command'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/toast'

interface CommandAction {
  id: string
  title: string
  description?: string
  icon?: React.ReactNode
  shortcut?: string[]
  keywords?: string[]
  section: 'navigation' | 'actions' | 'search' | 'recent' | 'favorites'
  action: () => void | Promise<void>
  disabled?: boolean
}

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'contact' | 'company' | 'deal' | 'document'
  url: string
  metadata?: Record<string, any>
}

// Mock data for demonstration
const mockSearchResults: SearchResult[] = [
  {
    id: '1',
    title: '<PERSON>',
    description: 'Senior Developer at TechCorp',
    type: 'contact',
    url: '/contacts/1',
    metadata: { email: '<EMAIL>', phone: '******-0123' }
  },
  {
    id: '2',
    title: 'TechCorp Solutions',
    description: 'Enterprise Software Company',
    type: 'company',
    url: '/companies/1',
    metadata: { employees: 500, industry: 'Technology' }
  },
  {
    id: '3',
    title: 'Enterprise License Deal',
    description: '$50,000 software licensing agreement',
    type: 'deal',
    url: '/deals/1',
    metadata: { value: 50000, stage: 'negotiation' }
  },
]

const useCommandPalette = () => {
  const [open, setOpen] = React.useState(false)
  const [search, setSearch] = React.useState('')
  const [searchResults, setSearchResults] = React.useState<SearchResult[]>([])
  const [recentActions, setRecentActions] = React.useState<string[]>([])
  const [favorites, setFavorites] = React.useState<string[]>([])
  const [loading, setLoading] = React.useState(false)
  
  const router = useRouter()
  const { toast } = useToast()

  // Keyboard shortcut to open command palette
  React.useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  // Search functionality
  React.useEffect(() => {
    if (!search.trim()) {
      setSearchResults([])
      return
    }

    const searchTimeout = setTimeout(async () => {
      setLoading(true)
      try {
        // Simulate API search
        await new Promise(resolve => setTimeout(resolve, 300))
        
        const filtered = mockSearchResults.filter(result =>
          result.title.toLowerCase().includes(search.toLowerCase()) ||
          result.description.toLowerCase().includes(search.toLowerCase())
        )
        
        setSearchResults(filtered)
      } catch (error) {
        console.error('Search error:', error)
        toast({
          title: 'Search Error',
          description: 'Failed to search. Please try again.',
          variant: 'destructive',
        })
      } finally {
        setLoading(false)
      }
    }, 200)

    return () => clearTimeout(searchTimeout)
  }, [search, toast])

  const addToRecent = (actionId: string) => {
    setRecentActions(prev => {
      const filtered = prev.filter(id => id !== actionId)
      return [actionId, ...filtered].slice(0, 5)
    })
  }

  const toggleFavorite = (actionId: string) => {
    setFavorites(prev => 
      prev.includes(actionId) 
        ? prev.filter(id => id !== actionId)
        : [...prev, actionId]
    )
  }

  const executeAction = async (action: CommandAction) => {
    try {
      await action.action()
      addToRecent(action.id)
      setOpen(false)
      setSearch('')
    } catch (error) {
      console.error('Action error:', error)
      toast({
        title: 'Action Failed',
        description: 'Failed to execute action. Please try again.',
        variant: 'destructive',
      })
    }
  }

  return {
    open,
    setOpen,
    search,
    setSearch,
    searchResults,
    recentActions,
    favorites,
    loading,
    executeAction,
    toggleFavorite,
  }
}

export function CommandPalette() {
  const {
    open,
    setOpen,
    search,
    setSearch,
    searchResults,
    recentActions,
    favorites,
    loading,
    executeAction,
    toggleFavorite,
  } = useCommandPalette()
  
  const router = useRouter()

  const navigationActions: CommandAction[] = [
    {
      id: 'nav-dashboard',
      title: 'Dashboard',
      description: 'Go to dashboard',
      icon: <BarChart3 className="h-4 w-4" />,
      shortcut: ['g', 'd'],
      section: 'navigation',
      action: () => router.push('/dashboard'),
    },
    {
      id: 'nav-contacts',
      title: 'Contacts',
      description: 'View all contacts',
      icon: <Users className="h-4 w-4" />,
      shortcut: ['g', 'c'],
      section: 'navigation',
      action: () => router.push('/contacts'),
    },
    {
      id: 'nav-companies',
      title: 'Companies',
      description: 'View all companies',
      icon: <Building2 className="h-4 w-4" />,
      shortcut: ['g', 'o'],
      section: 'navigation',
      action: () => router.push('/companies'),
    },
    {
      id: 'nav-deals',
      title: 'Deals',
      description: 'View all deals',
      icon: <TrendingUp className="h-4 w-4" />,
      shortcut: ['g', 'e'],
      section: 'navigation',
      action: () => router.push('/deals'),
    },
  ]

  const quickActions: CommandAction[] = [
    {
      id: 'action-new-contact',
      title: 'New Contact',
      description: 'Create a new contact',
      icon: <Plus className="h-4 w-4" />,
      shortcut: ['n', 'c'],
      section: 'actions',
      action: () => router.push('/contacts/new'),
    },
    {
      id: 'action-new-company',
      title: 'New Company',
      description: 'Create a new company',
      icon: <Plus className="h-4 w-4" />,
      shortcut: ['n', 'o'],
      section: 'actions',
      action: () => router.push('/companies/new'),
    },
    {
      id: 'action-new-deal',
      title: 'New Deal',
      description: 'Create a new deal',
      icon: <Plus className="h-4 w-4" />,
      shortcut: ['n', 'd'],
      section: 'actions',
      action: () => router.push('/deals/new'),
    },
    {
      id: 'action-schedule-meeting',
      title: 'Schedule Meeting',
      description: 'Schedule a new meeting',
      icon: <Calendar className="h-4 w-4" />,
      shortcut: ['n', 'm'],
      section: 'actions',
      action: () => router.push('/calendar/new'),
    },
  ]

  const allActions = [...navigationActions, ...quickActions]

  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'contact':
        return <Users className="h-4 w-4" />
      case 'company':
        return <Building2 className="h-4 w-4" />
      case 'deal':
        return <TrendingUp className="h-4 w-4" />
      case 'document':
        return <FileText className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: SearchResult['type']) => {
    switch (type) {
      case 'contact':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'company':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'deal':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'document':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  return (
    <>
      {/* Trigger button */}
      <button
        onClick={() => setOpen(true)}
        className="inline-flex items-center gap-2 px-3 py-2 text-sm text-muted-foreground bg-background border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
      >
        <Search className="h-4 w-4" />
        <span>Search...</span>
        <CommandShortcut>⌘K</CommandShortcut>
      </button>

      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder="Type a command or search..."
          value={search}
          onValueChange={setSearch}
        />
        <CommandList>
          <CommandEmpty>
            {loading ? 'Searching...' : 'No results found.'}
          </CommandEmpty>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <CommandGroup heading="Search Results">
              {searchResults.map((result) => (
                <CommandItem
                  key={result.id}
                  onSelect={() => {
                    router.push(result.url)
                    setOpen(false)
                  }}
                  className="flex items-center gap-3"
                >
                  {getTypeIcon(result.type)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{result.title}</span>
                      <Badge variant="secondary" className={`text-xs ${getTypeColor(result.type)}`}>
                        {result.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {result.description}
                    </p>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          )}

          {/* Recent Actions */}
          {recentActions.length > 0 && !search && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Recent">
                {recentActions.map((actionId) => {
                  const action = allActions.find(a => a.id === actionId)
                  if (!action) return null
                  
                  return (
                    <CommandItem
                      key={action.id}
                      onSelect={() => executeAction(action)}
                      className="flex items-center gap-3"
                    >
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      {action.icon}
                      <div className="flex-1">
                        <span>{action.title}</span>
                        {action.description && (
                          <p className="text-sm text-muted-foreground">
                            {action.description}
                          </p>
                        )}
                      </div>
                      {action.shortcut && (
                        <CommandShortcut>
                          {action.shortcut.join(' ')}
                        </CommandShortcut>
                      )}
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </>
          )}

          {/* Navigation */}
          {!search && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Navigation">
                {navigationActions.map((action) => (
                  <CommandItem
                    key={action.id}
                    onSelect={() => executeAction(action)}
                    className="flex items-center gap-3"
                  >
                    {action.icon}
                    <div className="flex-1">
                      <span>{action.title}</span>
                      {action.description && (
                        <p className="text-sm text-muted-foreground">
                          {action.description}
                        </p>
                      )}
                    </div>
                    {action.shortcut && (
                      <CommandShortcut>
                        {action.shortcut.join(' ')}
                      </CommandShortcut>
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}

          {/* Quick Actions */}
          {!search && (
            <>
              <CommandSeparator />
              <CommandGroup heading="Quick Actions">
                {quickActions.map((action) => (
                  <CommandItem
                    key={action.id}
                    onSelect={() => executeAction(action)}
                    className="flex items-center gap-3"
                  >
                    {action.icon}
                    <div className="flex-1">
                      <span>{action.title}</span>
                      {action.description && (
                        <p className="text-sm text-muted-foreground">
                          {action.description}
                        </p>
                      )}
                    </div>
                    {favorites.includes(action.id) && (
                      <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    )}
                    {action.shortcut && (
                      <CommandShortcut>
                        {action.shortcut.join(' ')}
                      </CommandShortcut>
                    )}
                  </CommandItem>
                ))}
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
}

export { useCommandPalette }
