'use client';

import React from 'react';
import { AppShell, Container, PageHeader } from '@/components/layout/app-shell';
import { EnhancedCard, MetricCard, ActionCard } from '@/components/ui/enhanced-card';
import { EnhancedBadge, StatusBadge } from '@/components/ui/enhanced-badge';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  Building2, 
  TrendingUp, 
  DollarSign, 
  Activity, 
  Calendar,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Eye,
  Info
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { useToast } from '@/components/ui/toast';
import { Enhanced<PERSON>ine<PERSON><PERSON>, EnhancedBar<PERSON><PERSON>, <PERSON>hanced<PERSON>ie<PERSON><PERSON> } from '@/components/ui/charts';
import { usePerformanceMetrics } from '@/lib/performance';

// Mock data for testing
const mockData = {
  stats: {
    totalContacts: 1247,
    totalCompanies: 89,
    totalDeals: 156,
    totalRevenue: 2450000,
  },
  recentActivity: [
    {
      id: '1',
      type: 'deal',
      title: 'New deal created: Enterprise Software License',
      description: 'Deal worth $50,000 added to pipeline',
      timestamp: new Date().toISOString(),
      user: 'John Doe',
    },
    {
      id: '2',
      type: 'contact',
      title: 'Contact updated: <PERSON> <PERSON>',
      description: 'Contact information and preferences updated',
      timestamp: new Date(Date.now() - 3600000).toISOString(),
      user: 'Jane Smith',
    },
    {
      id: '3',
      type: 'company',
      title: 'New company added: TechCorp Solutions',
      description: 'Enterprise client with 500+ employees',
      timestamp: new Date(Date.now() - 7200000).toISOString(),
      user: 'Mike Wilson',
    },
  ],
};

// Mock chart data
const chartData = [
  { name: 'Jan', contacts: 400, deals: 240, revenue: 2400 },
  { name: 'Feb', contacts: 300, deals: 139, revenue: 2210 },
  { name: 'Mar', contacts: 200, deals: 980, revenue: 2290 },
  { name: 'Apr', contacts: 278, deals: 390, revenue: 2000 },
  { name: 'May', contacts: 189, deals: 480, revenue: 2181 },
  { name: 'Jun', contacts: 239, deals: 380, revenue: 2500 },
]

const pieData = [
  { name: 'New', value: 400, color: '#8884d8' },
  { name: 'Qualified', value: 300, color: '#82ca9d' },
  { name: 'Proposal', value: 200, color: '#ffc658' },
  { name: 'Won', value: 100, color: '#ff7300' },
]

export default function UITestPage() {
  const { toast } = useToast()
  const { metrics, vitals } = usePerformanceMetrics()

  const handleToastDemo = (type: 'success' | 'error' | 'warning' | 'info') => {
    switch (type) {
      case 'success':
        toast({
          title: 'Success!',
          description: 'Your action was completed successfully.',
          variant: 'success',
        })
        break
      case 'error':
        toast({
          title: 'Error occurred',
          description: 'Something went wrong. Please try again.',
          variant: 'destructive',
        })
        break
      case 'warning':
        toast({
          title: 'Warning',
          description: 'Please review your input before proceeding.',
          variant: 'warning',
        })
        break
      case 'info':
        toast({
          title: 'Information',
          description: 'Here is some helpful information.',
          variant: 'info',
        })
        break
    }
  }

  return (
    <AppShell>
      <Container className="py-8 space-y-8">
        {/* Header */}
        <PageHeader
          title="UI Test Dashboard"
          description="Testing UI components and layouts without authentication"
          actions={
            <div className="flex items-center gap-2">
              <EnhancedBadge variant="info" icon={<Info className="h-3 w-3" />}>
                Test Mode
              </EnhancedBadge>
              <EnhancedButton
                variant="outline"
                size="sm"
                leftIcon={<Plus className="h-4 w-4" />}
              >
                Quick Add
              </EnhancedButton>
            </div>
          }
        />

        {/* Demo Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>UI Test Environment:</strong> This page is for testing UI components and layouts 
            without authentication requirements.
          </AlertDescription>
        </Alert>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" data-testid="metric-cards">
          <MetricCard
            value={mockData.stats.totalContacts.toLocaleString()}
            label="Total Contacts"
            change={{
              value: 8.2,
              type: 'increase',
              period: 'last month'
            }}
            icon={<Users className="h-6 w-6" />}
          />
          <MetricCard
            value={mockData.stats.totalCompanies.toLocaleString()}
            label="Companies"
            change={{
              value: 12.5,
              type: 'increase',
              period: 'last month'
            }}
            icon={<Building2 className="h-6 w-6" />}
          />
          <MetricCard
            value={mockData.stats.totalDeals.toLocaleString()}
            label="Active Deals"
            change={{
              value: 3.1,
              type: 'decrease',
              period: 'last month'
            }}
            icon={<TrendingUp className="h-6 w-6" />}
          />
          <MetricCard
            value={formatCurrency(mockData.stats.totalRevenue)}
            label="Total Revenue"
            change={{
              value: 15.3,
              type: 'increase',
              period: 'last month'
            }}
            icon={<DollarSign className="h-6 w-6" />}
          />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <div className="lg:col-span-2">
            <EnhancedCard
              title="Recent Activity"
              description="Latest updates and changes in your CRM"
              headerActions={
                <EnhancedButton
                  variant="ghost"
                  size="sm"
                  rightIcon={<Eye className="h-4 w-4" />}
                >
                  View All
                </EnhancedButton>
              }
            >
              <div className="space-y-4">
                {mockData.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="p-2 rounded-full bg-primary/10">
                      <Activity className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm">{activity.title}</p>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatDate(activity.timestamp)}
                        </span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{activity.user}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </EnhancedCard>
          </div>

          {/* Quick Actions */}
          <div>
            <EnhancedCard
              title="Quick Actions"
              description="Common tasks and shortcuts"
            >
              <div className="space-y-3">
                <ActionCard
                  icon={<Users className="h-5 w-5" />}
                  label="Add Contact"
                  description="Create a new contact"
                />
                <ActionCard
                  icon={<Building2 className="h-5 w-5" />}
                  label="Add Company"
                  description="Register a new company"
                />
                <ActionCard
                  icon={<TrendingUp className="h-5 w-5" />}
                  label="Create Deal"
                  description="Start a new deal"
                />
                <ActionCard
                  icon={<Calendar className="h-5 w-5" />}
                  label="Schedule Meeting"
                  description="Book a meeting"
                />
              </div>
            </EnhancedCard>
          </div>
        </div>

        {/* Analytics Overview */}
        <EnhancedCard
          title="Analytics Overview"
          description="Key performance metrics and trends"
          headerActions={
            <EnhancedButton
              variant="outline"
              size="sm"
              rightIcon={<ArrowUpRight className="h-4 w-4" />}
            >
              View Details
            </EnhancedButton>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Conversion Rate</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">24.8%</span>
                <div className="flex items-center text-green-600 text-sm">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>+2.1%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Monthly Growth</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">12.5%</span>
                <div className="flex items-center text-green-600 text-sm">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>+0.8%</span>
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Activity Score</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">87</span>
                <div className="flex items-center text-red-600 text-sm">
                  <ArrowDownRight className="h-3 w-3" />
                  <span>-3</span>
                </div>
              </div>
            </div>
          </div>
        </EnhancedCard>

        {/* Advanced Features Demo */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Advanced Features Demo</h2>

          {/* Toast Notifications Demo */}
          <EnhancedCard
            title="Toast Notifications"
            description="Real-time feedback system with different notification types"
          >
            <div className="flex flex-wrap gap-2">
              <EnhancedButton
                variant="success"
                size="sm"
                onClick={() => handleToastDemo('success')}
              >
                Success Toast
              </EnhancedButton>
              <EnhancedButton
                variant="destructive"
                size="sm"
                onClick={() => handleToastDemo('error')}
              >
                Error Toast
              </EnhancedButton>
              <EnhancedButton
                variant="warning"
                size="sm"
                onClick={() => handleToastDemo('warning')}
              >
                Warning Toast
              </EnhancedButton>
              <EnhancedButton
                variant="info"
                size="sm"
                onClick={() => handleToastDemo('info')}
              >
                Info Toast
              </EnhancedButton>
            </div>
          </EnhancedCard>

          {/* Performance Metrics */}
          {metrics && (
            <EnhancedCard
              title="Performance Metrics"
              description="Real-time performance monitoring and Core Web Vitals"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium">Page Load</div>
                  <div className="text-muted-foreground">{metrics.pageLoadTime.toFixed(2)}ms</div>
                </div>
                <div>
                  <div className="font-medium">First Paint</div>
                  <div className="text-muted-foreground">{metrics.firstPaint.toFixed(2)}ms</div>
                </div>
                <div>
                  <div className="font-medium">FCP</div>
                  <div className="text-muted-foreground">{metrics.firstContentfulPaint.toFixed(2)}ms</div>
                </div>
                <div>
                  <div className="font-medium">TTFB</div>
                  <div className="text-muted-foreground">{metrics.timeToFirstByte.toFixed(2)}ms</div>
                </div>
              </div>
            </EnhancedCard>
          )}
        </div>

        {/* Data Visualization */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Data Visualization</h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EnhancedLineChart
              data={chartData}
              lines={[
                { dataKey: 'contacts', name: 'Contacts', color: '#8884d8' },
                { dataKey: 'deals', name: 'Deals', color: '#82ca9d' },
              ]}
              title="Monthly Trends"
              description="Contact and deal trends over time"
              height={300}
            />

            <EnhancedBarChart
              data={chartData}
              bars={[
                { dataKey: 'revenue', name: 'Revenue', color: '#ffc658' },
              ]}
              title="Monthly Revenue"
              description="Revenue performance by month"
              height={300}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <EnhancedPieChart
              data={pieData}
              title="Deal Pipeline"
              description="Distribution of deals by stage"
              height={300}
            />

            <EnhancedCard
              title="Command Palette"
              description="Press Cmd+K (or Ctrl+K) to open the command palette for quick navigation and actions"
            >
              <div className="space-y-4">
                <div className="p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <kbd className="px-2 py-1 bg-background border rounded text-xs">⌘</kbd>
                    <span>+</span>
                    <kbd className="px-2 py-1 bg-background border rounded text-xs">K</kbd>
                    <span className="text-muted-foreground">Open command palette</span>
                  </div>
                </div>
                <div className="text-sm text-muted-foreground">
                  Try searching for "dashboard", "contacts", or "new contact" to see the command palette in action.
                </div>
              </div>
            </EnhancedCard>
          </div>
        </div>

        {/* Navigation Cards */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Explore OneCRM</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <ActionCard
              icon={<Users className="h-6 w-6" />}
              label="Contacts"
              description="Manage your customer contacts and relationships"
            />
            <ActionCard
              icon={<Building2 className="h-6 w-6" />}
              label="Companies"
              description="Track companies and organizational hierarchies"
            />
            <ActionCard
              icon={<TrendingUp className="h-6 w-6" />}
              label="Deals"
              description="Manage sales pipeline and deal progression"
            />
            <ActionCard
              icon={<BarChart3 className="h-6 w-6" />}
              label="Analytics"
              description="Advanced reporting and business insights"
            />
          </div>
        </div>
      </Container>
    </AppShell>
  );
}
