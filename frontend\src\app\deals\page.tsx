'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  <PERSON><PERSON><PERSON>,
  Button,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Fab,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  ViewKanban as KanbanIcon,
  TableChart as TableIcon,
  Analytics as AnalyticsIcon,
} from '@mui/icons-material';
import { DealsTable } from '../../components/deals/DealsTable';
import { DealsKanban } from '../../components/deals/DealsKanban';
import { DealsStats } from '../../components/deals/DealsStats';
import { DealForm } from '../../components/deals/DealForm';
import { DealFilters } from '../../components/deals/DealFilters';
import { DealsForecast } from '../../components/deals/DealsForecast';
import { AppLayout } from '../../components/layout/AppLayout';

type ViewMode = 'table' | 'kanban' | 'forecast';

export default function DealsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('kanban');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    stage: '',
    ownerId: '',
    contactId: '',
    companyId: '',
    source: '',
    type: '',
    priority: '',
    minAmount: '',
    maxAmount: '',
    minProbability: '',
    maxProbability: '',
    expectedCloseDateFrom: '',
    expectedCloseDateTo: '',
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showDealForm, setShowDealForm] = useState(false);
  const [selectedDeal, setSelectedDeal] = useState(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleCreateDeal = () => {
    setSelectedDeal(null);
    setShowDealForm(true);
  };

  const handleEditDeal = (deal: any) => {
    setSelectedDeal(deal);
    setShowDealForm(true);
  };

  const handleCloseForm = () => {
    setShowDealForm(false);
    setSelectedDeal(null);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleViewModeChange = (event: React.MouseEvent<HTMLElement>, newMode: ViewMode) => {
    if (newMode !== null) {
      setViewMode(newMode);
    }
  };

  const handleExport = () => {
    handleMenuClose();
    console.log('Export deals');
  };

  const handleImport = () => {
    handleMenuClose();
    console.log('Import deals');
  };

  const renderContent = () => {
    switch (viewMode) {
      case 'table':
        return (
          <DealsTable
            searchQuery={searchQuery}
            filters={filters}
            onEditDeal={handleEditDeal}
          />
        );
      case 'kanban':
        return (
          <DealsKanban
            searchQuery={searchQuery}
            filters={filters}
            onEditDeal={handleEditDeal}
          />
        );
      case 'forecast':
        return <DealsForecast />;
      default:
        return null;
    }
  };

  return (
    <AppLayout>
      <Container maxWidth="xl">
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1" fontWeight={600}>
            Deals
          </Typography>
          
          <Box display="flex" gap={2} alignItems="center">
            {/* View Mode Toggle */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
            >
              <ToggleButton value="kanban">
                <KanbanIcon />
              </ToggleButton>
              <ToggleButton value="table">
                <TableIcon />
              </ToggleButton>
              <ToggleButton value="forecast">
                <AnalyticsIcon />
              </ToggleButton>
            </ToggleButtonGroup>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateDeal}
            >
              Add Deal
            </Button>
          </Box>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleExport}>
              <DownloadIcon sx={{ mr: 1 }} />
              Export Deals
            </MenuItem>
            <MenuItem onClick={handleImport}>
              <UploadIcon sx={{ mr: 1 }} />
              Import Deals
            </MenuItem>
          </Menu>
        </Box>

        {/* Stats */}
        <Box mb={3}>
          <DealsStats />
        </Box>

        {/* Search and Filters - Only show for table and kanban views */}
        {viewMode !== 'forecast' && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Search deals by title, company, or contact..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Box display="flex" gap={1} alignItems="center" flexWrap="wrap">
                    <Button
                      variant={showFilters ? 'contained' : 'outlined'}
                      startIcon={<FilterIcon />}
                      onClick={() => setShowFilters(!showFilters)}
                      size="small"
                    >
                      Filters
                    </Button>
                    
                    {/* Active Filters */}
                    {filters.stage && (
                      <Chip
                        label={`Stage: ${filters.stage}`}
                        onDelete={() => setFilters({ ...filters, stage: '' })}
                        size="small"
                      />
                    )}
                    
                    {filters.priority && (
                      <Chip
                        label={`Priority: ${filters.priority}`}
                        onDelete={() => setFilters({ ...filters, priority: '' })}
                        size="small"
                      />
                    )}
                  </Box>
                </Grid>
              </Grid>

              {/* Expandable Filters */}
              {showFilters && (
                <Box mt={2}>
                  <DealFilters
                    filters={filters}
                    onChange={handleFilterChange}
                  />
                </Box>
              )}
            </CardContent>
          </Card>
        )}

        {/* Content based on view mode */}
        {renderContent()}

        {/* Floating Action Button for Mobile */}
        <Fab
          color="primary"
          aria-label="add deal"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' },
          }}
          onClick={handleCreateDeal}
        >
          <AddIcon />
        </Fab>

        {/* Deal Form Dialog */}
        {showDealForm && (
          <DealForm
            open={showDealForm}
            deal={selectedDeal}
            onClose={handleCloseForm}
          />
        )}
      </Container>
    </AppLayout>
  );
}
