{"id": "onecrm", "realm": "onecrm", "displayName": "OneCRM", "displayNameHtml": "<div class=\"kc-logo-text\"><span>OneCRM</span></div>", "enabled": true, "sslRequired": "external", "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultRoles": ["default-roles-onecrm", "offline_access", "uma_authorization"], "requiredCredentials": ["password"], "passwordPolicy": "length(8) and digits(1) and lowerCase(1) and upperCase(1) and specialChars(1) and notUsername", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "accessTokenLifespan": 900, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "clients": [{"id": "onecrm-frontend", "clientId": "onecrm-frontend", "name": "OneCRM Frontend", "description": "OneCRM Frontend Application", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "onecrm-frontend-secret", "redirectUris": ["http://localhost:3000/*", "http://localhost:3000/auth/callback", "https://onecrm.local/*"], "webOrigins": ["http://localhost:3000", "https://onecrm.local"], "protocol": "openid-connect", "publicClient": true, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "authorizationServicesEnabled": false, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"name": "org_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "org_id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "org_id", "jsonType.label": "String"}}, {"name": "role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "role", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "role", "jsonType.label": "String"}}]}, {"id": "onecrm-backend", "clientId": "onecrm-backend", "name": "OneCRM Backend", "description": "OneCRM Backend API Service", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "onecrm-backend-secret", "protocol": "openid-connect", "publicClient": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "bearerOnly": true}], "roles": {"realm": [{"name": "admin", "description": "Organization Administrator", "composite": false, "clientRole": false, "containerId": "onecrm"}, {"name": "user", "description": "Regular User", "composite": false, "clientRole": false, "containerId": "onecrm"}, {"name": "viewer", "description": "Read-only User", "composite": false, "clientRole": false, "containerId": "onecrm"}]}, "groups": [{"name": "default-org", "path": "/default-org", "attributes": {"org_id": ["default-org-id"]}, "realmRoles": ["user"], "subGroups": []}], "users": [{"username": "<EMAIL>", "email": "<EMAIL>", "firstName": "Admin", "lastName": "User", "enabled": true, "emailVerified": true, "credentials": [{"type": "password", "value": "admin123", "temporary": false}], "realmRoles": ["admin", "user"], "groups": ["/default-org"], "attributes": {"org_id": ["default-org-id"], "role": ["admin"]}}], "scopeMappings": [], "clientScopeMappings": {}, "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "microprofile-jwt"], "defaultOptionalClientScopes": ["offline_access", "address", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": true, "eventsListeners": ["jboss-logging"], "enabledEventTypes": ["SEND_RESET_PASSWORD", "UPDATE_CONSENT_ERROR", "GRANT_CONSENT", "VERIFY_PROFILE_ERROR", "REMOVE_TOTP", "REVOKE_GRANT", "UPDATE_TOTP", "LOGIN_ERROR", "CLIENT_LOGIN", "RESET_PASSWORD_ERROR", "IMPERSONATE_ERROR", "CODE_TO_TOKEN_ERROR", "CUSTOM_REQUIRED_ACTION", "RESTART_AUTHENTICATION", "IMPERSONATE", "UPDATE_PROFILE_ERROR", "LOGIN", "UPDATE_PASSWORD_ERROR", "CLIENT_INITIATED_ACCOUNT_LINKING", "TOKEN_EXCHANGE", "LOGOUT", "REGISTER", "CLIENT_REGISTER", "IDENTITY_PROVIDER_LINK_ACCOUNT", "UPDATE_PASSWORD", "CLIENT_DELETE", "FEDERATED_IDENTITY_LINK", "IDENTITY_PROVIDER_FIRST_LOGIN", "CLIENT_DELETE_ERROR", "VERIFY_EMAIL", "CLIENT_LOGIN_ERROR", "RESTART_AUTHENTICATION_ERROR", "EXECUTE_ACTIONS", "REMOVE_FEDERATED_IDENTITY_ERROR", "TOKEN_EXCHANGE_ERROR", "PERMISSION_TOKEN", "SEND_IDENTITY_PROVIDER_LINK_ERROR", "EXECUTE_ACTION_TOKEN_ERROR", "SEND_VERIFY_EMAIL", "EXECUTE_ACTIONS_ERROR", "REMOVE_FEDERATED_IDENTITY", "IDENTITY_PROVIDER_POST_LOGIN", "IDENTITY_PROVIDER_LINK_ACCOUNT_ERROR", "UPDATE_EMAIL", "REGISTER_ERROR", "REVOKE_GRANT_ERROR", "LOGOUT_ERROR", "UPDATE_EMAIL_ERROR", "CLIENT_UPDATE_ERROR", "UPDATE_PROFILE", "FEDERATED_IDENTITY_LINK_ERROR", "CLIENT_REGISTER_ERROR", "SEND_IDENTITY_PROVIDER_LINK", "SEND_VERIFY_EMAIL_ERROR", "RESET_PASSWORD", "CLIENT_INITIATED_ACCOUNT_LINKING_ERROR", "UPDATE_CONSENT", "REMOVE_TOTP_ERROR", "VERIFY_EMAIL_ERROR", "SEND_RESET_PASSWORD_ERROR", "CLIENT_UPDATE", "IDENTITY_PROVIDER_POST_LOGIN_ERROR", "CUSTOM_REQUIRED_ACTION_ERROR", "UPDATE_TOTP_ERROR", "CODE_TO_TOKEN", "VERIFY_PROFILE", "GRANT_CONSENT_ERROR", "IDENTITY_PROVIDER_FIRST_LOGIN_ERROR"], "adminEventsEnabled": true, "adminEventsDetailsEnabled": true, "identityProviders": [], "identityProviderMappers": [], "internationalizationEnabled": true, "supportedLocales": ["en", "es", "fr", "de"], "defaultLocale": "en"}