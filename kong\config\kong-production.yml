_format_version: "3.0"
_transform: true

services:
  - name: crm-api
    url: http://crm-api:3001
    tags:
      - crm
      - api
      - production
    routes:
      - name: crm-api-route
        paths:
          - /api
        strip_path: false
        preserve_host: false
        protocols:
          - https
    plugins:
      - name: openid-connect
        config:
          issuer: "https://keycloak.onecrm.com/realms/onecrm"
          client_id: "onecrm-backend"
          client_secret: "{{ KEYCLOAK_CLIENT_SECRET }}"
          discovery: "https://keycloak.onecrm.com/realms/onecrm/.well-known/openid_configuration"
          scope: "openid profile email"
          response_type: "code"
          ssl_verify: true
          session_secret: "{{ SESSION_SECRET }}"
          introspection_endpoint: "https://keycloak.onecrm.com/realms/onecrm/protocol/openid-connect/token/introspect"
          bearer_only: true
          realm: "onecrm"
          introspect_jwt_tokens: true
          cache_introspection: true
          cache_ttl: 300
          anonymous: ""
          run_on_preflight: false
          upstream_headers_claims:
            - "sub:X-User-Sub"
            - "email:X-User-Email"
            - "org_id:X-Org-Id"
            - "role:X-User-Role"
          upstream_headers_names:
            - "sub:X-User-Sub"
            - "email:X-User-Email"
            - "org_id:X-Org-Id"
            - "role:X-User-Role"
      - name: cors
        config:
          origins:
            - "https://app.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - Authorization
            - X-Org-Id
          exposed_headers:
            - X-Auth-Token
            - X-Tenant-Id
            - X-Kong-Request-ID
          credentials: true
          max_age: 3600
      - name: rate-limiting
        config:
          minute: 1000
          hour: 10000
          day: 100000
          policy: redis
          redis_host: "redis"
          redis_port: 6379
          hide_client_headers: false
          fault_tolerant: true
      - name: request-transformer
        config:
          add:
            headers:
              - "X-Kong-Request-ID:$(kong.request.get_header('kong-request-id'))"
              - "X-Forwarded-Proto:https"
          remove:
            headers:
              - "X-Client-Org-Id"
      - name: response-transformer
        config:
          add:
            headers:
              - "X-Kong-Upstream-Latency:$(kong.response.get_header('x-kong-upstream-latency'))"
              - "X-Content-Type-Options:nosniff"
              - "X-Frame-Options:DENY"
              - "X-XSS-Protection:1; mode=block"
      - name: prometheus
        config:
          per_consumer: true
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true

  - name: auth-service
    url: http://auth-service:3002
    tags:
      - auth
      - service
      - production
    routes:
      - name: auth-service-route
        paths:
          - /auth
        strip_path: false
        preserve_host: false
        protocols:
          - https
    plugins:
      - name: cors
        config:
          origins:
            - "https://app.onecrm.com"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
          credentials: true

  - name: tenant-service
    url: http://tenant-service:3003
    tags:
      - tenant
      - service
      - production
    routes:
      - name: tenant-service-route
        paths:
          - /tenants
        strip_path: false
        preserve_host: false
        protocols:
          - https
    plugins:
      - name: openid-connect
        config:
          issuer: "https://keycloak.onecrm.com/realms/onecrm"
          client_id: "onecrm-backend"
          client_secret: "{{ KEYCLOAK_CLIENT_SECRET }}"
          bearer_only: true
          introspect_jwt_tokens: true
          upstream_headers_claims:
            - "sub:X-User-Sub"
            - "org_id:X-Org-Id"
            - "role:X-User-Role"

# Global plugins
plugins:
  - name: prometheus
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
  - name: request-id
    config:
      header_name: "Kong-Request-ID"
      generator: "uuid"
      echo_downstream: true
  - name: correlation-id
    config:
      header_name: "Kong-Correlation-ID"
      generator: "uuid"
      echo_downstream: true

# Consumers (will be populated by Keycloak integration)
consumers: []

# Upstreams for load balancing
upstreams:
  - name: crm-api-upstream
    algorithm: round-robin
    healthchecks:
      active:
        http_path: "/api/health"
        healthy:
          interval: 30
          successes: 1
        unhealthy:
          interval: 30
          http_failures: 3
    targets:
      - target: "crm-api:3001"
        weight: 100

# Certificates (to be configured with actual SSL certificates)
certificates: []

# SNIs (Server Name Indication)
snis: []
