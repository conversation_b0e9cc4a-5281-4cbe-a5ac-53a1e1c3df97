#!/bin/bash

# OneCRM Setup Script
# This script sets up the development environment for OneCRM

set -e

echo "🚀 Setting up OneCRM development environment..."

# Check if required tools are installed
check_requirements() {
    echo "📋 Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        echo "❌ Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo "❌ npm is not installed. Please install npm."
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed. Please install Docker."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose is not installed. Please install Docker Compose."
        exit 1
    fi
    
    echo "✅ All requirements satisfied"
}

# Install dependencies
install_dependencies() {
    echo "📦 Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Install frontend dependencies
    cd frontend && npm install && cd ..
    
    # Install backend dependencies
    cd backend/crm-api && npm install && cd ../..
    
    echo "✅ Dependencies installed"
}

# Setup environment files
setup_environment() {
    echo "🔧 Setting up environment files..."
    
    if [ ! -f .env ]; then
        cp .env.development .env
        echo "✅ Created .env file from .env.development"
    else
        echo "ℹ️  .env file already exists"
    fi
    
    if [ ! -f frontend/.env.local ]; then
        cat > frontend/.env.local << EOF
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
EOF
        echo "✅ Created frontend/.env.local"
    else
        echo "ℹ️  frontend/.env.local already exists"
    fi
}

# Start infrastructure services
start_infrastructure() {
    echo "🐳 Starting infrastructure services..."
    
    # Start PostgreSQL, Redis, Keycloak, and Kong
    docker-compose -f docker-compose.dev.yml up -d postgres redis keycloak kong
    
    echo "⏳ Waiting for services to be ready..."
    sleep 30
    
    # Check if services are healthy
    if docker-compose -f docker-compose.dev.yml ps | grep -q "unhealthy"; then
        echo "❌ Some services are not healthy. Please check docker-compose logs."
        docker-compose -f docker-compose.dev.yml logs
        exit 1
    fi
    
    echo "✅ Infrastructure services started"
}

# Setup Keycloak realm
setup_keycloak() {
    echo "🔐 Setting up Keycloak realm..."
    
    # Wait for Keycloak to be fully ready
    echo "⏳ Waiting for Keycloak to be ready..."
    until curl -s http://localhost:8080/realms/master > /dev/null; do
        sleep 5
    done
    
    echo "✅ Keycloak is ready"
    echo "ℹ️  Please manually configure Keycloak realm 'onecrm' at http://localhost:8080"
    echo "ℹ️  Admin credentials: admin/admin"
}

# Run database migrations
run_migrations() {
    echo "🗄️  Running database migrations..."
    
    # The init.sql script should have already run via docker-compose
    echo "✅ Database initialized"
}

# Final setup
final_setup() {
    echo "🎉 Setup complete!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure Keycloak realm at http://localhost:8080 (admin/admin)"
    echo "2. Run 'npm run dev' to start the development servers"
    echo "3. Access the application at http://localhost:3000"
    echo ""
    echo "🔗 Useful URLs:"
    echo "- Frontend: http://localhost:3000"
    echo "- API: http://localhost:3001"
    echo "- Kong Gateway: http://localhost:8000"
    echo "- Kong Admin: http://localhost:8001"
    echo "- Kong Manager: http://localhost:8002"
    echo "- Keycloak: http://localhost:8080"
    echo "- API Documentation: http://localhost:3001/api/docs"
}

# Main execution
main() {
    check_requirements
    install_dependencies
    setup_environment
    start_infrastructure
    setup_keycloak
    run_migrations
    final_setup
}

# Run main function
main "$@"
