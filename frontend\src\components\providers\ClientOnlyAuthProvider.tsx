'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// Check if we should use <PERSON>Auth or <PERSON><PERSON> Auth
const useNextAuth = process.env.NEXT_PUBLIC_KEYCLOAK_ENABLED === 'true';

// Dynamically import the appropriate AuthProvider
const AuthProvider = dynamic(
  () => {
    if (useNextAuth) {
      return import('./NextAuthProvider').then((mod) => ({ default: mod.NextAuthProvider }));
    } else {
      return import('./MockAuthProvider').then((mod) => ({ default: mod.MockAuthProvider }));
    }
  },
  {
    ssr: false,
    loading: () => <div>Loading authentication...</div>,
  }
);

interface ClientOnlyAuthProviderProps {
  children: React.ReactNode;
}

export const ClientOnlyAuthProvider: React.FC<ClientOnlyAuthProviderProps> = ({ children }) => {
  return <AuthProvider>{children}</AuthProvider>;
};
