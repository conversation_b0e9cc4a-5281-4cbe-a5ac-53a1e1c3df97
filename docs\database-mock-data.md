# Database Setup with Mock Data for OneCRM

## Database Schema Overview

```mermaid
erDiagram
    ORGANIZATIONS ||--o{ USERS : has
    ORGANIZATIONS ||--o{ DEPARTMENTS : contains
    ORGANIZATIONS ||--o{ CONTACTS : owns
    ORGANIZATIONS ||--o{ COMPANIES : manages
    ORGANIZATIONS ||--o{ DEALS : tracks

    USERS ||--o{ CONTACTS : manages
    USERS ||--o{ DEALS : owns
    USERS ||--o{ ACTIVITIES : creates
    USERS }o--|| DEPARTMENTS : belongs_to

    CONTACTS ||--o{ DEALS : generates
    CONTACTS }o--|| COMPANIES : works_for
    CONTACTS ||--o{ ACTIVITIES : involved_in

    COMPANIES ||--o{ CONTACTS : employs
    COMPANIES ||--o{ DEALS : participates_in

    DEALS ||--o{ ACTIVITIES : has
    DEALS }o--|| USERS : assigned_to

    ORGANIZATIONS {
        uuid id PK
        string name
        string domain
        jsonb settings
        timestamp created_at
        timestamp updated_at
    }

    DEPARTMENTS {
        uuid id PK
        uuid organization_id FK
        string name
        string description
        uuid manager_id FK
        timestamp created_at
    }

    USERS {
        uuid id PK
        string keycloak_id UK
        uuid organization_id FK
        uuid department_id FK
        string email UK
        string first_name
        string last_name
        string role
        jsonb attributes
        boolean active
        timestamp created_at
        timestamp updated_at
    }

    CONTACTS {
        uuid id PK
        uuid organization_id FK
        uuid assigned_to FK
        uuid company_id FK
        string first_name
        string last_name
        string email
        string phone
        string status
        string source
        jsonb custom_fields
        timestamp created_at
        timestamp updated_at
    }

    COMPANIES {
        uuid id PK
        uuid organization_id FK
        string name
        string industry
        string website
        string phone
        text address
        integer employee_count
        decimal annual_revenue
        timestamp created_at
        timestamp updated_at
    }

    DEALS {
        uuid id PK
        uuid organization_id FK
        uuid contact_id FK
        uuid company_id FK
        uuid assigned_to FK
        string title
        decimal amount
        string currency
        string stage
        string probability
        date expected_close_date
        text description
        timestamp created_at
        timestamp updated_at
    }

    ACTIVITIES {
        uuid id PK
        uuid organization_id FK
        uuid user_id FK
        uuid contact_id FK
        uuid deal_id FK
        string type
        string subject
        text description
        timestamp activity_date
        timestamp created_at
    }
```

## Data Flow Architecture

```mermaid
graph TB
    subgraph "Data Sources"
        KC[Keycloak Users] --> DB[(PostgreSQL)]
        EXT[External APIs] --> DB
        CSV[CSV Import] --> DB
    end

    subgraph "Database Layer"
        DB --> ORG[Organizations]
        DB --> DEPT[Departments]
        DB --> USR[Users]
        DB --> CONT[Contacts]
        DB --> COMP[Companies]
        DB --> DEAL[Deals]
        DB --> ACT[Activities]
    end

    subgraph "Business Logic"
        ORG --> RBAC[RBAC Filter]
        DEPT --> RBAC
        USR --> RBAC
        CONT --> RBAC
        COMP --> RBAC
        DEAL --> RBAC
        ACT --> RBAC
    end

    subgraph "API Layer"
        RBAC --> API[REST API]
        API --> AUTH[Auth Middleware]
        AUTH --> VALID[Token Validation]
    end

    subgraph "Frontend"
        VALID --> FE[Next.js App]
        FE --> DASH[Dashboard]
        FE --> CONTACTS[Contacts Page]
        FE --> DEALS[Deals Page]
        FE --> REPORTS[Reports]
    end

    style KC fill:#f9f,stroke:#333,stroke-width:2px
    style DB fill:#bbf,stroke:#333,stroke-width:2px
    style RBAC fill:#bfb,stroke:#333,stroke-width:2px
    style API fill:#fbf,stroke:#333,stroke-width:2px
```

## Multi-Tenant Data Isolation

```mermaid
graph LR
    subgraph "Organization A Data"
        A_ORG[Org A]
        A_USERS[Users A]
        A_CONTACTS[Contacts A]
        A_DEALS[Deals A]

        A_ORG --> A_USERS
        A_ORG --> A_CONTACTS
        A_ORG --> A_DEALS
    end

    subgraph "Organization B Data"
        B_ORG[Org B]
        B_USERS[Users B]
        B_CONTACTS[Contacts B]
        B_DEALS[Deals B]

        B_ORG --> B_USERS
        B_ORG --> B_CONTACTS
        B_ORG --> B_DEALS
    end

    subgraph "Shared Infrastructure"
        DB[(Database)]
        API[API Layer]
        AUTH[Auth Service]
    end

    A_ORG -.-> DB
    B_ORG -.-> DB

    DB --> API
    API --> AUTH

    AUTH -.-> |Filter A Data| A_ORG
    AUTH -.-> |Filter B Data| B_ORG

    style A_ORG fill:#e1f5fe
    style B_ORG fill:#f3e5f5
    style DB fill:#e8f5e8
    style AUTH fill:#fff3e0
```

## Database Migration Scripts

### 1. Create Database and Extensions

```sql
-- Create database
CREATE DATABASE onecrm;

-- Connect to database
\c onecrm;

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
```

### 2. Create Tables

```sql
-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Departments table
CREATE TABLE departments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    manager_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, name)
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keycloak_id VARCHAR(255) UNIQUE NOT NULL,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    department_id UUID REFERENCES departments(id) ON DELETE SET NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    role VARCHAR(100) NOT NULL,
    attributes JSONB DEFAULT '{}',
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Companies table
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    industry VARCHAR(255),
    website VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    employee_count INTEGER,
    annual_revenue DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contacts table
CREATE TABLE contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    status VARCHAR(50) DEFAULT 'new',
    source VARCHAR(100),
    custom_fields JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Deals table
CREATE TABLE deals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE SET NULL,
    company_id UUID REFERENCES companies(id) ON DELETE SET NULL,
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    stage VARCHAR(100) DEFAULT 'prospecting',
    probability INTEGER DEFAULT 10,
    expected_close_date DATE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Activities table
CREATE TABLE activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    contact_id UUID REFERENCES contacts(id) ON DELETE CASCADE,
    deal_id UUID REFERENCES deals(id) ON DELETE CASCADE,
    type VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    description TEXT,
    activity_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key for department manager
ALTER TABLE departments ADD CONSTRAINT fk_departments_manager 
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

-- Create indexes
CREATE INDEX idx_users_organization ON users(organization_id);
CREATE INDEX idx_users_keycloak ON users(keycloak_id);
CREATE INDEX idx_contacts_organization ON contacts(organization_id);
CREATE INDEX idx_contacts_assigned ON contacts(assigned_to);
CREATE INDEX idx_deals_organization ON deals(organization_id);
CREATE INDEX idx_deals_assigned ON deals(assigned_to);
CREATE INDEX idx_activities_user ON activities(user_id);
CREATE INDEX idx_activities_contact ON activities(contact_id);
```

### 3. Insert Mock Data

```sql
-- Insert Organizations
INSERT INTO organizations (id, name, domain, settings) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'TechCorp Solutions', 'techcorp.com', '{"timezone": "America/New_York", "currency": "USD"}'),
('550e8400-e29b-41d4-a716-446655440002', 'StartupInc', 'startupinc.com', '{"timezone": "America/Los_Angeles", "currency": "USD"}'),
('550e8400-e29b-41d4-a716-446655440003', 'Global Enterprises', 'globalent.com', '{"timezone": "UTC", "currency": "EUR"}');

-- Insert Departments
INSERT INTO departments (id, organization_id, name, description) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Sales', 'Sales and Business Development'),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Marketing', 'Marketing and Lead Generation'),
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440001', 'Support', 'Customer Support and Success'),
('650e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440001', 'Administration', 'Administrative Functions'),
('650e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440002', 'Sales', 'Sales Team'),
('650e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440002', 'Administration', 'Admin Team');

-- Insert Users (matching Keycloak demo users)
INSERT INTO users (id, keycloak_id, organization_id, department_id, email, first_name, last_name, role, attributes) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'admin.techcorp', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'John', 'Smith', 'org-admin', '{"territory": "Global", "cost_center": "CC-ADMIN"}'),
('750e8400-e29b-41d4-a716-446655440002', 'admin.startup', '550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'Sarah', 'Johnson', 'org-admin', '{"territory": "North America", "cost_center": "CC-ADMIN"}'),
('750e8400-e29b-41d4-a716-446655440003', 'manager.sales', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Michael', 'Brown', 'dept-manager', '{"territory": "North America", "cost_center": "CC-SALES", "team_size": 8}'),
('750e8400-e29b-41d4-a716-446655440004', 'manager.marketing', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Lisa', 'Davis', 'dept-manager', '{"territory": "Global", "cost_center": "CC-MARKETING", "team_size": 5}'),
('750e8400-e29b-41d4-a716-446655440005', 'manager.support', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'David', 'Wilson', 'dept-manager', '{"territory": "Global", "cost_center": "CC-SUPPORT", "team_size": 12}'),
('750e8400-e29b-41d4-a716-446655440006', 'sales.senior', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Robert', 'Taylor', 'sales-rep', '{"territory": "West Coast", "quota": 500000, "commission_rate": 0.08}'),
('750e8400-e29b-41d4-a716-446655440007', 'sales.junior', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Emily', 'Anderson', 'sales-rep', '{"territory": "East Coast", "quota": 300000, "commission_rate": 0.06}'),
('750e8400-e29b-41d4-a716-446655440008', 'sales.startup', '550e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Alex', 'Martinez', 'sales-rep', '{"territory": "North America", "quota": 200000, "commission_rate": 0.10}'),
('750e8400-e29b-41d4-a716-446655440009', 'marketing.specialist', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Jessica', 'Lee', 'marketing-user', '{"specialization": "Digital Marketing", "cost_center": "CC-MARKETING"}'),
('750e8400-e29b-41d4-a716-446655440010', 'support.l1', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Kevin', 'Garcia', 'support-agent', '{"support_level": "L1", "shift": "Day"}'),
('750e8400-e29b-41d4-a716-446655440011', 'support.l2', '550e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Amanda', 'Rodriguez', 'support-agent', '{"support_level": "L2", "shift": "Night"}');

-- Update department managers
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440003' WHERE id = '650e8400-e29b-41d4-a716-446655440001';
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440004' WHERE id = '650e8400-e29b-41d4-a716-446655440002';
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440005' WHERE id = '650e8400-e29b-41d4-a716-446655440003';
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440001' WHERE id = '650e8400-e29b-41d4-a716-446655440004';
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440008' WHERE id = '650e8400-e29b-41d4-a716-446655440005';
UPDATE departments SET manager_id = '750e8400-e29b-41d4-a716-446655440002' WHERE id = '650e8400-e29b-41d4-a716-446655440006';
```

## Mock Data Generation Script

### Node.js Script for Large Dataset

```javascript
// scripts/generate-mock-data.js
const { Pool } = require('pg');
const { faker } = require('@faker-js/faker');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'onecrm',
  password: 'your_password',
  port: 5432,
});

async function generateMockData() {
  const client = await pool.connect();
  
  try {
    // Generate Companies
    console.log('Generating companies...');
    for (let i = 0; i < 100; i++) {
      const orgId = i < 70 ? '550e8400-e29b-41d4-a716-446655440001' : 
                   i < 90 ? '550e8400-e29b-41d4-a716-446655440002' : 
                           '550e8400-e29b-41d4-a716-446655440003';
      
      await client.query(`
        INSERT INTO companies (organization_id, name, industry, website, phone, address, employee_count, annual_revenue)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        orgId,
        faker.company.name(),
        faker.company.buzzNoun(),
        faker.internet.url(),
        faker.phone.number(),
        faker.location.streetAddress(),
        faker.number.int({ min: 10, max: 10000 }),
        faker.number.float({ min: 100000, max: 50000000, precision: 0.01 })
      ]);
    }

    // Generate Contacts
    console.log('Generating contacts...');
    const companies = await client.query('SELECT id, organization_id FROM companies');
    const users = await client.query('SELECT id, organization_id FROM users WHERE role IN (\'sales-rep\', \'dept-manager\')');
    
    for (let i = 0; i < 500; i++) {
      const company = faker.helpers.arrayElement(companies.rows);
      const assignedUser = faker.helpers.arrayElement(
        users.rows.filter(u => u.organization_id === company.organization_id)
      );
      
      await client.query(`
        INSERT INTO contacts (organization_id, assigned_to, company_id, first_name, last_name, email, phone, status, source)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `, [
        company.organization_id,
        assignedUser?.id || null,
        company.id,
        faker.person.firstName(),
        faker.person.lastName(),
        faker.internet.email(),
        faker.phone.number(),
        faker.helpers.arrayElement(['new', 'qualified', 'contacted', 'converted', 'lost']),
        faker.helpers.arrayElement(['website', 'referral', 'cold_call', 'email', 'social_media', 'trade_show'])
      ]);
    }

    // Generate Deals
    console.log('Generating deals...');
    const contacts = await client.query('SELECT id, organization_id, company_id, assigned_to FROM contacts');
    
    for (let i = 0; i < 200; i++) {
      const contact = faker.helpers.arrayElement(contacts.rows);
      
      await client.query(`
        INSERT INTO deals (organization_id, contact_id, company_id, assigned_to, title, amount, stage, probability, expected_close_date, description)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `, [
        contact.organization_id,
        contact.id,
        contact.company_id,
        contact.assigned_to,
        faker.commerce.productName() + ' Deal',
        faker.number.float({ min: 5000, max: 500000, precision: 0.01 }),
        faker.helpers.arrayElement(['prospecting', 'qualification', 'proposal', 'negotiation', 'closed_won', 'closed_lost']),
        faker.number.int({ min: 10, max: 90 }),
        faker.date.future(),
        faker.lorem.paragraph()
      ]);
    }

    // Generate Activities
    console.log('Generating activities...');
    const deals = await client.query('SELECT id, organization_id, contact_id FROM deals');
    
    for (let i = 0; i < 1000; i++) {
      const deal = faker.helpers.arrayElement(deals.rows);
      const user = faker.helpers.arrayElement(
        users.rows.filter(u => u.organization_id === deal.organization_id)
      );
      
      await client.query(`
        INSERT INTO activities (organization_id, user_id, contact_id, deal_id, type, subject, description, activity_date)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        deal.organization_id,
        user?.id || null,
        deal.contact_id,
        deal.id,
        faker.helpers.arrayElement(['call', 'email', 'meeting', 'note', 'task']),
        faker.lorem.sentence(),
        faker.lorem.paragraph(),
        faker.date.recent({ days: 30 })
      ]);
    }

    console.log('Mock data generation completed!');
  } catch (error) {
    console.error('Error generating mock data:', error);
  } finally {
    client.release();
  }
}

generateMockData();
```

## Database Connection Configuration

### Backend Environment Variables

```bash
# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/onecrm
DB_HOST=localhost
DB_PORT=5432
DB_NAME=onecrm
DB_USER=postgres
DB_PASSWORD=your_password

# Connection Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
```

### TypeORM Configuration (if using TypeORM)

```typescript
// src/config/database.ts
export const databaseConfig = {
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME || 'onecrm',
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*{.ts,.js}'],
  synchronize: false,
  logging: process.env.NODE_ENV === 'development',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};
```

## Data Validation and Testing

### 1. Verify Data Integrity

```sql
-- Check organization data
SELECT o.name, COUNT(u.id) as user_count, COUNT(c.id) as contact_count, COUNT(d.id) as deal_count
FROM organizations o
LEFT JOIN users u ON o.id = u.organization_id
LEFT JOIN contacts c ON o.id = c.organization_id
LEFT JOIN deals d ON o.id = d.organization_id
GROUP BY o.id, o.name;

-- Check user assignments
SELECT u.first_name, u.last_name, u.role, d.name as department, 
       COUNT(c.id) as assigned_contacts, COUNT(dl.id) as assigned_deals
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN contacts c ON u.id = c.assigned_to
LEFT JOIN deals dl ON u.id = dl.assigned_to
GROUP BY u.id, u.first_name, u.last_name, u.role, d.name;

-- Check deal pipeline
SELECT stage, COUNT(*) as deal_count, SUM(amount) as total_value
FROM deals
GROUP BY stage
ORDER BY 
  CASE stage
    WHEN 'prospecting' THEN 1
    WHEN 'qualification' THEN 2
    WHEN 'proposal' THEN 3
    WHEN 'negotiation' THEN 4
    WHEN 'closed_won' THEN 5
    WHEN 'closed_lost' THEN 6
  END;
```

### 2. Performance Testing Queries

```sql
-- Test contact search performance
EXPLAIN ANALYZE SELECT * FROM contacts 
WHERE organization_id = '550e8400-e29b-41d4-a716-446655440001' 
AND (first_name ILIKE '%john%' OR last_name ILIKE '%john%' OR email ILIKE '%john%');

-- Test deal aggregation performance
EXPLAIN ANALYZE SELECT 
  assigned_to,
  COUNT(*) as deal_count,
  SUM(amount) as total_value,
  AVG(amount) as avg_value
FROM deals 
WHERE organization_id = '550e8400-e29b-41d4-a716-446655440001'
GROUP BY assigned_to;
```

## Quick Setup Commands

### 1. Create Database and Run Migrations
```bash
# Create database
createdb onecrm

# Run migration script
psql -d onecrm -f scripts/create-tables.sql
psql -d onecrm -f scripts/insert-mock-data.sql
```

### 2. Generate Additional Mock Data
```bash
# Install dependencies
npm install @faker-js/faker pg

# Run mock data generator
node scripts/generate-mock-data.js
```

### 3. Verify Setup
```bash
# Check data counts
psql -d onecrm -c "
SELECT
  'Organizations' as table_name, COUNT(*) as count FROM organizations
UNION ALL
SELECT 'Users', COUNT(*) FROM users
UNION ALL
SELECT 'Contacts', COUNT(*) FROM contacts
UNION ALL
SELECT 'Companies', COUNT(*) FROM companies
UNION ALL
SELECT 'Deals', COUNT(*) FROM deals
UNION ALL
SELECT 'Activities', COUNT(*) FROM activities;
"
```

## API Endpoints for Testing

### 1. User Data by Role
```
GET /api/users/profile - Current user profile
GET /api/users/team - Team members (managers only)
GET /api/users/department - Department users (managers only)
GET /api/users/organization - All org users (org-admin only)
```

### 2. Contact Access by Role
```
GET /api/contacts - Filtered by user permissions
GET /api/contacts/assigned - User's assigned contacts
GET /api/contacts/team - Team contacts (managers only)
POST /api/contacts - Create contact (permission required)
```

### 3. Deal Access by Role
```
GET /api/deals - User's accessible deals
GET /api/deals/pipeline - Pipeline view with permissions
PUT /api/deals/:id/approve - Approve deal (manager only)
```

This comprehensive database setup provides realistic mock data that matches the RBAC structure and allows for thorough testing of all CRM functionality across different user roles and organizations.
