import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Organization } from '../../organization/entities/organization.entity';

export enum PortalSessionType {
  BILLING = 'billing',
  ACCOUNT_MANAGEMENT = 'account_management',
  USAGE_DASHBOARD = 'usage_dashboard',
  SUPPORT = 'support',
}

export enum PortalSessionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
}

@Entity('portal_sessions')
@Index(['organizationId', 'status'])
@Index(['sessionToken'], { unique: true })
export class PortalSession {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ type: 'varchar', length: 255, name: 'user_id' })
  userId: string;

  @Column({ type: 'varchar', length: 255, name: 'user_email' })
  userEmail: string;

  @Column({
    type: 'enum',
    enum: PortalSessionType,
  })
  type: PortalSessionType;

  @Column({
    type: 'enum',
    enum: PortalSessionStatus,
    default: PortalSessionStatus.ACTIVE,
  })
  status: PortalSessionStatus;

  @Column({ type: 'varchar', length: 512, name: 'session_token' })
  sessionToken: string;

  @Column({ type: 'varchar', length: 512, nullable: true, name: 'return_url' })
  returnUrl: string;

  @Column({ type: 'timestamp', name: 'expires_at' })
  expiresAt: Date;

  @Column({ type: 'timestamp', nullable: true, name: 'last_accessed_at' })
  lastAccessedAt: Date;

  @Column({ type: 'varchar', length: 45, nullable: true, name: 'ip_address' })
  ipAddress: string;

  @Column({ type: 'text', nullable: true, name: 'user_agent' })
  userAgent: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    permissions?: string[];
    features?: string[];
    restrictions?: string[];
    customData?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true, name: 'access_log' })
  accessLog: Array<{
    timestamp: Date;
    action: string;
    ipAddress?: string;
    userAgent?: string;
  }>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get isActive(): boolean {
    return this.status === PortalSessionStatus.ACTIVE && 
           this.expiresAt && 
           new Date() < this.expiresAt;
  }

  get isExpired(): boolean {
    return this.expiresAt && new Date() > this.expiresAt;
  }

  get timeUntilExpiry(): number {
    if (!this.expiresAt) return 0;
    const now = new Date();
    const diff = this.expiresAt.getTime() - now.getTime();
    return Math.max(0, diff);
  }

  get minutesUntilExpiry(): number {
    return Math.round(this.timeUntilExpiry / (1000 * 60));
  }
}
