'use client';

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  LinearProgress,
  Chip,
  Avatar,
} from '@mui/material';
import {
  TrendingUp,
  CalendarToday,
  AttachMoney,
  Assessment,
  Timeline,
} from '@mui/icons-material';

interface ForecastData {
  currentQuarter: {
    target: number;
    actual: number;
    projected: number;
    progress: number;
  };
  nextQuarter: {
    target: number;
    projected: number;
    confidence: number;
  };
  monthlyForecast: Array<{
    month: string;
    projected: number;
    confidence: number;
    deals: number;
  }>;
  riskFactors: Array<{
    factor: string;
    impact: 'high' | 'medium' | 'low';
    description: string;
  }>;
}

interface DealsForecastProps {
  forecast?: ForecastData;
  loading?: boolean;
}

export const DealsForecast: React.FC<DealsForecastProps> = ({
  forecast,
  loading = false,
}) => {
  // Default forecast data if none provided
  const defaultForecast: ForecastData = {
    currentQuarter: {
      target: 1000000,
      actual: 750000,
      projected: 950000,
      progress: 75,
    },
    nextQuarter: {
      target: 1200000,
      projected: 1100000,
      confidence: 85,
    },
    monthlyForecast: [
      { month: 'January', projected: 400000, confidence: 90, deals: 12 },
      { month: 'February', projected: 350000, confidence: 85, deals: 10 },
      { month: 'March', projected: 450000, confidence: 80, deals: 15 },
    ],
    riskFactors: [
      { factor: 'Economic Uncertainty', impact: 'medium', description: 'Market volatility may affect deal closure' },
      { factor: 'Competition', impact: 'high', description: 'Increased competition in key segments' },
    ],
  };

  const forecastData = forecast || defaultForecast;
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getRiskColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'success';
    if (confidence >= 60) return 'warning';
    return 'error';
  };

  if (loading) {
    return (
      <Grid container spacing={3}>
        {[1, 2, 3, 4].map((i) => (
          <Grid item xs={12} md={6} key={i}>
            <Card>
              <CardContent>
                <Box sx={{ height: 120 }}>
                  <LinearProgress />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Current Quarter Performance */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CalendarToday />
              Current Quarter
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">Progress to Target</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {forecastData.currentQuarter.progress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={forecastData.currentQuarter.progress}
                sx={{ height: 8, borderRadius: 4 }}
                color={forecastData.currentQuarter.progress >= 75 ? 'success' : 'warning'}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Typography variant="caption" color="text.secondary">Target</Typography>
                <Typography variant="h6" fontWeight="bold">
                  {formatCurrency(forecastData.currentQuarter.target)}
                </Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="caption" color="text.secondary">Actual</Typography>
                <Typography variant="h6" fontWeight="bold" color="success.main">
                  {formatCurrency(forecastData.currentQuarter.actual)}
                </Typography>
              </Grid>
              <Grid item xs={4}>
                <Typography variant="caption" color="text.secondary">Projected</Typography>
                <Typography variant="h6" fontWeight="bold" color="primary.main">
                  {formatCurrency(forecastData.currentQuarter.projected)}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Next Quarter Forecast */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingUp />
              Next Quarter Forecast
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="body2">Confidence Level</Typography>
                <Chip
                  label={`${forecastData.nextQuarter.confidence}%`}
                  color={getConfidenceColor(forecastData.nextQuarter.confidence)}
                  size="small"
                />
              </Box>
              <LinearProgress
                variant="determinate"
                value={forecastData.nextQuarter.confidence}
                sx={{ height: 8, borderRadius: 4 }}
                color={getConfidenceColor(forecastData.nextQuarter.confidence)}
              />
            </Box>

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">Target</Typography>
                <Typography variant="h6" fontWeight="bold">
                  {formatCurrency(forecastData.nextQuarter.target)}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption" color="text.secondary">Projected</Typography>
                <Typography variant="h6" fontWeight="bold" color="primary.main">
                  {formatCurrency(forecastData.nextQuarter.projected)}
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Monthly Forecast */}
      <Grid item xs={12} md={8}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Timeline />
              Monthly Forecast
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              {forecastData.monthlyForecast.map((month) => (
                <Box key={month.month} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {month.month}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Chip
                        label={`${month.confidence}% confidence`}
                        size="small"
                        color={getConfidenceColor(month.confidence)}
                      />
                      <Typography variant="body2" fontWeight="bold">
                        {formatCurrency(month.projected)}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {month.deals} deals expected to close
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={month.confidence}
                    sx={{ 
                      height: 4, 
                      borderRadius: 2, 
                      mt: 1,
                      bgcolor: 'grey.200'
                    }}
                    color={getConfidenceColor(month.confidence)}
                  />
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Risk Factors */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Assessment />
              Risk Factors
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              {forecastData.riskFactors.map((risk, index) => (
                <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {risk.factor}
                    </Typography>
                    <Chip
                      label={risk.impact.toUpperCase()}
                      size="small"
                      color={getRiskColor(risk.impact) as any}
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {risk.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};
