'use client';

import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Skeleton,
} from '@mui/material';
import {
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Source as SourceIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
  isLoading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box>
              <Skeleton variant="text" width={120} height={24} />
              <Skeleton variant="text" width={80} height={40} />
              {subtitle && <Skeleton variant="text" width={100} height={20} />}
            </Box>
            <Skeleton variant="circular" width={48} height={48} />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 600 }}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Avatar
            sx={{
              bgcolor: color,
              width: 48,
              height: 48,
            }}
          >
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );
};

export const ContactsStats: React.FC = () => {
  const { data: stats, isLoading } = useSWR('/api/contacts/stats');

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getTopStatus = () => {
    if (!stats?.byStatus) return 'N/A';
    const entries = Object.entries(stats.byStatus);
    if (entries.length === 0) return 'N/A';
    const [topStatus] = entries.reduce((a, b) => (a[1] as number) > (b[1] as number) ? a : b);
    return topStatus.charAt(0).toUpperCase() + topStatus.slice(1);
  };

  const getTopSource = () => {
    if (!stats?.bySource) return 'N/A';
    const entries = Object.entries(stats.bySource);
    if (entries.length === 0) return 'N/A';
    const [topSource] = entries.reduce((a, b) => (a[1] as number) > (b[1] as number) ? a : b);
    return topSource.charAt(0).toUpperCase() + topSource.slice(1);
  };

  const statsData = [
    {
      title: 'Total Contacts',
      value: stats ? formatNumber(stats.total) : 0,
      subtitle: stats?.recentlyCreated ? `+${stats.recentlyCreated} this month` : undefined,
      icon: <PeopleIcon />,
      color: 'primary.main',
      isLoading,
    },
    {
      title: 'Top Status',
      value: getTopStatus(),
      subtitle: stats?.byStatus ? `${Object.keys(stats.byStatus).length} statuses` : undefined,
      icon: <TrendingUpIcon />,
      color: 'success.main',
      isLoading,
    },
    {
      title: 'Top Source',
      value: getTopSource(),
      subtitle: stats?.bySource ? `${Object.keys(stats.bySource).length} sources` : undefined,
      icon: <SourceIcon />,
      color: 'warning.main',
      isLoading,
    },
    {
      title: 'Recent Activity',
      value: stats?.recentlyCreated || 0,
      subtitle: 'New contacts this month',
      icon: <AssignmentIcon />,
      color: 'info.main',
      isLoading,
    },
  ];

  return (
    <Grid container spacing={3}>
      {statsData.map((stat, index) => (
        <Grid item xs={12} sm={6} md={3} key={index}>
          <StatCard {...stat} subtitle={stat.subtitle || ''} />
        </Grid>
      ))}
    </Grid>
  );
};
