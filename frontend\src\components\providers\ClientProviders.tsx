'use client';

import React, { Suspense, useEffect } from 'react';
import { ThemeProvider } from './theme-provider';
import { ToastProviderComponent } from '@/components/ui/toast';
import { ErrorBoundary, setupGlobalErrorHandling } from '@/components/error-boundary';
import { CommandPalette } from '@/components/ui/command-palette';
import { performanceMonitor } from '@/lib/performance';
import { LoadingSpinner } from '../common/LoadingComponents';
import { ClientOnlyAuthProvider } from './ClientOnlyAuthProvider';
import { TenantProvider } from './TenantProvider';

// Simple providers that don't require authentication
interface ClientProvidersProps {
  children: React.ReactNode;
}

export const ClientProviders: React.FC<ClientProvidersProps> = ({ children }) => {
  useEffect(() => {
    // Initialize global error handling
    setupGlobalErrorHandling()

    // Initialize performance monitoring
    performanceMonitor.init()

    return () => {
      performanceMonitor.cleanup()
    }
  }, [])

  return (
    <ErrorBoundary level="page">
      <ThemeProvider defaultTheme="system" storageKey="onecrm-theme">
        <ToastProviderComponent>
          <ClientOnlyAuthProvider>
            <TenantProvider>
              <Suspense fallback={<LoadingSpinner message="Loading application..." />}>
                {children}
                <CommandPalette />
              </Suspense>
            </TenantProvider>
          </ClientOnlyAuthProvider>
        </ToastProviderComponent>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

// For when authentication is needed (can be used in specific pages)
export const AuthenticatedProviders: React.FC<ClientProvidersProps> = ({ children }) => {
  // This will be used later when we need authentication
  // For now, we'll keep it simple to avoid SSR issues
  return (
    <ClientProviders>
      {children}
    </ClientProviders>
  );
};
