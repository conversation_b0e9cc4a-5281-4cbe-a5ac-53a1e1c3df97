'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  IconButton,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar,
  Button,
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Business,
  AccountTree,
  Add,
  Edit,
} from '@mui/icons-material';

interface Company {
  id: string;
  name: string;
  industry: string;
  employees?: number;
  revenue?: number;
  subsidiaries?: Company[];
  parentId?: string;
}

interface CompanyHierarchyProps {
  companies?: Company[];
  onEditCompany?: (company: Company) => void;
  onAddSubsidiary?: (parentId: string) => void;
  open?: boolean;
  onClose?: () => void;
  loading?: boolean;
}

const CompanyNode: React.FC<{
  company: Company;
  level: number;
  onEdit?: (company: Company) => void;
  onAddSubsidiary?: (parentId: string) => void;
}> = ({ company, level, onEdit, onAddSubsidiary }) => {
  const [expanded, setExpanded] = useState(level < 2); // Auto-expand first 2 levels

  const hasSubsidiaries = company.subsidiaries && company.subsidiaries.length > 0;
  const indentLevel = level * 20;

  const formatRevenue = (revenue?: number) => {
    if (!revenue) return 'N/A';
    if (revenue >= 1000000) return `$${(revenue / 1000000).toFixed(1)}M`;
    if (revenue >= 1000) return `$${(revenue / 1000).toFixed(1)}K`;
    return `$${revenue}`;
  };

  const formatEmployees = (employees?: number) => {
    if (!employees) return 'N/A';
    return employees.toLocaleString();
  };

  return (
    <Box sx={{ ml: `${indentLevel}px` }}>
      <Card 
        elevation={level === 0 ? 2 : 1} 
        sx={{ 
          mb: 1,
          border: level === 0 ? '2px solid' : '1px solid',
          borderColor: level === 0 ? 'primary.main' : 'divider',
        }}
      >
        <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flex: 1 }}>
              <Avatar sx={{ bgcolor: level === 0 ? 'primary.main' : 'secondary.main' }}>
                <Business />
              </Avatar>
              
              <Box sx={{ flex: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Typography variant={level === 0 ? 'h6' : 'subtitle1'} fontWeight="bold">
                    {company.name}
                  </Typography>
                  {level > 0 && (
                    <Chip 
                      label="Subsidiary" 
                      size="small" 
                      color="secondary" 
                      variant="outlined" 
                    />
                  )}
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                  <Chip label={company.industry} size="small" />
                  <Chip 
                    label={`${formatEmployees(company.employees)} employees`} 
                    size="small" 
                    variant="outlined" 
                  />
                  <Chip 
                    label={`${formatRevenue(company.revenue)} revenue`} 
                    size="small" 
                    variant="outlined" 
                  />
                </Box>
              </Box>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {onEdit && (
                <IconButton size="small" onClick={() => onEdit(company)}>
                  <Edit />
                </IconButton>
              )}
              
              {onAddSubsidiary && (
                <Button
                  size="small"
                  startIcon={<Add />}
                  onClick={() => onAddSubsidiary(company.id)}
                  variant="outlined"
                >
                  Add Subsidiary
                </Button>
              )}
              
              {hasSubsidiaries && (
                <IconButton onClick={() => setExpanded(!expanded)}>
                  {expanded ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              )}
            </Box>
          </Box>
          
          {hasSubsidiaries && (
            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <AccountTree fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {company.subsidiaries!.length} subsidiaries
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
      
      {hasSubsidiaries && (
        <Collapse in={expanded}>
          <Box sx={{ ml: 2, borderLeft: '2px dashed', borderColor: 'divider', pl: 2 }}>
            {company.subsidiaries!.map((subsidiary) => (
              <CompanyNode
                key={subsidiary.id}
                company={subsidiary}
                level={level + 1}
                {...(onEdit && { onEdit })}
                {...(onAddSubsidiary && { onAddSubsidiary })}
              />
            ))}
          </Box>
        </Collapse>
      )}
    </Box>
  );
};

export const CompanyHierarchy: React.FC<CompanyHierarchyProps> = ({
  companies = [],
  onEditCompany,
  onAddSubsidiary,
  loading = false,
}) => {
  // Build hierarchy from flat list
  const buildHierarchy = (companies: Company[]): Company[] => {
    const companyMap = new Map<string, Company>();
    const rootCompanies: Company[] = [];
    
    // First pass: create map and initialize subsidiaries
    companies.forEach(company => {
      companyMap.set(company.id, { ...company, subsidiaries: [] });
    });
    
    // Second pass: build hierarchy
    companies.forEach(company => {
      const companyNode = companyMap.get(company.id)!;
      
      if (company.parentId) {
        const parent = companyMap.get(company.parentId);
        if (parent) {
          parent.subsidiaries!.push(companyNode);
        } else {
          // Parent not found, treat as root
          rootCompanies.push(companyNode);
        }
      } else {
        rootCompanies.push(companyNode);
      }
    });
    
    return rootCompanies;
  };

  const hierarchicalCompanies = buildHierarchy(companies);

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Loading company hierarchy...</Typography>
      </Box>
    );
  }

  if (hierarchicalCompanies.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="text.secondary">
          No companies found. Add some companies to see the hierarchy.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AccountTree />
          Company Hierarchy
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {companies.length} companies total
        </Typography>
      </Box>
      
      {hierarchicalCompanies.map((company) => (
        <CompanyNode
          key={company.id}
          company={company}
          level={0}
          {...(onEditCompany && { onEdit: onEditCompany })}
          {...(onAddSubsidiary && { onAddSubsidiary })}
        />
      ))}
    </Box>
  );
};
