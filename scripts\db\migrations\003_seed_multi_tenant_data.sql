-- OneCRM Multi-Tenant Seed Data
-- Migration 003: Create seed data for testing multi-tenancy
-- This creates multiple organizations with isolated data

-- Clear existing data (for development only)
-- TRUNCATE TABLE activities, deals, companies, contacts, organization_invitations, users, organizations RESTART IDENTITY CASCADE;

-- Insert test organizations
INSERT INTO organizations (id, name, slug, plan, max_users, max_storage_gb, features, billing_email) VALUES
    ('550e8400-e29b-41d4-a716-**********01', 'Acme Corporation', 'acme-corp', 'enterprise', 100, 100, '{"advanced_reporting": true, "api_access": true, "custom_fields": true}', '<EMAIL>'),
    ('550e8400-e29b-41d4-a716-**********02', 'TechStart Inc', 'techstart', 'pro', 25, 25, '{"advanced_reporting": true, "api_access": false, "custom_fields": true}', '<EMAIL>'),
    ('550e8400-e29b-41d4-a716-**********03', 'Small Business LLC', 'small-biz', 'free', 5, 5, '{"advanced_reporting": false, "api_access": false, "custom_fields": false}', '<EMAIL>')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    plan = EXCLUDED.plan,
    max_users = EXCLUDED.max_users,
    max_storage_gb = EXCLUDED.max_storage_gb,
    features = EXCLUDED.features,
    billing_email = EXCLUDED.billing_email;

-- Insert test users for Acme Corporation
INSERT INTO users (id, keycloak_sub, email, first_name, last_name, org_id, role, timezone, locale) VALUES
    ('660e8400-e29b-41d4-a716-**********01', 'acme-admin-001', '<EMAIL>', 'John', 'Admin', '550e8400-e29b-41d4-a716-**********01', 'admin', 'America/New_York', 'en'),
    ('660e8400-e29b-41d4-a716-**********02', 'acme-user-001', '<EMAIL>', 'Jane', 'Sales', '550e8400-e29b-41d4-a716-**********01', 'user', 'America/New_York', 'en'),
    ('660e8400-e29b-41d4-a716-**********03', 'acme-user-002', '<EMAIL>', 'Bob', 'Support', '550e8400-e29b-41d4-a716-**********01', 'user', 'America/New_York', 'en')
ON CONFLICT (keycloak_sub) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    org_id = EXCLUDED.org_id,
    role = EXCLUDED.role;

-- Insert test users for TechStart Inc
INSERT INTO users (id, keycloak_sub, email, first_name, last_name, org_id, role, timezone, locale) VALUES
    ('660e8400-e29b-41d4-a716-**********04', 'techstart-admin-001', '<EMAIL>', 'Alice', 'Founder', '550e8400-e29b-41d4-a716-**********02', 'admin', 'America/Los_Angeles', 'en'),
    ('660e8400-e29b-41d4-a716-**********05', 'techstart-user-001', '<EMAIL>', 'Charlie', 'Developer', '550e8400-e29b-41d4-a716-**********02', 'user', 'America/Los_Angeles', 'en')
ON CONFLICT (keycloak_sub) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    org_id = EXCLUDED.org_id,
    role = EXCLUDED.role;

-- Insert test users for Small Business LLC
INSERT INTO users (id, keycloak_sub, email, first_name, last_name, org_id, role, timezone, locale) VALUES
    ('660e8400-e29b-41d4-a716-**********06', 'smallbiz-admin-001', '<EMAIL>', 'David', 'Owner', '550e8400-e29b-41d4-a716-**********03', 'admin', 'America/Chicago', 'en')
ON CONFLICT (keycloak_sub) DO UPDATE SET
    email = EXCLUDED.email,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    org_id = EXCLUDED.org_id,
    role = EXCLUDED.role;

-- Insert test companies for Acme Corporation
INSERT INTO companies (id, org_id, name, domain, industry, size, website, created_by, assigned_to) VALUES
    ('770e8400-e29b-41d4-a716-**********01', '550e8400-e29b-41d4-a716-**********01', 'Microsoft Corporation', 'microsoft.com', 'Technology', 'Enterprise', 'https://microsoft.com', '660e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********02'),
    ('770e8400-e29b-41d4-a716-**********02', '550e8400-e29b-41d4-a716-**********01', 'Google LLC', 'google.com', 'Technology', 'Enterprise', 'https://google.com', '660e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********03')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    domain = EXCLUDED.domain,
    industry = EXCLUDED.industry,
    size = EXCLUDED.size,
    website = EXCLUDED.website;

-- Insert test companies for TechStart Inc
INSERT INTO companies (id, org_id, name, domain, industry, size, website, created_by, assigned_to) VALUES
    ('770e8400-e29b-41d4-a716-**********03', '550e8400-e29b-41d4-a716-**********02', 'Startup Ventures', 'startupventures.com', 'Venture Capital', 'Small', 'https://startupventures.com', '660e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********05'),
    ('770e8400-e29b-41d4-a716-**********04', '550e8400-e29b-41d4-a716-**********02', 'Tech Incubator', 'techincubator.com', 'Technology', 'Medium', 'https://techincubator.com', '660e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********04')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    domain = EXCLUDED.domain,
    industry = EXCLUDED.industry,
    size = EXCLUDED.size,
    website = EXCLUDED.website;

-- Insert test companies for Small Business LLC
INSERT INTO companies (id, org_id, name, domain, industry, size, website, created_by, assigned_to) VALUES
    ('770e8400-e29b-41d4-a716-**********05', '550e8400-e29b-41d4-a716-**********03', 'Local Restaurant', 'localrestaurant.com', 'Food & Beverage', 'Small', 'https://localrestaurant.com', '660e8400-e29b-41d4-a716-**********06', '660e8400-e29b-41d4-a716-**********06')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    domain = EXCLUDED.domain,
    industry = EXCLUDED.industry,
    size = EXCLUDED.size,
    website = EXCLUDED.website;

-- Insert test contacts for Acme Corporation
INSERT INTO contacts (id, org_id, first_name, last_name, email, company, title, created_by, assigned_to) VALUES
    ('880e8400-e29b-41d4-a716-**********01', '550e8400-e29b-41d4-a716-**********01', 'Satya', 'Nadella', '<EMAIL>', 'Microsoft Corporation', 'CEO', '660e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********02'),
    ('880e8400-e29b-41d4-a716-**********02', '550e8400-e29b-41d4-a716-**********01', 'Sundar', 'Pichai', '<EMAIL>', 'Google LLC', 'CEO', '660e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********03'),
    ('880e8400-e29b-41d4-a716-**********03', '550e8400-e29b-41d4-a716-**********01', 'Amy', 'Hood', '<EMAIL>', 'Microsoft Corporation', 'CFO', '660e8400-e29b-41d4-a716-**********03', '660e8400-e29b-41d4-a716-**********02')
ON CONFLICT (id) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    email = EXCLUDED.email,
    company = EXCLUDED.company,
    title = EXCLUDED.title;

-- Insert test contacts for TechStart Inc
INSERT INTO contacts (id, org_id, first_name, last_name, email, company, title, created_by, assigned_to) VALUES
    ('880e8400-e29b-41d4-a716-**********04', '550e8400-e29b-41d4-a716-**********02', 'John', 'Investor', '<EMAIL>', 'Startup Ventures', 'Partner', '660e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********05'),
    ('880e8400-e29b-41d4-a716-**********05', '550e8400-e29b-41d4-a716-**********02', 'Sarah', 'Mentor', '<EMAIL>', 'Tech Incubator', 'Director', '660e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********04')
ON CONFLICT (id) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    email = EXCLUDED.email,
    company = EXCLUDED.company,
    title = EXCLUDED.title;

-- Insert test contacts for Small Business LLC
INSERT INTO contacts (id, org_id, first_name, last_name, email, company, title, created_by, assigned_to) VALUES
    ('880e8400-e29b-41d4-a716-**********06', '550e8400-e29b-41d4-a716-**********03', 'Maria', 'Customer', '<EMAIL>', 'Local Restaurant', 'Manager', '660e8400-e29b-41d4-a716-**********06', '660e8400-e29b-41d4-a716-**********06')
ON CONFLICT (id) DO UPDATE SET
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    email = EXCLUDED.email,
    company = EXCLUDED.company,
    title = EXCLUDED.title;

-- Insert test deals for Acme Corporation
INSERT INTO deals (id, org_id, title, amount, stage, probability, expected_close_date, contact_id, company_id, owner_id, created_by) VALUES
    ('990e8400-e29b-41d4-a716-**********01', '550e8400-e29b-41d4-a716-**********01', 'Microsoft Enterprise License', 500000.00, 'negotiation', 75, '2024-03-31', '880e8400-e29b-41d4-a716-**********01', '770e8400-e29b-41d4-a716-**********01', '660e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********02'),
    ('990e8400-e29b-41d4-a716-**********02', '550e8400-e29b-41d4-a716-**********01', 'Google Cloud Migration', 250000.00, 'proposal', 50, '2024-04-15', '880e8400-e29b-41d4-a716-**********02', '770e8400-e29b-41d4-a716-**********02', '660e8400-e29b-41d4-a716-**********03', '660e8400-e29b-41d4-a716-**********03')
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    amount = EXCLUDED.amount,
    stage = EXCLUDED.stage,
    probability = EXCLUDED.probability,
    expected_close_date = EXCLUDED.expected_close_date;

-- Insert test deals for TechStart Inc
INSERT INTO deals (id, org_id, title, amount, stage, probability, expected_close_date, contact_id, company_id, owner_id, created_by) VALUES
    ('990e8400-e29b-41d4-a716-**********03', '550e8400-e29b-41d4-a716-**********02', 'Series A Funding', 2000000.00, 'qualification', 30, '2024-06-30', '880e8400-e29b-41d4-a716-**********04', '770e8400-e29b-41d4-a716-**********03', '660e8400-e29b-41d4-a716-**********04', '660e8400-e29b-41d4-a716-**********04'),
    ('990e8400-e29b-41d4-a716-**********04', '550e8400-e29b-41d4-a716-**********02', 'Incubator Program', 50000.00, 'closed-won', 100, '2024-02-01', '880e8400-e29b-41d4-a716-**********05', '770e8400-e29b-41d4-a716-**********04', '660e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********05')
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    amount = EXCLUDED.amount,
    stage = EXCLUDED.stage,
    probability = EXCLUDED.probability,
    expected_close_date = EXCLUDED.expected_close_date;

-- Insert test deals for Small Business LLC
INSERT INTO deals (id, org_id, title, amount, stage, probability, expected_close_date, contact_id, company_id, owner_id, created_by) VALUES
    ('990e8400-e29b-41d4-a716-**********05', '550e8400-e29b-41d4-a716-**********03', 'Catering Contract', 15000.00, 'proposal', 60, '2024-03-15', '880e8400-e29b-41d4-a716-**********06', '770e8400-e29b-41d4-a716-**********05', '660e8400-e29b-41d4-a716-**********06', '660e8400-e29b-41d4-a716-**********06')
ON CONFLICT (id) DO UPDATE SET
    title = EXCLUDED.title,
    amount = EXCLUDED.amount,
    stage = EXCLUDED.stage,
    probability = EXCLUDED.probability,
    expected_close_date = EXCLUDED.expected_close_date;

-- Insert test activities
INSERT INTO activities (id, org_id, type, subject, description, contact_id, user_id) VALUES
    ('aa0e8400-e29b-41d4-a716-**********01', '550e8400-e29b-41d4-a716-**********01', 'email', 'Follow up on Microsoft deal', 'Sent follow-up email regarding enterprise license pricing', '880e8400-e29b-41d4-a716-**********01', '660e8400-e29b-41d4-a716-**********02'),
    ('aa0e8400-e29b-41d4-a716-**********02', '550e8400-e29b-41d4-a716-**********02', 'call', 'Investor call', 'Discussed Series A funding timeline and requirements', '880e8400-e29b-41d4-a716-**********04', '660e8400-e29b-41d4-a716-**********04'),
    ('aa0e8400-e29b-41d4-a716-**********03', '550e8400-e29b-41d4-a716-**********03', 'meeting', 'Catering proposal meeting', 'Presented catering proposal for corporate events', '880e8400-e29b-41d4-a716-**********06', '660e8400-e29b-41d4-a716-**********06')
ON CONFLICT (id) DO UPDATE SET
    subject = EXCLUDED.subject,
    description = EXCLUDED.description;

-- Insert test organization invitations
INSERT INTO organization_invitations (id, org_id, email, role, invited_by, status) VALUES
    ('bb0e8400-e29b-41d4-a716-**********01', '550e8400-e29b-41d4-a716-**********01', '<EMAIL>', 'user', '660e8400-e29b-41d4-a716-**********01', 'pending'),
    ('bb0e8400-e29b-41d4-a716-**********02', '550e8400-e29b-41d4-a716-**********02', '<EMAIL>', 'user', '660e8400-e29b-41d4-a716-**********04', 'pending')
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    status = EXCLUDED.status;

-- Insert test usage data
INSERT INTO organization_usage (org_id, metric, value, period_start, period_end) VALUES
    ('550e8400-e29b-41d4-a716-**********01', 'users', 3, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********01', 'contacts', 3, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********01', 'api_calls', 15420, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********02', 'users', 2, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********02', 'contacts', 2, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********02', 'api_calls', 8750, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********03', 'users', 1, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********03', 'contacts', 1, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00'),
    ('550e8400-e29b-41d4-a716-**********03', 'api_calls', 1250, '2024-01-01 00:00:00+00', '2024-01-31 23:59:59+00')
ON CONFLICT (org_id, metric, period_start) DO UPDATE SET
    value = EXCLUDED.value;
