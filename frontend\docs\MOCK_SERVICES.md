# Mock Services Documentation

## Overview

OneCRM frontend includes a comprehensive mock services system that allows development without external dependencies like Keycloak, external APIs, or complex authentication flows.

## Features

### 🎭 Mock Authentication
- **Auto-login**: Automatically logs in with a mock user
- **Role-based access**: Supports multiple user roles
- **JWT tokens**: Generates mock JWT tokens for API calls
- **Session management**: Handles login/logout flows

### 🔌 Mock API
- **Data generation**: Generates realistic mock data for CRM entities
- **Network simulation**: Simulates network delays and errors
- **CRUD operations**: Supports create, read, update, delete operations
- **Error handling**: Configurable error rates for testing

### 📊 Mock Data
- **Contacts**: Realistic contact data with names, emails, companies
- **Companies**: Company data with industries, revenue, employees
- **Deals**: Sales pipeline data with stages and values
- **Analytics**: Dashboard statistics and metrics

## Configuration

### Environment Variables

Create a `.env.local` file in the frontend directory:

```bash
# Mock Services Configuration
NEXT_PUBLIC_MOCK_AUTH=true          # Enable/disable mock authentication
NEXT_PUBLIC_MOCK_API=false          # Enable/disable mock API
NEXT_PUBLIC_MOCK_DELAY=300          # Network delay simulation (ms)

# Keycloak Configuration (when mock auth is disabled)
NEXT_PUBLIC_KEYCLOAK_ENABLED=false  # Enable/disable Keycloak
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
```

### Development vs Production

#### Development Mode (Default)
```bash
NEXT_PUBLIC_MOCK_AUTH=true
NEXT_PUBLIC_MOCK_API=false
NEXT_PUBLIC_KEYCLOAK_ENABLED=false
```

#### Production Mode
```bash
NEXT_PUBLIC_MOCK_AUTH=false
NEXT_PUBLIC_MOCK_API=false
NEXT_PUBLIC_KEYCLOAK_ENABLED=true
```

## Usage

### Mock Authentication

```typescript
import { useAuth } from '@/components/providers/MockAuthProvider';

function MyComponent() {
  const { user, isAuthenticated, login, logout } = useAuth();
  
  if (!isAuthenticated) {
    return <button onClick={() => login()}>Login</button>;
  }
  
  return (
    <div>
      Welcome, {user?.name}!
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### Mock API

```typescript
import { api, mockApi } from '@/lib/mockApi';

// Use the unified API (automatically chooses mock or real)
const contacts = await api.get('/contacts');

// Use mock API directly
const mockContacts = await mockApi.getContacts();
```

### Mock Data Access

```typescript
import { mockApi } from '@/lib/mockApi';

// Get mock contacts
const contacts = await mockApi.getContacts();

// Get mock companies
const companies = await mockApi.getCompanies();

// Get mock deals
const deals = await mockApi.getDeals();

// Get mock statistics
const stats = await mockApi.getStats();
```

## Mock User Configuration

The default mock user can be customized in `src/config/mock.config.ts`:

```typescript
mockUser: {
  id: 'mock-user-123',
  email: '<EMAIL>',
  name: 'Admin User',
  roles: ['admin', 'user'],
  organizationId: 'mock-org-456',
}
```

## Visual Indicators

When mock services are enabled, visual indicators appear in the top corners:

- **Top Right**: Mock Auth status
- **Top Left**: Mock API status (when enabled)

## API Endpoints

### Mock Authentication Endpoints
- `login()` - Mock login with any credentials
- `logout()` - Clear mock session
- `getToken()` - Get mock JWT token
- `refreshToken()` - Refresh mock token

### Mock API Endpoints
- `GET /contacts` - List all contacts
- `GET /contacts/:id` - Get specific contact
- `POST /contacts` - Create new contact
- `GET /companies` - List all companies
- `GET /deals` - List all deals
- `GET /stats` - Get dashboard statistics
- `GET /health` - Health check

## Error Simulation

Configure error rates in `mock.config.ts`:

```typescript
api: {
  enabled: true,
  delay: 500,        // Network delay in ms
  errorRate: 10,     // 10% of requests will fail
}
```

## Development Workflow

### 1. Start with Mock Services
```bash
# Enable all mock services
NEXT_PUBLIC_MOCK_AUTH=true
NEXT_PUBLIC_MOCK_API=true
```

### 2. Test with Real Backend
```bash
# Use real API, keep mock auth
NEXT_PUBLIC_MOCK_AUTH=true
NEXT_PUBLIC_MOCK_API=false
```

### 3. Full Integration
```bash
# Use all real services
NEXT_PUBLIC_MOCK_AUTH=false
NEXT_PUBLIC_MOCK_API=false
NEXT_PUBLIC_KEYCLOAK_ENABLED=true
```

## Troubleshooting

### Mock Services Not Working
1. Check environment variables in `.env.local`
2. Verify mock configuration in browser console
3. Look for mock status indicators on the page

### Authentication Issues
1. Clear localStorage: `localStorage.clear()`
2. Check mock user configuration
3. Verify `NEXT_PUBLIC_MOCK_AUTH=true`

### API Issues
1. Check network tab for mock API calls
2. Verify backend is running on port 3002
3. Check `NEXT_PUBLIC_MOCK_API` setting

## Best Practices

1. **Use mock services for initial development**
2. **Test with real services before production**
3. **Keep mock data realistic and up-to-date**
4. **Use environment variables for configuration**
5. **Document any custom mock configurations**

## Files Structure

```
src/
├── config/
│   └── mock.config.ts          # Mock services configuration
├── components/
│   ├── providers/
│   │   ├── MockAuthProvider.tsx # Mock authentication provider
│   │   └── ClientProviders.tsx  # Main providers wrapper
│   └── common/
│       └── MockStatusIndicators.tsx # Visual status indicators
├── lib/
│   └── mockApi.ts              # Mock API service
└── docs/
    └── MOCK_SERVICES.md        # This documentation
```

This mock system provides a complete development environment without external dependencies while maintaining compatibility with production services.
