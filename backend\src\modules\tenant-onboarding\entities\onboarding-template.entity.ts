import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { OnboardingStep } from './onboarding-step.entity';

export enum OnboardingTemplateType {
  STARTUP = 'startup',
  SMB = 'smb',
  ENTERPRISE = 'enterprise',
  CUSTOM = 'custom',
}

@Entity('onboarding_templates')
export class OnboardingTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: OnboardingTemplateType,
    default: OnboardingTemplateType.SMB,
  })
  type: OnboardingTemplateType;

  @Column({ type: 'boolean', default: true })
  active: boolean;

  @Column({ type: 'boolean', default: false })
  default: boolean;

  @Column({ type: 'json', nullable: true })
  target_criteria: {
    company_size?: string;
    industry?: string[];
    use_case?: string[];
    technical_expertise?: string;
  };

  @Column({ type: 'int', nullable: true })
  estimated_completion_days: number;

  @Column({ type: 'json', nullable: true })
  success_metrics: {
    completion_rate_target?: number;
    time_to_value_days?: number;
    user_adoption_rate?: number;
  };

  @OneToMany(() => OnboardingStep, step => step.template, {
    cascade: true,
    eager: true,
  })
  steps: OnboardingStep[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
