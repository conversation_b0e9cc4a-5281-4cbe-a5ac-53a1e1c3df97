import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { SubscriptionController } from './subscription.controller';
import { SubscriptionService } from './subscription.service';
import { StripeService } from './stripe.service';
import { BillingService } from './billing.service';
import { SubscriptionPlan } from './entities/subscription-plan.entity';
import { Subscription } from './entities/subscription.entity';
import { Invoice } from './entities/invoice.entity';
import { PaymentMethod } from './entities/payment-method.entity';
import { UsageRecord } from './entities/usage-record.entity';
import { BillingAddress } from './entities/billing-address.entity';
import { OrganizationModule } from '../organization/organization.module';
import { EmailModule } from '../email/email.module';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      SubscriptionPlan,
      Subscription,
      Invoice,
      PaymentMethod,
      UsageRecord,
      BillingAddress,
    ]),
    OrganizationModule,
    EmailModule,
    NotificationModule,
  ],
  controllers: [SubscriptionController],
  providers: [SubscriptionService, StripeService, BillingService],
  exports: [SubscriptionService, StripeService, BillingService],
})
export class SubscriptionModule {}
