import { IsOptional, IsString, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>A<PERSON>y, IsEnum, IsUUID, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export enum DealSortField {
  TITLE = 'title',
  AMOUNT = 'amount',
  STAGE = 'stage',
  PROBABILITY = 'probability',
  EXPECTED_CLOSE_DATE = 'expectedCloseDate',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum DealStage {
  LEAD = 'lead',
  QUALIFICATION = 'qualification',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed-won',
  CLOSED_LOST = 'closed-lost',
}

export class SearchDealsDto {
  @ApiProperty({ description: 'Search query for title or description', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ description: 'Filter by deal stage', enum: DealStage, required: false })
  @IsOptional()
  @IsEnum(DealStage)
  stage?: DealStage;

  @ApiProperty({ description: 'Filter by deal owner ID', required: false })
  @IsOptional()
  @IsUUID()
  ownerId?: string;

  @ApiProperty({ description: 'Filter by contact ID', required: false })
  @IsOptional()
  @IsUUID()
  contactId?: string;

  @ApiProperty({ description: 'Filter by company ID', required: false })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ description: 'Filter by deal source', required: false })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({ description: 'Filter by deal type', required: false })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({ description: 'Filter by deal priority', required: false })
  @IsOptional()
  @IsString()
  priority?: string;

  @ApiProperty({ description: 'Minimum deal amount', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  minAmount?: number;

  @ApiProperty({ description: 'Maximum deal amount', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  maxAmount?: number;

  @ApiProperty({ description: 'Minimum probability', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(100)
  minProbability?: number;

  @ApiProperty({ description: 'Maximum probability', required: false })
  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(100)
  maxProbability?: number;

  @ApiProperty({ description: 'Expected close date from', required: false })
  @IsOptional()
  @IsDateString()
  expectedCloseDateFrom?: string;

  @ApiProperty({ description: 'Expected close date to', required: false })
  @IsOptional()
  @IsDateString()
  expectedCloseDateTo?: string;

  @ApiProperty({ description: 'Page number', minimum: 1, default: 1, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: 'Number of items per page', minimum: 1, maximum: 100, default: 20, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiProperty({ description: 'Sort field', enum: DealSortField, default: DealSortField.CREATED_AT, required: false })
  @IsOptional()
  @IsEnum(DealSortField)
  sortBy?: DealSortField = DealSortField.CREATED_AT;

  @ApiProperty({ description: 'Sort order', enum: SortOrder, default: SortOrder.DESC, required: false })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder = SortOrder.DESC;

  @ApiProperty({ description: 'Include soft-deleted deals', default: false, required: false })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includeDeleted?: boolean = false;
}

export class DealsResponseDto {
  @ApiProperty({ description: 'List of deals' })
  deals: any[];

  @ApiProperty({ description: 'Total number of deals' })
  total: number;

  @ApiProperty({ description: 'Current page number' })
  page: number;

  @ApiProperty({ description: 'Number of items per page' })
  limit: number;

  @ApiProperty({ description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ description: 'Whether there are more pages' })
  hasNext: boolean;

  @ApiProperty({ description: 'Whether there are previous pages' })
  hasPrev: boolean;
}

export class DealsPipelineDto {
  @ApiProperty({ description: 'Pipeline statistics by stage' })
  pipeline: {
    stage: string;
    count: number;
    totalValue: number;
    averageValue: number;
    deals: any[];
  }[];

  @ApiProperty({ description: 'Total pipeline value' })
  totalValue: number;

  @ApiProperty({ description: 'Weighted pipeline value (by probability)' })
  weightedValue: number;

  @ApiProperty({ description: 'Total number of deals' })
  totalDeals: number;

  @ApiProperty({ description: 'Average deal size' })
  averageDealSize: number;
}

export class DealsForecastDto {
  @ApiProperty({ description: 'Forecast for current month' })
  currentMonth: {
    expected: number;
    committed: number;
    bestCase: number;
    deals: number;
  };

  @ApiProperty({ description: 'Forecast for current quarter' })
  currentQuarter: {
    expected: number;
    committed: number;
    bestCase: number;
    deals: number;
  };

  @ApiProperty({ description: 'Monthly forecast breakdown' })
  monthlyForecast: {
    month: string;
    expected: number;
    committed: number;
    bestCase: number;
    deals: number;
  }[];
}
