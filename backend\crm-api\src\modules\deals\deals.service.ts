import { Injectable, NotFoundException, ForbiddenException, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';
import { Deal } from './deal.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';
import { CreateDealDto } from './dto/create-deal.dto';
import { UpdateDealDto } from './dto/update-deal.dto';
import { SearchDealsDto, DealsResponseDto, DealsPipelineDto, DealsForecastDto } from './dto/search-deals.dto';

@Injectable()
export class DealsService {
  private readonly logger = new Logger(DealsService.name);

  constructor(
    @InjectRepository(Deal)
    private dealsRepository: Repository<Deal>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Contact)
    private contactsRepository: Repository<Contact>,
    @InjectRepository(Company)
    private companiesRepository: Repository<Company>,
    private tenantContextService: TenantContextService,
  ) {}

  async create(createDealDto: CreateDealDto): Promise<Deal> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Validate owner belongs to the same organization
    if (createDealDto.ownerId) {
      const owner = await this.usersRepository.findOne({
        where: { id: createDealDto.ownerId, orgId },
      });
      if (!owner) {
        throw new BadRequestException('Deal owner not found in your organization');
      }
    }

    // Validate contact belongs to the same organization
    if (createDealDto.contactId) {
      const contact = await this.contactsRepository.findOne({
        where: { id: createDealDto.contactId, orgId },
      });
      if (!contact) {
        throw new BadRequestException('Contact not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (createDealDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: createDealDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Set default probability based on stage
    const probability = createDealDto.probability ?? this.getDefaultProbabilityForStage(createDealDto.stage);

    const deal = this.dealsRepository.create({
      ...createDealDto,
      probability,
      orgId,
      createdById: currentUserId,
      updatedById: currentUserId,
      ownerId: createDealDto.ownerId || currentUserId,
    });

    const savedDeal = await this.dealsRepository.save(deal);
    
    this.logger.log(`Deal created: ${savedDeal.id} by user ${currentUserId}`);
    
    return this.findById(savedDeal.id);
  }

  async findAll(searchDto: SearchDealsDto): Promise<DealsResponseDto> {
    const orgId = this.tenantContextService.getOrgId();
    const { page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'DESC' } = searchDto;

    const queryBuilder = this.dealsRepository
      .createQueryBuilder('deal')
      .leftJoinAndSelect('deal.owner', 'owner')
      .leftJoinAndSelect('deal.createdBy', 'createdBy')
      .leftJoinAndSelect('deal.contact', 'contact')
      .leftJoinAndSelect('deal.company', 'company')
      .where('deal.orgId = :orgId', { orgId });

    // Apply search filters
    this.applySearchFilters(queryBuilder, searchDto);

    // Apply sorting
    queryBuilder.orderBy(`deal.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [deals, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      deals,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  async findById(id: string): Promise<Deal> {
    const orgId = this.tenantContextService.getOrgId();
    
    const deal = await this.dealsRepository.findOne({
      where: { id, orgId },
      relations: ['owner', 'createdBy', 'updatedBy', 'contact', 'company'],
    });

    if (!deal) {
      throw new NotFoundException(`Deal with ID ${id} not found`);
    }

    return deal;
  }

  async update(id: string, updateDealDto: UpdateDealDto): Promise<Deal> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if deal exists and belongs to the organization
    const existingDeal = await this.findById(id);

    // Check permissions - users can update deals they own or created, admins can update all
    const canUpdate = this.tenantContextService.isAdmin() || 
                     existingDeal.ownerId === currentUserId ||
                     existingDeal.createdById === currentUserId;

    if (!canUpdate) {
      throw new ForbiddenException('You do not have permission to update this deal');
    }

    // Validate owner belongs to the same organization
    if (updateDealDto.ownerId) {
      const owner = await this.usersRepository.findOne({
        where: { id: updateDealDto.ownerId, orgId },
      });
      if (!owner) {
        throw new BadRequestException('Deal owner not found in your organization');
      }
    }

    // Validate contact belongs to the same organization
    if (updateDealDto.contactId) {
      const contact = await this.contactsRepository.findOne({
        where: { id: updateDealDto.contactId, orgId },
      });
      if (!contact) {
        throw new BadRequestException('Contact not found in your organization');
      }
    }

    // Validate company belongs to the same organization
    if (updateDealDto.companyId) {
      const company = await this.companiesRepository.findOne({
        where: { id: updateDealDto.companyId, orgId },
      });
      if (!company) {
        throw new BadRequestException('Company not found in your organization');
      }
    }

    // Update probability if stage changed and probability not explicitly set
    if (updateDealDto.stage && updateDealDto.probability === undefined) {
      updateDealDto.probability = this.getDefaultProbabilityForStage(updateDealDto.stage);
    }

    await this.dealsRepository.update(
      { id, orgId },
      { ...updateDealDto, updatedById: currentUserId }
    );

    this.logger.log(`Deal updated: ${id} by user ${currentUserId}`);

    return this.findById(id);
  }

  async remove(id: string): Promise<void> {
    const orgId = this.tenantContextService.getOrgId();
    const currentUserId = this.tenantContextService.getUserId();

    // Check if deal exists and belongs to the organization
    const existingDeal = await this.findById(id);

    // Check permissions - users can delete deals they own or created, admins can delete all
    const canDelete = this.tenantContextService.isAdmin() || 
                     existingDeal.ownerId === currentUserId ||
                     existingDeal.createdById === currentUserId;

    if (!canDelete) {
      throw new ForbiddenException('You do not have permission to delete this deal');
    }

    // Soft delete
    await this.dealsRepository.softDelete({ id, orgId });

    this.logger.log(`Deal deleted: ${id} by user ${currentUserId}`);
  }

  async getPipeline(): Promise<DealsPipelineDto> {
    const orgId = this.tenantContextService.getOrgId();

    const stages = ['lead', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost'];
    const pipeline = [];
    let totalValue = 0;
    let weightedValue = 0;
    let totalDeals = 0;

    for (const stage of stages) {
      const deals = await this.dealsRepository.find({
        where: { orgId, stage },
        relations: ['owner', 'contact', 'company'],
      });

      const stageValue = deals.reduce((sum, deal) => sum + parseFloat(deal.amount.toString()), 0);
      const stageWeightedValue = deals.reduce((sum, deal) => {
        const probability = deal.probability || 0;
        return sum + (parseFloat(deal.amount.toString()) * probability / 100);
      }, 0);

      pipeline.push({
        stage,
        count: deals.length,
        totalValue: stageValue,
        averageValue: deals.length > 0 ? stageValue / deals.length : 0,
        deals: deals.slice(0, 10), // Limit to top 10 deals per stage
      });

      if (stage !== 'closed-lost') {
        totalValue += stageValue;
        weightedValue += stageWeightedValue;
      }
      
      totalDeals += deals.length;
    }

    return {
      pipeline,
      totalValue,
      weightedValue,
      totalDeals,
      averageDealSize: totalDeals > 0 ? totalValue / totalDeals : 0,
    };
  }

  async getForecast(): Promise<DealsForecastDto> {
    const orgId = this.tenantContextService.getOrgId();
    const now = new Date();

    // Current month forecast
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const currentMonthDeals = await this.dealsRepository.find({
      where: {
        orgId,
        expectedCloseDate: Between(currentMonthStart, currentMonthEnd),
        stage: { $in: ['qualification', 'proposal', 'negotiation'] } as any,
      },
    });

    const currentMonth = this.calculateForecastMetrics(currentMonthDeals);

    // Current quarter forecast
    const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
    const quarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);

    const currentQuarterDeals = await this.dealsRepository.find({
      where: {
        orgId,
        expectedCloseDate: Between(quarterStart, quarterEnd),
        stage: { $in: ['qualification', 'proposal', 'negotiation'] } as any,
      },
    });

    const currentQuarter = this.calculateForecastMetrics(currentQuarterDeals);

    // Monthly forecast for next 6 months
    const monthlyForecast = [];
    for (let i = 0; i < 6; i++) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() + i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + i + 1, 0);

      const monthDeals = await this.dealsRepository.find({
        where: {
          orgId,
          expectedCloseDate: Between(monthStart, monthEnd),
          stage: { $in: ['qualification', 'proposal', 'negotiation'] } as any,
        },
      });

      const monthMetrics = this.calculateForecastMetrics(monthDeals);
      
      monthlyForecast.push({
        month: monthStart.toISOString().substring(0, 7), // YYYY-MM format
        ...monthMetrics,
      });
    }

    return {
      currentMonth,
      currentQuarter,
      monthlyForecast,
    };
  }

  private calculateForecastMetrics(deals: Deal[]): {
    expected: number;
    committed: number;
    bestCase: number;
    deals: number;
  } {
    const expected = deals.reduce((sum, deal) => {
      const probability = deal.probability || 0;
      return sum + (parseFloat(deal.amount.toString()) * probability / 100);
    }, 0);

    const committed = deals
      .filter(deal => (deal.probability || 0) >= 75)
      .reduce((sum, deal) => sum + parseFloat(deal.amount.toString()), 0);

    const bestCase = deals.reduce((sum, deal) => sum + parseFloat(deal.amount.toString()), 0);

    return {
      expected,
      committed,
      bestCase,
      deals: deals.length,
    };
  }

  private getDefaultProbabilityForStage(stage: string): number {
    const stageProbabilities: Record<string, number> = {
      'lead': 10,
      'qualification': 25,
      'proposal': 50,
      'negotiation': 75,
      'closed-won': 100,
      'closed-lost': 0,
    };

    return stageProbabilities[stage] || 25;
  }

  private applySearchFilters(
    queryBuilder: SelectQueryBuilder<Deal>,
    searchDto: SearchDealsDto
  ): void {
    const {
      search,
      stage,
      ownerId,
      contactId,
      companyId,
      source,
      type,
      priority,
      minAmount,
      maxAmount,
      minProbability,
      maxProbability,
      expectedCloseDateFrom,
      expectedCloseDateTo,
      includeDeleted,
    } = searchDto;

    // Include deleted deals if requested
    if (!includeDeleted) {
      queryBuilder.andWhere('deal.deletedAt IS NULL');
    }

    // General search across title and description
    if (search) {
      queryBuilder.andWhere(
        '(deal.title ILIKE :search OR deal.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Filter by stage
    if (stage) {
      queryBuilder.andWhere('deal.stage = :stage', { stage });
    }

    // Filter by owner
    if (ownerId) {
      queryBuilder.andWhere('deal.ownerId = :ownerId', { ownerId });
    }

    // Filter by contact
    if (contactId) {
      queryBuilder.andWhere('deal.contactId = :contactId', { contactId });
    }

    // Filter by company
    if (companyId) {
      queryBuilder.andWhere('deal.companyId = :companyId', { companyId });
    }

    // Filter by source
    if (source) {
      queryBuilder.andWhere('deal.source = :source', { source });
    }

    // Filter by type
    if (type) {
      queryBuilder.andWhere('deal.type = :type', { type });
    }

    // Filter by priority
    if (priority) {
      queryBuilder.andWhere('deal.priority = :priority', { priority });
    }

    // Filter by amount range
    if (minAmount !== undefined || maxAmount !== undefined) {
      if (minAmount !== undefined && maxAmount !== undefined) {
        queryBuilder.andWhere('deal.amount BETWEEN :minAmount AND :maxAmount', {
          minAmount,
          maxAmount,
        });
      } else if (minAmount !== undefined) {
        queryBuilder.andWhere('deal.amount >= :minAmount', { minAmount });
      } else if (maxAmount !== undefined) {
        queryBuilder.andWhere('deal.amount <= :maxAmount', { maxAmount });
      }
    }

    // Filter by probability range
    if (minProbability !== undefined || maxProbability !== undefined) {
      if (minProbability !== undefined && maxProbability !== undefined) {
        queryBuilder.andWhere('deal.probability BETWEEN :minProbability AND :maxProbability', {
          minProbability,
          maxProbability,
        });
      } else if (minProbability !== undefined) {
        queryBuilder.andWhere('deal.probability >= :minProbability', { minProbability });
      } else if (maxProbability !== undefined) {
        queryBuilder.andWhere('deal.probability <= :maxProbability', { maxProbability });
      }
    }

    // Filter by expected close date range
    if (expectedCloseDateFrom || expectedCloseDateTo) {
      if (expectedCloseDateFrom && expectedCloseDateTo) {
        queryBuilder.andWhere('deal.expectedCloseDate BETWEEN :dateFrom AND :dateTo', {
          dateFrom: expectedCloseDateFrom,
          dateTo: expectedCloseDateTo,
        });
      } else if (expectedCloseDateFrom) {
        queryBuilder.andWhere('deal.expectedCloseDate >= :dateFrom', { dateFrom: expectedCloseDateFrom });
      } else if (expectedCloseDateTo) {
        queryBuilder.andWhere('deal.expectedCloseDate <= :dateTo', { dateTo: expectedCloseDateTo });
      }
    }
  }

  /**
   * Test method to get deals data directly without tenant context
   */
  async getTestData() {
    try {
      const deals = await this.dealsRepository
        .createQueryBuilder('deal')
        .leftJoinAndSelect('deal.contact', 'contact')
        .leftJoinAndSelect('deal.company', 'company')
        .leftJoinAndSelect('deal.owner', 'owner')
        .leftJoinAndSelect('deal.createdBy', 'createdBy')
        .take(10)
        .getMany();

      return {
        status: 'success',
        message: 'Test data retrieved successfully',
        count: deals.length,
        data: deals,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error('Error retrieving test data', error.stack);
      return {
        status: 'error',
        message: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
