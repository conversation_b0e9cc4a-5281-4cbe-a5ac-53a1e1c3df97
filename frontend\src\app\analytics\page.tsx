'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Tabs,
  Tab,
  Button,
  IconButton,
  Menu,
  MenuItem as MenuItemComponent,
} from '@mui/material';
import {
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Pie<PERSON><PERSON> as PieChartIcon,
} from '@mui/icons-material';
import { AppLayout } from '../../components/layout/AppLayout';
import { AdvancedAnalytics } from '../../components/dashboard/AdvancedAnalytics';
import { InteractivePipeline } from '../../components/deals/InteractivePipeline';
import { mutate } from 'swr';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
    </div>
  );
}

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleTimeRangeChange = (event: any) => {
    setTimeRange(event.target.value);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRefresh = () => {
    handleMenuClose();
    // Refresh all analytics data
    mutate(key => typeof key === 'string' && key.startsWith('/api/analytics'));
    mutate('/api/deals/pipeline');
    mutate('/api/contacts/stats');
    mutate('/api/companies/stats');
    mutate('/api/activities/stats');
  };

  const handleExport = () => {
    handleMenuClose();
    console.log('Export analytics data');
  };

  const timeRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '6m', label: 'Last 6 months' },
    { value: '1y', label: 'Last year' },
    { value: 'ytd', label: 'Year to date' },
  ];

  return (
    <AppLayout>
      <Container maxWidth="xl">
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1" fontWeight={600}>
            Analytics & Reports
          </Typography>
          
          <Box display="flex" gap={2} alignItems="center">
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={handleTimeRangeChange}
              >
                {timeRangeOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <IconButton onClick={handleRefresh} title="Refresh Data">
              <RefreshIcon />
            </IconButton>

            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>

            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItemComponent onClick={handleExport}>
                <DownloadIcon sx={{ mr: 1 }} />
                Export Report
              </MenuItemComponent>
            </Menu>
          </Box>
        </Box>

        {/* Analytics Tabs */}
        <Card sx={{ mb: 3 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={handleTabChange}>
              <Tab 
                icon={<AssessmentIcon />} 
                label="Overview" 
                iconPosition="start"
              />
              <Tab 
                icon={<TrendingUpIcon />} 
                label="Sales Pipeline" 
                iconPosition="start"
              />
              <Tab 
                icon={<PieChartIcon />} 
                label="Performance" 
                iconPosition="start"
              />
            </Tabs>
          </Box>

          {/* Overview Tab */}
          <TabPanel value={activeTab} index={0}>
            <AdvancedAnalytics timeRange={timeRange} />
          </TabPanel>

          {/* Sales Pipeline Tab */}
          <TabPanel value={activeTab} index={1}>
            <Box p={3}>
              <Typography variant="h6" gutterBottom>
                Interactive Sales Pipeline
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={3}>
                Drag and drop deals between stages to update their status
              </Typography>
              <InteractivePipeline
                searchQuery=""
                filters={{}}
                onEditDeal={(deal) => console.log('Edit deal:', deal)}
              />
            </Box>
          </TabPanel>

          {/* Performance Tab */}
          <TabPanel value={activeTab} index={2}>
            <Box p={3}>
              <Grid container spacing={3}>
                {/* Team Performance */}
                <Grid item xs={12} lg={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Team Performance
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Individual and team performance metrics
                      </Typography>
                      {/* Add team performance charts here */}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Activity Analysis */}
                <Grid item xs={12} lg={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Activity Analysis
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Activity patterns and productivity insights
                      </Typography>
                      {/* Add activity analysis charts here */}
                    </CardContent>
                  </Card>
                </Grid>

                {/* Revenue Forecasting */}
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Revenue Forecasting
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Predictive revenue analysis based on current pipeline
                      </Typography>
                      {/* Add forecasting charts here */}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </TabPanel>
        </Card>

        {/* Quick Actions */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer', '&:hover': { boxShadow: 4 } }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <TrendingUpIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Sales Report
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Generate detailed sales performance report
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer', '&:hover': { boxShadow: 4 } }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <AssessmentIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Activity Report
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Analyze team activity and productivity
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer', '&:hover': { boxShadow: 4 } }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <PieChartIcon sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Pipeline Report
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Deep dive into sales pipeline health
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer', '&:hover': { boxShadow: 4 } }}>
              <CardContent sx={{ textAlign: 'center' }}>
                <DownloadIcon sx={{ fontSize: 48, color: 'info.main', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Custom Report
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Create and export custom reports
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </AppLayout>
  );
}
