# Keycloak Configuration for OneCRM

## Prerequisites
- Self-hosted Keycloak instance running
- Admin access to Keycloak Admin Console
- Existing realm (or create new one named `onecrm`)

## Keycloak Architecture for OneCRM

```mermaid
graph TB
    subgraph "Keycloak Server"
        REALM[OneCRM Realm]

        subgraph "Clients"
            FC[onecrm-frontend<br/>Public Client]
            BC[onecrm-backend<br/>Confidential Client]
        end

        subgraph "Roles"
            RR[Realm Roles<br/>super-admin, org-admin<br/>dept-manager, sales-rep<br/>support-agent, marketing-user, viewer]
            CR[Client Roles<br/>contacts:create, deals:approve<br/>reports:view, users:manage]
        end

        subgraph "Users & Groups"
            USERS[Users<br/>with Attributes]
            GROUPS[Groups<br/>by Department/Org]
        end

        subgraph "Mappers"
            RM[Role Mappers]
            AM[Attribute Mappers]
            GM[Group Mappers]
        end
    end

    subgraph "OneCRM Application"
        FE[Frontend<br/>Next.js]
        BE[Backend<br/>NestJS API]
        DB[(Database)]
    end

    REALM --> FC
    REALM --> BC
    REALM --> RR
    REALM --> CR
    REALM --> USERS
    REALM --> GROUPS

    RR --> RM
    USERS --> AM
    GROUPS --> GM

    FC --> FE
    BC --> BE
    BE --> DB

    RM -.-> |JWT Claims| FE
    AM -.-> |User Context| BE

    style REALM fill:#f9f,stroke:#333,stroke-width:2px
    style FC fill:#e1f5fe
    style BC fill:#f3e5f5
    style FE fill:#e8f5e8
    style BE fill:#fff3e0
```

## Client Configuration Flow

```mermaid
flowchart TD
    START[Start Keycloak Setup] --> REALM_CHECK{Realm Exists?}
    REALM_CHECK -->|No| CREATE_REALM[Create OneCRM Realm]
    REALM_CHECK -->|Yes| CREATE_FRONTEND[Create Frontend Client]
    CREATE_REALM --> CREATE_FRONTEND

    CREATE_FRONTEND --> CONFIG_FRONTEND[Configure Frontend Client<br/>- Public Client<br/>- Standard Flow<br/>- Valid Redirect URIs]

    CONFIG_FRONTEND --> CREATE_BACKEND[Create Backend Client]
    CREATE_BACKEND --> CONFIG_BACKEND[Configure Backend Client<br/>- Confidential Client<br/>- Service Accounts<br/>- Client Secret]

    CONFIG_BACKEND --> CREATE_ROLES[Create Realm Roles<br/>- super-admin<br/>- org-admin<br/>- dept-manager<br/>- sales-rep<br/>- support-agent<br/>- marketing-user<br/>- viewer]

    CREATE_ROLES --> CREATE_CLIENT_ROLES[Create Client Roles<br/>- contacts:*<br/>- companies:*<br/>- deals:*<br/>- users:*<br/>- reports:*]

    CREATE_CLIENT_ROLES --> MAP_ROLES[Map Client Roles to Realm Roles]
    MAP_ROLES --> CREATE_MAPPERS[Create Token Mappers<br/>- Role Mapper<br/>- Organization Mapper<br/>- Department Mapper]

    CREATE_MAPPERS --> CREATE_USERS[Create Test Users]
    CREATE_USERS --> ASSIGN_ROLES[Assign Roles to Users]
    ASSIGN_ROLES --> TEST_CONFIG[Test Configuration]

    TEST_CONFIG --> END[Setup Complete]

    style START fill:#4CAF50
    style END fill:#2196F3
    style CREATE_REALM fill:#FF9800
    style TEST_CONFIG fill:#9C27B0
```

## Client Configuration

### 1. Create Frontend Client

#### Basic Settings
```
Client ID: onecrm-frontend
Client Type: OpenID Connect
Client authentication: OFF (Public client)
```

#### Capability Config
```
☑ Standard flow
☑ Direct access grants
☐ Implicit flow
☐ Service accounts roles
☐ OAuth 2.0 Device Authorization Grant
☐ OIDC CIBA Grant
```

#### Login Settings

**Root URL**
```
Development: http://localhost:3000
Production: https://your-domain.com
```

**Valid Redirect URIs**
```
Development: 
  http://localhost:3000/*
  http://localhost:3000/auth/callback

Production:
  https://your-domain.com/*
  https://your-domain.com/auth/callback
```

**Valid Post Logout Redirect URIs**
```
Development: http://localhost:3000
Production: https://your-domain.com
```

**Web Origins**
```
Development: http://localhost:3000
Production: https://your-domain.com
```

#### Advanced Settings
```
Access Token Lifespan: 15 minutes
Client Session Idle: 30 minutes
Client Session Max: 12 hours
```

### 2. Create Backend Client

#### Basic Settings
```
Client ID: onecrm-backend
Client Type: OpenID Connect
Client authentication: ON (Confidential client)
```

#### Capability Config
```
☐ Standard flow
☐ Direct access grants
☐ Implicit flow
☑ Service accounts roles
☐ OAuth 2.0 Device Authorization Grant
☐ OIDC CIBA Grant
```

#### Credentials Tab
```
Client Authenticator: Client Id and Secret
```
**Note**: Copy the generated client secret for backend configuration.

### 3. Realm Settings

#### Login Tab
```
☑ User registration: ON
☑ Forgot password: ON
☑ Remember me: ON
☑ Login with email: ON
```

#### Email Tab
```
From: <EMAIL>
From Display Name: OneCRM
Host: your-smtp-host
Port: 587
☑ Enable StartTLS: ON
☑ Enable Authentication: ON
Username: your-smtp-username
Password: your-smtp-password
```

#### Tokens Tab
```
Access Token Lifespan: 15 minutes
Access Token Lifespan For Implicit Flow: 15 minutes
Client login timeout: 1 minute
Login timeout: 30 minutes
Login action timeout: 5 minutes
```

### 4. User Attributes (Optional)

#### Custom Attributes
```
organization_id: String
department: String
role: String
phone: String
```

#### Mappers for Frontend Client

**Organization ID Mapper**
```
Name: organization-id
Mapper Type: User Attribute
User Attribute: organization_id
Token Claim Name: organization_id
Claim JSON Type: String
☑ Add to ID token
☑ Add to access token
☑ Add to userinfo
```

**Role Mapper**
```
Name: user-roles
Mapper Type: User Realm Role
Token Claim Name: roles
Claim JSON Type: String
☑ Add to ID token
☑ Add to access token
☐ Add to userinfo
```

## Realm Roles

### Create Roles
```
admin: Full system access
manager: Management access
user: Standard user access
viewer: Read-only access
```

### Role Descriptions
```
admin: "System administrator with full access"
manager: "Department manager with team access"
user: "Standard user with basic CRM access"
viewer: "Read-only access to CRM data"
```

## Test User Creation

### Admin User
```
Username: admin
Email: <EMAIL>
First Name: System
Last Name: Administrator
☑ Email Verified: ON
☑ Enabled: ON

Credentials:
Password: admin123 (temporary)
☐ Temporary: OFF

Role Mappings:
- admin
```

### Test User
```
Username: testuser
Email: <EMAIL>
First Name: Test
Last Name: User
☑ Email Verified: ON
☑ Enabled: ON

Credentials:
Password: test123 (temporary)
☐ Temporary: OFF

Role Mappings:
- user

Attributes:
organization_id: org-001
department: Sales
```

## Environment Configuration

### Development (.env.local)
```bash
# Keycloak Configuration
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002/api
```

### Production (.env.production)
```bash
# Keycloak Configuration
NEXT_PUBLIC_KEYCLOAK_URL=https://auth.your-domain.com
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.your-domain.com/api
```

### Backend (.env)
```bash
# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=your-generated-secret

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/onecrm
```

## Verification Steps

### 1. Test Frontend Client
```bash
# Navigate to your app
http://localhost:3000

# Should redirect to Keycloak login
http://localhost:8080/realms/onecrm/protocol/openid-connect/auth?...
```

### 2. Test Backend Token Validation
```bash
# Get token from frontend login
# Test API endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3002/api/profile
```

### 3. Verify Token Content
```bash
# Decode JWT token at jwt.io
# Check claims: sub, email, roles, organization_id
```

## Troubleshooting

### Common Issues

**Invalid Redirect URI**
- Check Valid Redirect URIs in client settings
- Ensure exact match including protocol and port

**CORS Errors**
- Verify Web Origins in client settings
- Check browser network tab for preflight requests

**Token Validation Fails**
- Verify client secret in backend configuration
- Check realm name spelling
- Ensure Keycloak URL is accessible from backend

**User Cannot Login**
- Check user is enabled
- Verify email is confirmed
- Check password policy compliance

### Debug Commands

**Test Keycloak Connectivity**
```bash
curl http://localhost:8080/realms/onecrm/.well-known/openid_configuration
```

**Validate Token**
```bash
curl -X POST http://localhost:8080/realms/onecrm/protocol/openid-connect/token/introspect \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "client_id=onecrm-backend" \
  -d "client_secret=YOUR_SECRET" \
  -d "token=YOUR_ACCESS_TOKEN"
```
