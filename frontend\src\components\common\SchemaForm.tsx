'use client';

import React from 'react';
import Form from '@rjsf/mui';
import validator from '@rjsf/validator-ajv8';
import { RJSFSchema, UiSchema } from '@rjsf/utils';
import { Box, Paper, Typography } from '@mui/material';

interface SchemaFormProps {
  schema: RJSFSchema;
  uiSchema?: UiSchema;
  formData?: any;
  onSubmit?: (data: any) => void;
  onChange?: (data: any) => void;
  title?: string;
  disabled?: boolean;
  showErrorList?: boolean;
}

export const SchemaForm: React.FC<SchemaFormProps> = ({
  schema,
  uiSchema = {},
  formData = {},
  onSubmit,
  onChange,
  title,
  disabled = false,
  showErrorList = true,
}) => {
  const handleSubmit = (data: any) => {
    if (onSubmit) {
      onSubmit(data.formData);
    }
  };

  const handleChange = (data: any) => {
    if (onChange) {
      onChange(data.formData);
    }
  };

  return (
    <Paper elevation={1} sx={{ p: 3 }}>
      {title && (
        <Typography variant="h6" component="h2" gutterBottom>
          {title}
        </Typography>
      )}
      
      <Box sx={{ mt: 2 }}>
        <Form
          schema={schema}
          uiSchema={uiSchema}
          formData={formData}
          validator={validator}
          onSubmit={handleSubmit}
          onChange={handleChange}
          disabled={disabled}
          showErrorList={showErrorList ? "top" : false}
          liveValidate
        />
      </Box>
    </Paper>
  );
};

// Common schema templates for CRM entities
export const ContactSchema: RJSFSchema = {
  type: 'object',
  required: ['firstName', 'lastName', 'email'],
  properties: {
    firstName: {
      type: 'string',
      title: 'First Name',
      minLength: 1,
    },
    lastName: {
      type: 'string',
      title: 'Last Name',
      minLength: 1,
    },
    email: {
      type: 'string',
      title: 'Email',
      format: 'email',
    },
    phone: {
      type: 'string',
      title: 'Phone Number',
    },
    company: {
      type: 'string',
      title: 'Company',
    },
    position: {
      type: 'string',
      title: 'Position',
    },
    notes: {
      type: 'string',
      title: 'Notes',
    },
  },
};

export const CompanySchema: RJSFSchema = {
  type: 'object',
  required: ['name', 'industry'],
  properties: {
    name: {
      type: 'string',
      title: 'Company Name',
      minLength: 1,
    },
    industry: {
      type: 'string',
      title: 'Industry',
      enum: [
        'Technology',
        'Healthcare',
        'Finance',
        'Manufacturing',
        'Retail',
        'Education',
        'Other',
      ],
    },
    website: {
      type: 'string',
      title: 'Website',
      format: 'uri',
    },
    employees: {
      type: 'integer',
      title: 'Number of Employees',
      minimum: 1,
    },
    revenue: {
      type: 'number',
      title: 'Annual Revenue',
      minimum: 0,
    },
    description: {
      type: 'string',
      title: 'Description',
    },
  },
};

export const DealSchema: RJSFSchema = {
  type: 'object',
  required: ['title', 'value', 'stage'],
  properties: {
    title: {
      type: 'string',
      title: 'Deal Title',
      minLength: 1,
    },
    value: {
      type: 'number',
      title: 'Deal Value',
      minimum: 0,
    },
    stage: {
      type: 'string',
      title: 'Stage',
      enum: [
        'Prospecting',
        'Qualification',
        'Proposal',
        'Negotiation',
        'Closed Won',
        'Closed Lost',
      ],
    },
    probability: {
      type: 'integer',
      title: 'Probability (%)',
      minimum: 0,
      maximum: 100,
    },
    expectedCloseDate: {
      type: 'string',
      title: 'Expected Close Date',
      format: 'date',
    },
    description: {
      type: 'string',
      title: 'Description',
    },
  },
};

// UI Schema templates for better form rendering
export const ContactUISchema: UiSchema = {
  firstName: {
    'ui:placeholder': 'Enter first name',
  },
  lastName: {
    'ui:placeholder': 'Enter last name',
  },
  email: {
    'ui:placeholder': 'Enter email address',
  },
  phone: {
    'ui:placeholder': 'Enter phone number',
  },
  notes: {
    'ui:widget': 'textarea',
    'ui:options': {
      rows: 4,
    },
  },
};

export const CompanyUISchema: UiSchema = {
  name: {
    'ui:placeholder': 'Enter company name',
  },
  website: {
    'ui:placeholder': 'https://example.com',
  },
  employees: {
    'ui:placeholder': 'Number of employees',
  },
  revenue: {
    'ui:placeholder': 'Annual revenue in USD',
  },
  description: {
    'ui:widget': 'textarea',
    'ui:options': {
      rows: 4,
    },
  },
};

export const DealUISchema: UiSchema = {
  title: {
    'ui:placeholder': 'Enter deal title',
  },
  value: {
    'ui:placeholder': 'Deal value in USD',
  },
  probability: {
    'ui:widget': 'range',
  },
  description: {
    'ui:widget': 'textarea',
    'ui:options': {
      rows: 3,
    },
  },
};
