# Keycloak Quick Setup Guide for OneCRM

## Quick Setup Overview

```mermaid
graph LR
    A[Access Keycloak Admin] --> B[Select/Create Realm]
    B --> C[Create Frontend Client]
    C --> D[Create Backend Client]
    D --> E[Configure Backend URIs]
    E --> F[Create Roles]
    F --> G[Create Test User]
    G --> H[Assign Roles]
    H --> I[Configure Mappers]
    I --> J[Test Complete Setup]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#9f9,stroke:#333,stroke-width:2px
```

## Step-by-Step Configuration

### 1. Access Keycloak Admin Console
```
URL: http://localhost:8080/admin
Login with your admin credentials
```

### 2. Select/Create Realm
- Click dropdown next to "Master" realm
- Select existing `onecrm` realm OR click "Create Realm"
- If creating new: Name = `onecrm`, click "Create"

### 3. Create Frontend Client

**Navigate**: Clients → Create Client

**General Settings**
```
Client type: OpenID Connect
Client ID: onecrm-frontend
```
Click "Next"

**Capability Config**
```
☑ Standard flow
☑ Direct access grants
☐ Implicit flow
☐ Service accounts roles
```
Click "Next"

**Login Settings**
```
Root URL: http://localhost:3000
Valid redirect URIs: http://localhost:3000/*
Valid post logout redirect URIs: http://localhost:3000
Web origins: http://localhost:3000
```
Click "Save"

### 4. Create Backend Client

**Navigate**: Clients → Create Client

**General Settings**
```
Client type: OpenID Connect
Client ID: onecrm-backend
```
Click "Next"

**Capability Config**
```
☑ Client authentication
☐ Standard flow
☐ Direct access grants
☑ Service accounts roles
```
Click "Next"

**Login Settings**
```
Root URL: http://localhost:3002
Valid redirect URIs: http://localhost:3002/*
Valid post logout redirect URIs: http://localhost:3002
Web origins: http://localhost:3002
Admin URL: http://localhost:3002/admin
```
Click "Save"

**Get Client Secret**
- Go to "Credentials" tab
- Copy the "Client secret" value
- Save this for backend configuration

**Configure Service Account (Important for Backend)**
- Go to "Service accounts roles" tab
- Click "Assign role"
- Select "realm-management" from "Filter by clients"
- Assign these roles:
  ```
  ☑ view-users
  ☑ view-clients
  ☑ view-realm
  ☑ manage-users (if backend needs to create users)
  ```
- Click "Assign"

### 5. Create Test User

**Navigate**: Users → Create User

**User Details**
```
Username: admin
Email: <EMAIL>
First name: System
Last name: Administrator
☑ Email verified
☑ Enabled
```
Click "Create"

**Set Password**
- Go to "Credentials" tab
- Click "Set password"
```
Password: admin123
☐ Temporary
```
Click "Save"

### 6. Create Roles

**Navigate**: Realm roles → Create role

Create these roles:
```
Role name: admin
Description: System administrator

Role name: user  
Description: Standard user

Role name: manager
Description: Department manager

Role name: viewer
Description: Read-only access
```

### 7. Assign Role to User

**Navigate**: Users → Select "admin" user → Role mapping

- Click "Assign role"
- Select "admin" role
- Click "Assign"

### 8. Configure Client Mappers (Frontend Client)

**Navigate**: Clients → onecrm-frontend → Client scopes → onecrm-frontend-dedicated → Mappers

**Add Role Mapper**
- Click "Add mapper" → "By configuration" → "User Realm Role"
```
Name: user-roles
Token Claim Name: roles
☑ Add to ID token
☑ Add to access token
```
Click "Save"

## Production Configuration

### Frontend Client - Production Settings
```
Root URL: https://your-domain.com
Valid redirect URIs: https://your-domain.com/*
Valid post logout redirect URIs: https://your-domain.com
Web origins: https://your-domain.com
```

### Backend Client - Production Settings
```
Root URL: https://api.your-domain.com
Valid redirect URIs: https://api.your-domain.com/*
Valid post logout redirect URIs: https://api.your-domain.com
Web origins: https://api.your-domain.com
Admin URL: https://api.your-domain.com/admin
```

### Environment Files

**Frontend (.env.local)**
```bash
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
```

**Frontend (.env.production)**
```bash
NEXT_PUBLIC_KEYCLOAK_URL=https://auth.your-domain.com
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
```

**Backend (.env)**
```bash
# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=your-copied-secret

# Backend Server Configuration
PORT=3002
API_BASE_URL=http://localhost:3002

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

**Backend (.env.production)**
```bash
# Keycloak Configuration
KEYCLOAK_URL=https://auth.your-domain.com
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=your-production-secret

# Backend Server Configuration
PORT=3002
API_BASE_URL=https://api.your-domain.com

# CORS Configuration
FRONTEND_URL=https://your-domain.com
ALLOWED_ORIGINS=https://your-domain.com
```

## Quick Test

### 1. Test Keycloak Endpoint
```bash
curl http://localhost:8080/realms/onecrm/.well-known/openid_configuration
```
Should return JSON with configuration

### 2. Test Backend Token Validation
```bash
# Start backend server
cd backend
npm run dev

# Backend should be running on http://localhost:3002
# Check backend health endpoint
curl http://localhost:3002/api/health
```

### 3. Test Login Flow
1. Start OneCRM frontend: `npm run dev`
2. Navigate to: `http://localhost:3000`
3. Should redirect to Keycloak login
4. Login with: `admin` / `admin123`
5. Should redirect back to OneCRM dashboard

### 4. Test Backend API with Token
```bash
# Get token from frontend login (copy from DevTools)
# Test protected API endpoint
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     http://localhost:3002/api/profile

# Should return user profile data
```

### 5. Verify Token
- Open browser DevTools → Application → Local Storage
- Check for tokens: `access_token`, `refresh_token`, `id_token`

## Common Issues & Fixes

**"Invalid redirect URI"**
- Check exact match in Valid redirect URIs
- Include protocol (http/https) and port

**"CORS error"**
- Add frontend URL to Web origins
- Restart Keycloak after changes

**"Client not found"**
- Verify client ID spelling
- Check you're in correct realm

**"Invalid client credentials"**
- Verify client secret is correct
- Check client authentication is enabled for backend client

## Complete Configuration Checklist

### ✅ Realm Configuration
- [ ] OneCRM realm created or selected
- [ ] Realm settings configured (login, tokens, email)

### ✅ Frontend Client (onecrm-frontend)
- [ ] Client created as OpenID Connect
- [ ] Public client (no authentication)
- [ ] Standard flow enabled
- [ ] Direct access grants enabled
- [ ] Valid redirect URIs: `http://localhost:3000/*`
- [ ] Valid post logout redirect URIs: `http://localhost:3000`
- [ ] Web origins: `http://localhost:3000`

### ✅ Backend Client (onecrm-backend)
- [ ] Client created as OpenID Connect
- [ ] Confidential client (authentication enabled)
- [ ] Service accounts roles enabled
- [ ] Valid redirect URIs: `http://localhost:3002/*`
- [ ] Valid post logout redirect URIs: `http://localhost:3002`
- [ ] Web origins: `http://localhost:3002`
- [ ] Admin URL: `http://localhost:3002/admin`
- [ ] Client secret copied and saved
- [ ] Service account roles assigned (view-users, view-clients, view-realm)

### ✅ Roles Configuration
- [ ] Realm roles created (admin, user, manager, viewer)
- [ ] Client roles created (contacts:*, deals:*, etc.)
- [ ] Role mappings configured

### ✅ Token Mappers
- [ ] Role mapper configured
- [ ] Organization mapper configured
- [ ] Department mapper configured

### ✅ Test User
- [ ] Username: admin
- [ ] Password: admin123
- [ ] Email verified
- [ ] Admin role assigned
- [ ] User attributes set (organization_id, department)

### ✅ Environment Variables
- [ ] Frontend .env.local configured
- [ ] Backend .env configured
- [ ] Client secret properly set in backend

### ✅ Testing
- [ ] Keycloak endpoint accessible
- [ ] Backend server starts successfully
- [ ] Frontend redirects to Keycloak login
- [ ] Login successful with test user
- [ ] Token validation working
- [ ] API calls with Bearer token successful

This complete checklist ensures OneCRM authentication is fully configured and working with Keycloak.
