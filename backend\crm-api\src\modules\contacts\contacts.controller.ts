import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  UseInterceptors,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
// import { AuthGuard, RoleGuard } from 'nest-keycloak-connect';
import { ContactsService } from './contacts.service';
import { Contact } from './contact.entity';
import { CreateContactDto } from './dto/create-contact.dto';
import { UpdateContactDto } from './dto/update-contact.dto';
import { SearchContactsDto, ContactsResponseDto } from './dto/search-contacts.dto';
import { CurrentUser, CurrentUserData } from '../../common/decorators/current-user.decorator';
import { Roles } from '../../common/decorators/roles.decorator';
import { RequirePermissions } from '../../common/decorators/permissions.decorator';
import { TenantGuard } from '../../common/guards/tenant.guard';
import { PermissionsGuard } from '../../common/guards/permissions.guard';
import { TenantInterceptor } from '../../common/interceptors/tenant.interceptor';
import { Public } from '../../common/decorators/public.decorator';

@ApiTags('contacts')
@ApiBearerAuth()
@Controller('contacts')
// @UseGuards(AuthGuard, RoleGuard, TenantGuard, PermissionsGuard)
// @UseInterceptors(TenantInterceptor)
export class ContactsController {
  constructor(private readonly contactsService: ContactsService) {}

  @Public()
  @Get('test')
  @ApiOperation({ summary: 'Test endpoint for contacts module' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async testEndpoint() {
    return {
      status: 'ok',
      message: 'Contacts module is working',
      timestamp: new Date().toISOString(),
      endpoints: [
        'GET /api/contacts/test',
        'GET /api/contacts/test-data',
        'GET /api/contacts',
        'POST /api/contacts',
      ],
    };
  }

  @Public()
  @Get('test-data')
  @ApiOperation({ summary: 'Test endpoint to get contacts data directly' })
  @ApiResponse({ status: 200, description: 'Test data retrieved successfully' })
  async testDataEndpoint() {
    return this.contactsService.getTestData();
  }

  @Post()
  // @RequirePermissions('contacts:write')
  @ApiOperation({ summary: 'Create a new contact' })
  @ApiResponse({ status: 201, description: 'Contact created successfully', type: Contact })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(@Body() createContactDto: CreateContactDto): Promise<Contact> {
    return this.contactsService.create(createContactDto);
  }

  @Get()
  // @RequirePermissions('contacts:read')
  @ApiOperation({ summary: 'Get all contacts with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Contacts retrieved successfully', type: ContactsResponseDto })
  @ApiQuery({ name: 'search', required: false, description: 'Search query for name, email, or company' })
  @ApiQuery({ name: 'company', required: false, description: 'Filter by company name' })
  @ApiQuery({ name: 'leadStatus', required: false, description: 'Filter by lead status' })
  @ApiQuery({ name: 'leadSource', required: false, description: 'Filter by lead source' })
  @ApiQuery({ name: 'assignedToId', required: false, description: 'Filter by assigned user ID' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({ name: 'sortOrder', required: false, description: 'Sort order (ASC/DESC)' })
  async findAll(@Query() searchDto: SearchContactsDto): Promise<ContactsResponseDto> {
    return this.contactsService.findAll(searchDto);
  }

  @Get('stats')
  // @RequirePermissions('contacts:read')
  @ApiOperation({ summary: 'Get contact statistics' })
  @ApiResponse({ status: 200, description: 'Contact statistics retrieved successfully' })
  async getStats(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    bySource: Record<string, number>;
    recentlyCreated: number;
  }> {
    return this.contactsService.getContactStats();
  }

  @Get(':id')
  // @RequirePermissions('contacts:read')
  @ApiOperation({ summary: 'Get contact by ID' })
  @ApiResponse({ status: 200, description: 'Contact retrieved successfully', type: Contact })
  @ApiResponse({ status: 404, description: 'Contact not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Contact> {
    return this.contactsService.findById(id);
  }

  @Put(':id')
  // @RequirePermissions('contacts:write')
  @ApiOperation({ summary: 'Update contact by ID' })
  @ApiResponse({ status: 200, description: 'Contact updated successfully', type: Contact })
  @ApiResponse({ status: 404, description: 'Contact not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateContactDto: UpdateContactDto
  ): Promise<Contact> {
    return this.contactsService.update(id, updateContactDto);
  }

  @Delete(':id')
  // @RequirePermissions('contacts:delete')
  @ApiOperation({ summary: 'Delete contact by ID (soft delete)' })
  @ApiResponse({ status: 200, description: 'Contact deleted successfully' })
  @ApiResponse({ status: 404, description: 'Contact not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.contactsService.remove(id);
    return { message: 'Contact deleted successfully' };
  }

  @Post(':id/restore')
  // @Roles('admin')
  // @RequirePermissions('contacts:write')
  @ApiOperation({ summary: 'Restore soft-deleted contact' })
  @ApiResponse({ status: 200, description: 'Contact restored successfully', type: Contact })
  @ApiResponse({ status: 404, description: 'Contact not found' })
  @ApiResponse({ status: 403, description: 'Only administrators can restore contacts' })
  async restore(@Param('id', ParseUUIDPipe) id: string): Promise<Contact> {
    return this.contactsService.restore(id);
  }

  @Post('bulk-delete')
  // @Roles('admin')
  // @RequirePermissions('contacts:delete')
  @ApiOperation({ summary: 'Bulk delete contacts' })
  @ApiResponse({ status: 200, description: 'Contacts deleted successfully' })
  @ApiResponse({ status: 403, description: 'Only administrators can bulk delete contacts' })
  async bulkDelete(@Body() body: { ids: string[] }): Promise<{ deleted: number }> {
    return this.contactsService.bulkDelete(body.ids);
  }
}
