import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { OnboardingStep, OnboardingStepStatus, OnboardingStepType } from './entities/onboarding-step.entity';
import { OnboardingTemplate, OnboardingTemplateType } from './entities/onboarding-template.entity';
import { TenantOnboardingProgress } from './entities/tenant-onboarding-progress.entity';
import { OrganizationService } from '../organization/organization.service';
import { UserService } from '../user/user.service';
import { EmailService } from '../email/email.service';
import { NotificationService } from '../notification/notification.service';

export interface OnboardingInitiationDto {
  organizationId: string;
  templateType?: OnboardingTemplateType;
  customSteps?: Partial<OnboardingStep>[];
  assignedTo?: string;
  autoStart?: boolean;
}

export interface OnboardingProgressDto {
  stepId: string;
  status: OnboardingStepStatus;
  data?: Record<string, any>;
  notes?: string;
  failureReason?: string;
}

export interface OnboardingStatusResponse {
  organizationId: string;
  templateId: string;
  templateName: string;
  totalSteps: number;
  completedSteps: number;
  currentStep?: TenantOnboardingProgress;
  progressPercentage: number;
  estimatedCompletionDate?: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'stalled';
  steps: TenantOnboardingProgress[];
}

@Injectable()
export class TenantOnboardingService {
  constructor(
    @InjectRepository(OnboardingStep)
    private readonly onboardingStepRepository: Repository<OnboardingStep>,
    @InjectRepository(OnboardingTemplate)
    private readonly onboardingTemplateRepository: Repository<OnboardingTemplate>,
    @InjectRepository(TenantOnboardingProgress)
    private readonly progressRepository: Repository<TenantOnboardingProgress>,
    private readonly organizationService: OrganizationService,
    private readonly userService: UserService,
    private readonly emailService: EmailService,
    private readonly notificationService: NotificationService,
  ) {}

  async initiateOnboarding(dto: OnboardingInitiationDto): Promise<OnboardingStatusResponse> {
    // Validate organization exists
    const organization = await this.organizationService.findById(dto.organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Check if onboarding already exists
    const existingProgress = await this.progressRepository.findOne({
      where: { organizationId: dto.organizationId },
    });

    if (existingProgress) {
      throw new BadRequestException('Onboarding already initiated for this organization');
    }

    // Get appropriate template
    let template: OnboardingTemplate;
    if (dto.templateType) {
      template = await this.onboardingTemplateRepository.findOne({
        where: { type: dto.templateType, active: true },
        relations: ['steps'],
      });
    } else {
      template = await this.onboardingTemplateRepository.findOne({
        where: { default: true, active: true },
        relations: ['steps'],
      });
    }

    if (!template) {
      throw new NotFoundException('No suitable onboarding template found');
    }

    // Create progress records for each step
    const progressRecords: TenantOnboardingProgress[] = [];
    
    for (const step of template.steps.sort((a, b) => a.order - b.order)) {
      const progress = this.progressRepository.create({
        organizationId: dto.organizationId,
        templateId: template.id,
        stepId: step.id,
        status: OnboardingStepStatus.PENDING,
        assignedTo: dto.assignedTo,
      });
      
      progressRecords.push(await this.progressRepository.save(progress));
    }

    // Start first step if auto-start is enabled
    if (dto.autoStart && progressRecords.length > 0) {
      await this.startStep(progressRecords[0].id);
    }

    // Send welcome email
    await this.sendWelcomeEmail(organization, template);

    // Create notification
    await this.notificationService.create({
      organizationId: dto.organizationId,
      type: 'onboarding_started',
      title: 'Welcome to OneCRM!',
      message: `Your onboarding journey has begun. Follow the steps to get the most out of OneCRM.`,
      data: { templateId: template.id },
    });

    return this.getOnboardingStatus(dto.organizationId);
  }

  async getOnboardingStatus(organizationId: string): Promise<OnboardingStatusResponse> {
    const progressRecords = await this.progressRepository.find({
      where: { organizationId },
      relations: ['template', 'step'],
      order: { step: { order: 'ASC' } },
    });

    if (progressRecords.length === 0) {
      throw new NotFoundException('No onboarding found for this organization');
    }

    const template = progressRecords[0].template;
    const totalSteps = progressRecords.length;
    const completedSteps = progressRecords.filter(p => p.status === OnboardingStepStatus.COMPLETED).length;
    const progressPercentage = Math.round((completedSteps / totalSteps) * 100);

    // Find current step (first non-completed step)
    const currentStep = progressRecords.find(p => 
      p.status === OnboardingStepStatus.IN_PROGRESS || 
      p.status === OnboardingStepStatus.PENDING
    );

    // Determine overall status
    let status: 'not_started' | 'in_progress' | 'completed' | 'stalled';
    if (completedSteps === 0) {
      status = 'not_started';
    } else if (completedSteps === totalSteps) {
      status = 'completed';
    } else if (this.isOnboardingStalled(progressRecords)) {
      status = 'stalled';
    } else {
      status = 'in_progress';
    }

    // Calculate estimated completion date
    let estimatedCompletionDate: Date | undefined;
    if (status === 'in_progress' && template.estimatedCompletionDays) {
      const startDate = progressRecords[0].createdAt;
      estimatedCompletionDate = new Date(startDate);
      estimatedCompletionDate.setDate(estimatedCompletionDate.getDate() + template.estimatedCompletionDays);
    }

    return {
      organizationId,
      templateId: template.id,
      templateName: template.name,
      totalSteps,
      completedSteps,
      currentStep,
      progressPercentage,
      estimatedCompletionDate,
      status,
      steps: progressRecords,
    };
  }

  async updateStepProgress(progressId: string, dto: OnboardingProgressDto): Promise<TenantOnboardingProgress> {
    const progress = await this.progressRepository.findOne({
      where: { id: progressId },
      relations: ['step', 'organization'],
    });

    if (!progress) {
      throw new NotFoundException('Onboarding progress not found');
    }

    const previousStatus = progress.status;
    
    // Update progress
    progress.status = dto.status;
    progress.data = { ...progress.data, ...dto.data };
    progress.notes = dto.notes || progress.notes;
    progress.failureReason = dto.failureReason;

    // Handle status transitions
    if (previousStatus !== OnboardingStepStatus.IN_PROGRESS && dto.status === OnboardingStepStatus.IN_PROGRESS) {
      progress.startedAt = new Date();
    }

    if (dto.status === OnboardingStepStatus.COMPLETED) {
      progress.completedAt = new Date();
      if (progress.startedAt) {
        progress.durationMinutes = Math.round(
          (progress.completedAt.getTime() - progress.startedAt.getTime()) / (1000 * 60)
        );
      }

      // Auto-start next step
      await this.autoStartNextStep(progress.organizationId, progress.step.order);
    }

    if (dto.status === OnboardingStepStatus.FAILED) {
      progress.retryCount += 1;
    }

    const updatedProgress = await this.progressRepository.save(progress);

    // Send notifications for important status changes
    await this.handleStatusChangeNotifications(updatedProgress, previousStatus);

    return updatedProgress;
  }

  async startStep(progressId: string): Promise<TenantOnboardingProgress> {
    return this.updateStepProgress(progressId, {
      stepId: '', // Will be ignored
      status: OnboardingStepStatus.IN_PROGRESS,
    });
  }

  async completeStep(progressId: string, data?: Record<string, any>, notes?: string): Promise<TenantOnboardingProgress> {
    return this.updateStepProgress(progressId, {
      stepId: '', // Will be ignored
      status: OnboardingStepStatus.COMPLETED,
      data,
      notes,
    });
  }

  async skipStep(progressId: string, reason?: string): Promise<TenantOnboardingProgress> {
    return this.updateStepProgress(progressId, {
      stepId: '', // Will be ignored
      status: OnboardingStepStatus.SKIPPED,
      notes: reason,
    });
  }

  async retryStep(progressId: string): Promise<TenantOnboardingProgress> {
    const progress = await this.progressRepository.findOne({
      where: { id: progressId },
    });

    if (!progress) {
      throw new NotFoundException('Onboarding progress not found');
    }

    if (progress.status !== OnboardingStepStatus.FAILED) {
      throw new BadRequestException('Can only retry failed steps');
    }

    return this.updateStepProgress(progressId, {
      stepId: '', // Will be ignored
      status: OnboardingStepStatus.PENDING,
      failureReason: undefined,
    });
  }

  private async autoStartNextStep(organizationId: string, currentStepOrder: number): Promise<void> {
    const nextStep = await this.progressRepository.findOne({
      where: {
        organizationId,
        status: OnboardingStepStatus.PENDING,
      },
      relations: ['step'],
      order: { step: { order: 'ASC' } },
    });

    if (nextStep && nextStep.step.order === currentStepOrder + 1) {
      await this.startStep(nextStep.id);
    }
  }

  private isOnboardingStalled(progressRecords: TenantOnboardingProgress[]): boolean {
    const inProgressStep = progressRecords.find(p => p.status === OnboardingStepStatus.IN_PROGRESS);
    if (!inProgressStep || !inProgressStep.startedAt) {
      return false;
    }

    // Consider stalled if in progress for more than 7 days
    const daysSinceStarted = (Date.now() - inProgressStep.startedAt.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceStarted > 7;
  }

  private async sendWelcomeEmail(organization: any, template: OnboardingTemplate): Promise<void> {
    // Implementation would send welcome email with onboarding information
    await this.emailService.sendTemplate({
      to: organization.primaryContactEmail,
      template: 'onboarding-welcome',
      data: {
        organizationName: organization.name,
        templateName: template.name,
        estimatedDays: template.estimatedCompletionDays,
      },
    });
  }

  private async handleStatusChangeNotifications(
    progress: TenantOnboardingProgress,
    previousStatus: OnboardingStepStatus,
  ): Promise<void> {
    if (progress.status === OnboardingStepStatus.COMPLETED && previousStatus !== OnboardingStepStatus.COMPLETED) {
      await this.notificationService.create({
        organizationId: progress.organizationId,
        type: 'onboarding_step_completed',
        title: 'Onboarding Step Completed',
        message: `You've completed the "${progress.step.name}" step. Great progress!`,
        data: { stepId: progress.stepId, progressId: progress.id },
      });
    }

    if (progress.status === OnboardingStepStatus.FAILED) {
      await this.notificationService.create({
        organizationId: progress.organizationId,
        type: 'onboarding_step_failed',
        title: 'Onboarding Step Failed',
        message: `The "${progress.step.name}" step encountered an issue. Please review and retry.`,
        data: { stepId: progress.stepId, progressId: progress.id, reason: progress.failureReason },
      });
    }
  }
}
