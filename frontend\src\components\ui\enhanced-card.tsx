'use client'

import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card'
import { Badge } from './badge'
import { Button } from './button'
import { MoreHorizontal, ExternalLink } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'

const enhancedCardVariants = cva(
  'transition-all duration-200',
  {
    variants: {
      variant: {
        default: '',
        elevated: 'shadow-lg hover:shadow-xl',
        outlined: 'border-2',
        ghost: 'border-0 shadow-none bg-transparent',
        gradient: 'bg-gradient-to-br from-background to-muted',
      },
      size: {
        sm: 'p-3',
        default: 'p-6',
        lg: 'p-8',
      },
      interactive: {
        true: 'cursor-pointer hover:bg-accent/50 hover:scale-[1.02] active:scale-[0.98]',
        false: '',
      },
      loading: {
        true: 'animate-pulse',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      interactive: false,
      loading: false,
    },
  }
)

export interface EnhancedCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof enhancedCardVariants> {
  title?: string
  description?: string
  badge?: {
    text: string
    variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  actions?: Array<{
    label: string
    onClick: () => void
    icon?: React.ReactNode
    variant?: 'default' | 'destructive' | 'outline' | 'ghost'
  }>
  onCardClick?: () => void
  loading?: boolean
  footer?: React.ReactNode
  headerActions?: React.ReactNode
  status?: 'success' | 'warning' | 'error' | 'info'
  progress?: number
}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  (
    {
      className,
      variant,
      size,
      interactive,
      loading,
      title,
      description,
      badge,
      actions,
      onCardClick,
      footer,
      headerActions,
      status,
      progress,
      children,
      ...props
    },
    ref
  ) => {
    const statusColors = {
      success: 'border-l-green-500',
      warning: 'border-l-yellow-500',
      error: 'border-l-red-500',
      info: 'border-l-blue-500',
    }

    return (
      <Card
        ref={ref}
        className={cn(
          enhancedCardVariants({ variant, size, interactive, loading }),
          status && `border-l-4 ${statusColors[status]}`,
          className
        )}
        onClick={onCardClick}
        {...props}
      >
        {(title || description || badge || headerActions) && (
          <CardHeader className={cn(size === 'sm' && 'p-3 pb-2')}>
            <div className="flex items-start justify-between">
              <div className="space-y-1 flex-1">
                {title && (
                  <div className="flex items-center gap-2">
                    <CardTitle className={cn(size === 'sm' && 'text-base')}>
                      {title}
                    </CardTitle>
                    {badge && (
                      <Badge variant={badge.variant || 'default'}>
                        {badge.text}
                      </Badge>
                    )}
                  </div>
                )}
                {description && (
                  <CardDescription className={cn(size === 'sm' && 'text-xs')}>
                    {description}
                  </CardDescription>
                )}
              </div>
              
              {(headerActions || actions) && (
                <div className="flex items-center gap-2">
                  {headerActions}
                  {actions && actions.length > 0 && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Open menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {actions.map((action, index) => (
                          <DropdownMenuItem
                            key={index}
                            onClick={(e) => {
                              e.stopPropagation()
                              action.onClick()
                            }}
                            className="flex items-center gap-2"
                          >
                            {action.icon}
                            {action.label}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              )}
            </div>
            
            {progress !== undefined && (
              <div className="w-full bg-secondary rounded-full h-2 mt-2">
                <div
                  className="bg-primary h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                />
              </div>
            )}
          </CardHeader>
        )}

        {children && (
          <CardContent className={cn(size === 'sm' && 'p-3 pt-0')}>
            {loading ? (
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
                <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
              </div>
            ) : (
              children
            )}
          </CardContent>
        )}

        {footer && (
          <CardFooter className={cn(size === 'sm' && 'p-3 pt-0')}>
            {footer}
          </CardFooter>
        )}
      </Card>
    )
  }
)

EnhancedCard.displayName = 'EnhancedCard'

// Specialized card components
export const MetricCard = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedCardProps, 'children'> & {
    value: string | number
    label: string
    change?: {
      value: number
      type: 'increase' | 'decrease'
      period?: string
    }
    icon?: React.ReactNode
  }
>(({ value, label, change, icon, ...props }, ref) => (
  <EnhancedCard ref={ref} className="min-h-[120px]" {...props}>
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <p className="text-2xl font-bold">{value}</p>
        <p className="text-sm text-muted-foreground">{label}</p>
        {change && (
          <div className="flex items-center gap-1 text-xs">
            <span
              className={cn(
                change.type === 'increase' ? 'text-green-600' : 'text-red-600'
              )}
            >
              {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
            </span>
            {change.period && (
              <span className="text-muted-foreground">vs {change.period}</span>
            )}
          </div>
        )}
      </div>
      {icon && (
        <div className="text-muted-foreground">
          {icon}
        </div>
      )}
    </div>
  </EnhancedCard>
))

MetricCard.displayName = 'MetricCard'

export const ActionCard = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedCardProps, 'children'> & {
    icon: React.ReactNode
    label: string
    description?: string
    href?: string
  }
>(({ icon, label, description, href, onCardClick, ...props }, ref) => (
  <EnhancedCard
    ref={ref}
    interactive
    variant="outlined"
    onCardClick={href ? () => window.open(href, '_blank') : onCardClick}
    {...props}
  >
    <div className="flex items-center gap-3">
      <div className="p-2 bg-primary/10 rounded-lg text-primary">
        {icon}
      </div>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <p className="font-medium">{label}</p>
          {href && <ExternalLink className="h-3 w-3 text-muted-foreground" />}
        </div>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
    </div>
  </EnhancedCard>
))

ActionCard.displayName = 'ActionCard'

export const StatusCard = React.forwardRef<
  HTMLDivElement,
  Omit<EnhancedCardProps, 'status'> & {
    status: 'online' | 'offline' | 'pending' | 'error'
    lastUpdated?: string
  }
>(({ status, lastUpdated, ...props }, ref) => {
  const statusConfig = {
    online: { color: 'bg-green-500', label: 'Online', cardStatus: 'success' as const },
    offline: { color: 'bg-gray-500', label: 'Offline', cardStatus: 'error' as const },
    pending: { color: 'bg-yellow-500', label: 'Pending', cardStatus: 'warning' as const },
    error: { color: 'bg-red-500', label: 'Error', cardStatus: 'error' as const },
  }

  const config = statusConfig[status]

  return (
    <EnhancedCard ref={ref} status={config.cardStatus} {...props}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={cn('w-2 h-2 rounded-full', config.color)} />
          <span className="font-medium">{config.label}</span>
        </div>
        {lastUpdated && (
          <span className="text-xs text-muted-foreground">
            Updated {lastUpdated}
          </span>
        )}
      </div>
    </EnhancedCard>
  )
})

StatusCard.displayName = 'StatusCard'

export { EnhancedCard, enhancedCardVariants }
