import { Is<PERSON>tring, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, IsDecimal, IsInt, Min, Max, IsDateString, IsObject } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export class CreateDealDto {
  @ApiProperty({ description: 'Deal title', example: 'Enterprise Software License' })
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({ description: 'Deal description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Deal amount in USD', example: 50000.00 })
  @Type(() => Number)
  @IsDecimal({ decimal_digits: '0,2' })
  @Transform(({ value }) => parseFloat(value))
  amount: number;

  @ApiProperty({ 
    description: 'Deal stage', 
    example: 'qualification',
    enum: ['lead', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost']
  })
  @IsString()
  @MaxLength(50)
  stage: string;

  @ApiProperty({ description: 'Probability of closing (0-100)', example: 75, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  @Max(100)
  probability?: number;

  @ApiProperty({ description: 'Expected close date', example: '2024-12-31', required: false })
  @IsOptional()
  @IsDateString()
  expectedCloseDate?: string;

  @ApiProperty({ description: 'Contact ID associated with this deal', required: false })
  @IsOptional()
  @IsUUID()
  contactId?: string;

  @ApiProperty({ description: 'Company ID associated with this deal', required: false })
  @IsOptional()
  @IsUUID()
  companyId?: string;

  @ApiProperty({ description: 'User ID who owns this deal', required: false })
  @IsOptional()
  @IsUUID()
  ownerId?: string;

  @ApiProperty({ description: 'Deal source', example: 'website', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  source?: string;

  @ApiProperty({ description: 'Deal type', example: 'new-business', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  type?: string;

  @ApiProperty({ description: 'Deal priority', example: 'high', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  priority?: string;

  @ApiProperty({ description: 'Custom fields as key-value pairs', required: false })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Deal notes', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}
