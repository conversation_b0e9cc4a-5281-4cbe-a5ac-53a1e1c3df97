import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DealsService } from '../../deals/deals.service';
import { Deal } from '../../deals/entities/deal.entity';
import { Contact } from '../../contacts/entities/contact.entity';
import { Company } from '../../companies/entities/company.entity';
import { User } from '../../users/entities/user.entity';
import { CreateDealDto } from '../../deals/dto/create-deal.dto';
import { UpdateDealDto } from '../../deals/dto/update-deal.dto';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('DealsService', () => {
  let service: DealsService;
  let dealRepository: Repository<Deal>;
  let contactRepository: Repository<Contact>;
  let companyRepository: Repository<Company>;
  let userRepository: Repository<User>;

  const mockDeal = {
    id: '1',
    title: 'Enterprise Software Deal',
    amount: 50000,
    stage: 'qualification',
    probability: 75,
    expectedCloseDate: new Date('2024-12-31'),
    source: 'website',
    type: 'new-business',
    priority: 'high',
    description: 'Large enterprise deal',
    orgId: 'org-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockContact = {
    id: 'contact-1',
    firstName: 'John',
    lastName: 'Doe',
    orgId: 'org-1',
  };

  const mockCompany = {
    id: 'company-1',
    name: 'Tech Corp',
    orgId: 'org-1',
  };

  const mockUser = {
    id: 'user-1',
    firstName: 'Jane',
    lastName: 'Sales',
    orgId: 'org-1',
  };

  const mockDealRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest.fn(),
      getMany: jest.fn(),
      select: jest.fn().mockReturnThis(),
      groupBy: jest.fn().mockReturnThis(),
      getRawMany: jest.fn(),
    })),
  };

  const mockContactRepository = {
    findOne: jest.fn(),
  };

  const mockCompanyRepository = {
    findOne: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DealsService,
        {
          provide: getRepositoryToken(Deal),
          useValue: mockDealRepository,
        },
        {
          provide: getRepositoryToken(Contact),
          useValue: mockContactRepository,
        },
        {
          provide: getRepositoryToken(Company),
          useValue: mockCompanyRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<DealsService>(DealsService);
    dealRepository = module.get<Repository<Deal>>(getRepositoryToken(Deal));
    contactRepository = module.get<Repository<Contact>>(getRepositoryToken(Contact));
    companyRepository = module.get<Repository<Company>>(getRepositoryToken(Company));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const createDealDto: CreateDealDto = {
      title: 'New Deal',
      amount: 25000,
      stage: 'lead',
      probability: 50,
      expectedCloseDate: new Date('2024-12-31'),
      contactId: 'contact-1',
      companyId: 'company-1',
      ownerId: 'user-1',
      source: 'website',
      type: 'new-business',
      priority: 'medium',
      description: 'Test deal',
    };

    it('should create a deal successfully', async () => {
      mockContactRepository.findOne.mockResolvedValue(mockContact);
      mockCompanyRepository.findOne.mockResolvedValue(mockCompany);
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      mockDealRepository.create.mockReturnValue(mockDeal);
      mockDealRepository.save.mockResolvedValue(mockDeal);

      const result = await service.create(createDealDto, 'org-1');

      expect(mockContactRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'contact-1', orgId: 'org-1' },
      });
      expect(mockCompanyRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'company-1', orgId: 'org-1' },
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user-1', orgId: 'org-1' },
      });
      expect(mockDealRepository.create).toHaveBeenCalledWith({
        ...createDealDto,
        contact: mockContact,
        company: mockCompany,
        owner: mockUser,
        orgId: 'org-1',
      });
      expect(result).toEqual(mockDeal);
    });

    it('should throw NotFoundException when contact not found', async () => {
      mockContactRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createDealDto, 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should validate deal amount is positive', async () => {
      const invalidDto = { ...createDealDto, amount: -1000 };
      
      await expect(service.create(invalidDto, 'org-1')).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should validate probability is between 0 and 100', async () => {
      const invalidDto = { ...createDealDto, probability: 150 };
      
      await expect(service.create(invalidDto, 'org-1')).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('findAll', () => {
    const queryOptions = {
      page: 1,
      limit: 10,
      search: 'enterprise',
      stage: 'qualification',
      ownerId: 'user-1',
      sortBy: 'amount',
      sortOrder: 'DESC' as const,
    };

    it('should return paginated deals', async () => {
      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[mockDeal], 1]);

      const result = await service.findAll('org-1', queryOptions);

      expect(mockDealRepository.createQueryBuilder).toHaveBeenCalledWith('deal');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('deal.orgId = :orgId', { orgId: 'org-1' });
      expect(result).toEqual({
        deals: [mockDeal],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      });
    });

    it('should apply search filter', async () => {
      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll('org-1', queryOptions);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(deal.title ILIKE :search OR deal.description ILIKE :search)',
        { search: '%enterprise%' },
      );
    });

    it('should apply stage filter', async () => {
      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getManyAndCount.mockResolvedValue([[], 0]);

      await service.findAll('org-1', queryOptions);

      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'deal.stage = :stage',
        { stage: 'qualification' },
      );
    });
  });

  describe('updateStage', () => {
    it('should update deal stage successfully', async () => {
      const updatedDeal = { ...mockDeal, stage: 'proposal' };
      mockDealRepository.findOne.mockResolvedValue(mockDeal);
      mockDealRepository.save.mockResolvedValue(updatedDeal);

      const result = await service.updateStage('1', 'proposal', 'org-1');

      expect(mockDealRepository.findOne).toHaveBeenCalledWith({
        where: { id: '1', orgId: 'org-1' },
      });
      expect(mockDealRepository.save).toHaveBeenCalledWith({
        ...mockDeal,
        stage: 'proposal',
      });
      expect(result).toEqual(updatedDeal);
    });

    it('should throw NotFoundException when deal not found', async () => {
      mockDealRepository.findOne.mockResolvedValue(null);

      await expect(service.updateStage('999', 'proposal', 'org-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should validate stage transition', async () => {
      mockDealRepository.findOne.mockResolvedValue({ ...mockDeal, stage: 'closed-won' });

      await expect(service.updateStage('1', 'lead', 'org-1')).rejects.toThrow(
        BadRequestException,
      );
    });
  });

  describe('getPipeline', () => {
    it('should return pipeline data grouped by stage', async () => {
      const mockPipelineData = [
        { stage: 'lead', count: '5', totalValue: '100000' },
        { stage: 'qualification', count: '3', totalValue: '150000' },
        { stage: 'proposal', count: '2', totalValue: '80000' },
      ];

      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getRawMany.mockResolvedValue(mockPipelineData);

      const result = await service.getPipeline('org-1');

      expect(mockDealRepository.createQueryBuilder).toHaveBeenCalledWith('deal');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('deal.orgId = :orgId', { orgId: 'org-1' });
      expect(mockQueryBuilder.select).toHaveBeenCalledWith([
        'deal.stage as stage',
        'COUNT(deal.id) as count',
        'SUM(deal.amount) as totalValue',
      ]);
      expect(mockQueryBuilder.groupBy).toHaveBeenCalledWith('deal.stage');
      
      expect(result).toEqual({
        pipeline: [
          { stage: 'lead', count: 5, totalValue: 100000, deals: [] },
          { stage: 'qualification', count: 3, totalValue: 150000, deals: [] },
          { stage: 'proposal', count: 2, totalValue: 80000, deals: [] },
        ],
      });
    });
  });

  describe('getForecast', () => {
    it('should return revenue forecast', async () => {
      const mockDeals = [
        { amount: 50000, probability: 75, expectedCloseDate: new Date('2024-12-31') },
        { amount: 30000, probability: 50, expectedCloseDate: new Date('2024-11-30') },
      ];

      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getMany.mockResolvedValue(mockDeals);

      const result = await service.getForecast('org-1', { months: 3 });

      expect(result).toHaveProperty('totalPipeline');
      expect(result).toHaveProperty('weightedPipeline');
      expect(result).toHaveProperty('monthlyForecast');
      expect(result.totalPipeline).toBe(80000);
      expect(result.weightedPipeline).toBe(52500); // (50000 * 0.75) + (30000 * 0.5)
    });
  });

  describe('getStats', () => {
    it('should return deal statistics', async () => {
      const mockQueryBuilder = mockDealRepository.createQueryBuilder();
      mockQueryBuilder.getMany.mockResolvedValue([
        { stage: 'qualification', amount: 50000, createdAt: new Date() },
        { stage: 'proposal', amount: 30000, createdAt: new Date() },
      ]);

      // Mock additional queries for stats
      jest.spyOn(service as any, 'getTotalDeals').mockResolvedValue(25);
      jest.spyOn(service as any, 'getTotalRevenue').mockResolvedValue(500000);
      jest.spyOn(service as any, 'getAverageAmount').mockResolvedValue(20000);
      jest.spyOn(service as any, 'getConversionRate').mockResolvedValue(65);

      const result = await service.getStats('org-1');

      expect(result).toHaveProperty('totalDeals');
      expect(result).toHaveProperty('totalRevenue');
      expect(result).toHaveProperty('averageAmount');
      expect(result).toHaveProperty('conversionRate');
      expect(result).toHaveProperty('byStage');
    });
  });
});
