'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Fab,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
} from '@mui/icons-material';
import { ContactsTable } from '../../components/contacts/ContactsTable';
import { ContactsStats } from '../../components/contacts/ContactsStats';
import { ContactForm } from '../../components/contacts/ContactForm';
import { ContactFilters } from '../../components/contacts/ContactFilters';
import { AppLayout } from '../../components/layout/AppLayout';

export default function ContactsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    leadStatus: '',
    leadSource: '',
    assignedToId: '',
    tags: [] as string[],
  });
  const [showFilters, setShowFilters] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleCreateContact = () => {
    setSelectedContact(null);
    setShowContactForm(true);
  };

  const handleEditContact = (contact: any) => {
    setSelectedContact(contact);
    setShowContactForm(true);
  };

  const handleCloseForm = () => {
    setShowContactForm(false);
    setSelectedContact(null);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleExport = () => {
    handleMenuClose();
    // TODO: Implement export functionality
    console.log('Export contacts');
  };

  const handleImport = () => {
    handleMenuClose();
    // TODO: Implement import functionality
    console.log('Import contacts');
  };

  return (
    <AppLayout>
      <Container maxWidth="xl">
        {/* Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1" fontWeight={600}>
            Contacts
          </Typography>
          
          <Box display="flex" gap={2}>
            <IconButton onClick={handleMenuOpen}>
              <MoreVertIcon />
            </IconButton>
            
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreateContact}
            >
              Add Contact
            </Button>
          </Box>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleExport}>
              <DownloadIcon sx={{ mr: 1 }} />
              Export Contacts
            </MenuItem>
            <MenuItem onClick={handleImport}>
              <UploadIcon sx={{ mr: 1 }} />
              Import Contacts
            </MenuItem>
          </Menu>
        </Box>

        {/* Stats */}
        <Box mb={3}>
          <ContactsStats />
        </Box>

        {/* Search and Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  placeholder="Search contacts by name, email, or company..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box display="flex" gap={1} alignItems="center" flexWrap="wrap">
                  <Button
                    variant={showFilters ? 'contained' : 'outlined'}
                    startIcon={<FilterIcon />}
                    onClick={() => setShowFilters(!showFilters)}
                    size="small"
                  >
                    Filters
                  </Button>
                  
                  {/* Active Filters */}
                  {filters.leadStatus && (
                    <Chip
                      label={`Status: ${filters.leadStatus}`}
                      onDelete={() => setFilters({ ...filters, leadStatus: '' })}
                      size="small"
                    />
                  )}
                  
                  {filters.leadSource && (
                    <Chip
                      label={`Source: ${filters.leadSource}`}
                      onDelete={() => setFilters({ ...filters, leadSource: '' })}
                      size="small"
                    />
                  )}
                  
                  {filters.tags.map((tag) => (
                    <Chip
                      key={tag}
                      label={`Tag: ${tag}`}
                      onDelete={() => setFilters({
                        ...filters,
                        tags: filters.tags.filter(t => t !== tag)
                      })}
                      size="small"
                    />
                  ))}
                </Box>
              </Grid>
            </Grid>

            {/* Expandable Filters */}
            {showFilters && (
              <Box mt={2}>
                <ContactFilters
                  filters={filters}
                  onChange={handleFilterChange}
                />
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Contacts Table */}
        <ContactsTable
          searchQuery={searchQuery}
          filters={filters}
          onEditContact={handleEditContact}
        />

        {/* Floating Action Button for Mobile */}
        <Fab
          color="primary"
          aria-label="add contact"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            display: { xs: 'flex', md: 'none' },
          }}
          onClick={handleCreateContact}
        >
          <AddIcon />
        </Fab>

        {/* Contact Form Dialog */}
        {showContactForm && (
          <ContactForm
            open={showContactForm}
            contact={selectedContact}
            onClose={handleCloseForm}
          />
        )}
      </Container>
    </AppLayout>
  );
}
