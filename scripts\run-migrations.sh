#!/bin/bash

# OneCRM Database Migration Runner
# This script runs database migrations in the correct order

set -e

DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-onecrm}"
DB_USER="${DB_USER:-onecrm}"
DB_PASSWORD="${DB_PASSWORD:-onecrm_dev_password}"
MIGRATIONS_DIR="scripts/db/migrations"

echo "🗄️  Running OneCRM Database Migrations..."

# Function to check if PostgreSQL is available
check_postgres() {
    echo "🔍 Checking PostgreSQL connection..."
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        echo "✅ PostgreSQL connection successful"
        return 0
    else
        echo "❌ PostgreSQL connection failed"
        return 1
    fi
}

# Function to create migrations table
create_migrations_table() {
    echo "📋 Creating migrations tracking table..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" << 'EOF'
CREATE TABLE IF NOT EXISTS schema_migrations (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) UNIQUE NOT NULL,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    checksum VARCHAR(64)
);
EOF
    
    echo "✅ Migrations table ready"
}

# Function to calculate file checksum
calculate_checksum() {
    local file_path=$1
    if command -v sha256sum &> /dev/null; then
        sha256sum "$file_path" | cut -d' ' -f1
    elif command -v shasum &> /dev/null; then
        shasum -a 256 "$file_path" | cut -d' ' -f1
    else
        echo "unknown"
    fi
}

# Function to check if migration was already executed
is_migration_executed() {
    local migration_name=$1
    local count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM schema_migrations WHERE migration_name = '$migration_name';")
    [ "$count" -gt 0 ]
}

# Function to record migration execution
record_migration() {
    local migration_name=$1
    local checksum=$2
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        INSERT INTO schema_migrations (migration_name, checksum) 
        VALUES ('$migration_name', '$checksum')
        ON CONFLICT (migration_name) DO UPDATE SET 
            executed_at = NOW(),
            checksum = EXCLUDED.checksum;
    "
}

# Function to run a single migration
run_migration() {
    local migration_file=$1
    local migration_name=$(basename "$migration_file" .sql)
    local checksum=$(calculate_checksum "$migration_file")
    
    echo "🔄 Processing migration: $migration_name"
    
    if is_migration_executed "$migration_name"; then
        echo "⏭️  Migration $migration_name already executed, skipping..."
        return 0
    fi
    
    echo "▶️  Executing migration: $migration_name"
    
    # Run the migration
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$migration_file"; then
        # Record successful execution
        record_migration "$migration_name" "$checksum"
        echo "✅ Migration $migration_name completed successfully"
        return 0
    else
        echo "❌ Migration $migration_name failed"
        return 1
    fi
}

# Function to run all migrations
run_all_migrations() {
    echo "🚀 Running all migrations..."
    
    # Find all migration files and sort them
    local migration_files=($(find "$MIGRATIONS_DIR" -name "*.sql" | sort))
    
    if [ ${#migration_files[@]} -eq 0 ]; then
        echo "⚠️  No migration files found in $MIGRATIONS_DIR"
        return 0
    fi
    
    echo "📋 Found ${#migration_files[@]} migration files"
    
    # Run each migration
    for migration_file in "${migration_files[@]}"; do
        if ! run_migration "$migration_file"; then
            echo "❌ Migration failed, stopping execution"
            return 1
        fi
    done
    
    echo "✅ All migrations completed successfully"
}

# Function to show migration status
show_migration_status() {
    echo "📊 Migration Status:"
    echo "==================="
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            migration_name,
            executed_at,
            checksum
        FROM schema_migrations 
        ORDER BY executed_at;
    "
}

# Function to rollback last migration (basic implementation)
rollback_migration() {
    echo "⚠️  Rollback functionality is not implemented yet"
    echo "Please manually rollback the database changes if needed"
    return 1
}

# Function to validate database schema
validate_schema() {
    echo "🔍 Validating database schema..."
    
    # Check if all required tables exist
    local required_tables=("organizations" "users" "contacts" "companies" "deals" "activities" "organization_invitations" "organization_usage")
    
    for table in "${required_tables[@]}"; do
        local exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '$table');")
        
        if [[ "$exists" =~ "t" ]]; then
            echo "✅ Table $table exists"
        else
            echo "❌ Table $table is missing"
            return 1
        fi
    done
    
    # Check if RLS is enabled
    local rls_tables=("organizations" "users" "contacts" "companies" "deals")
    
    for table in "${rls_tables[@]}"; do
        local rls_enabled=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT relrowsecurity FROM pg_class WHERE relname = '$table';")
        
        if [[ "$rls_enabled" =~ "t" ]]; then
            echo "✅ RLS enabled on $table"
        else
            echo "⚠️  RLS not enabled on $table"
        fi
    done
    
    echo "✅ Schema validation completed"
}

# Function to test multi-tenancy
test_multi_tenancy() {
    echo "🧪 Testing multi-tenancy isolation..."
    
    # Test data isolation for each organization
    local orgs=("550e8400-e29b-41d4-a716-************" "550e8400-e29b-41d4-a716-************" "550e8400-e29b-41d4-a716-************")
    
    for org_id in "${orgs[@]}"; do
        echo "🔍 Testing isolation for org: $org_id"
        
        # Set user context for testing
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "
            SELECT set_user_context(
                '660e8400-e29b-41d4-a716-************'::UUID,
                '$org_id'::UUID,
                'admin',
                'development'
            );
        " > /dev/null
        
        # Count records for this org
        local contact_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM contacts WHERE org_id = '$org_id';")
        local company_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM companies WHERE org_id = '$org_id';")
        local deal_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM deals WHERE org_id = '$org_id';")
        
        echo "  📊 Contacts: $contact_count, Companies: $company_count, Deals: $deal_count"
    done
    
    # Clear user context
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "SELECT clear_user_context();" > /dev/null
    
    echo "✅ Multi-tenancy test completed"
}

# Function to show help
show_help() {
    echo "OneCRM Database Migration Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  migrate     Run all pending migrations (default)"
    echo "  status      Show migration status"
    echo "  validate    Validate database schema"
    echo "  test        Test multi-tenancy isolation"
    echo "  rollback    Rollback last migration (not implemented)"
    echo "  help        Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  DB_HOST     Database host (default: localhost)"
    echo "  DB_PORT     Database port (default: 5432)"
    echo "  DB_NAME     Database name (default: onecrm)"
    echo "  DB_USER     Database user (default: onecrm)"
    echo "  DB_PASSWORD Database password (default: onecrm_dev_password)"
    echo ""
}

# Main execution
main() {
    local command="${1:-migrate}"
    
    echo "🔧 Configuration:"
    echo "- Host: $DB_HOST:$DB_PORT"
    echo "- Database: $DB_NAME"
    echo "- User: $DB_USER"
    echo ""
    
    # Check PostgreSQL connection
    if ! check_postgres; then
        echo "❌ Cannot connect to PostgreSQL. Please check your configuration."
        exit 1
    fi
    
    # Create migrations table
    create_migrations_table
    
    case $command in
        "migrate")
            run_all_migrations
            validate_schema
            ;;
        "status")
            show_migration_status
            ;;
        "validate")
            validate_schema
            ;;
        "test")
            test_multi_tenancy
            ;;
        "rollback")
            rollback_migration
            ;;
        "help")
            show_help
            ;;
        *)
            echo "❌ Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
