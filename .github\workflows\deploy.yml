name: Deploy to Staging/Production

on:
  push:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io

jobs:
  determine-environment:
    name: Determine Deployment Environment
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
    steps:
      - name: Determine environment
        id: env
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "environment=staging" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi

  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: determine-environment
    permissions:
      contents: read
      packages: write
    strategy:
      matrix:
        service: [frontend, crm-api, auth-service, tenant-service]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/${{ matrix.service }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/${{ matrix.service }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-kong-config:
    name: Deploy Kong Configuration
    runs-on: ubuntu-latest
    needs: [determine-environment, build-and-push]
    environment: ${{ needs.determine-environment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup decK
        run: |
          curl -sL https://github.com/Kong/deck/releases/latest/download/deck_linux_amd64.tar.gz -o deck.tar.gz
          tar -xf deck.tar.gz
          sudo mv deck /usr/local/bin/

      - name: Deploy Kong configuration
        run: |
          # Replace with actual Kong admin URL for the environment
          KONG_ADMIN_URL="${{ secrets.KONG_ADMIN_URL }}"
          deck sync --kong-addr $KONG_ADMIN_URL --state kong/config/kong.yml
        env:
          KONG_ADMIN_URL: ${{ secrets.KONG_ADMIN_URL }}

  deploy-application:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [determine-environment, build-and-push, deploy-kong-config]
    environment: ${{ needs.determine-environment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to Kubernetes
        run: |
          # This would contain actual deployment commands
          echo "Deploying to ${{ needs.determine-environment.outputs.environment }}"
          # Example: kubectl apply -f k8s/${{ needs.determine-environment.outputs.environment }}/
        env:
          ENVIRONMENT: ${{ needs.determine-environment.outputs.environment }}

  health-check:
    name: Post-Deployment Health Check
    runs-on: ubuntu-latest
    needs: [determine-environment, deploy-application]
    steps:
      - name: Wait for deployment
        run: sleep 30

      - name: Health check
        run: |
          # Replace with actual health check endpoints
          curl -f ${{ secrets.APP_URL }}/api/health || exit 1
          echo "Health check passed"

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [determine-environment, deploy-application, health-check]
    if: always()
    steps:
      - name: Notify success
        if: success()
        run: |
          echo "Deployment to ${{ needs.determine-environment.outputs.environment }} successful"
          # Add Slack/Teams notification here

      - name: Notify failure
        if: failure()
        run: |
          echo "Deployment to ${{ needs.determine-environment.outputs.environment }} failed"
          # Add Slack/Teams notification here
