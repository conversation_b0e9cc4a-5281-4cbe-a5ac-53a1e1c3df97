'use client';

import React from 'react';
import {
  Box,
  CircularProgress,
  Skeleton,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Backdrop,
} from '@mui/material';

interface LoadingSpinnerProps {
  size?: number;
  color?: 'primary' | 'secondary' | 'inherit';
  message?: string;
}

interface PageLoadingProps {
  message?: string;
}

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
}

interface CardSkeletonProps {
  title?: boolean;
  content?: boolean;
  actions?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 40,
  color = 'primary',
  message,
}) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        p: 3,
      }}
    >
      <CircularProgress size={size} color={color} />
      {message && (
        <Typography variant="body2" color="text.secondary">
          {message}
        </Typography>
      )}
    </Box>
  );
};

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = 'Loading...',
}) => {
  return (
    <Backdrop
      sx={{
        color: '#fff',
        zIndex: (theme) => theme.zIndex.drawer + 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
      }}
      open={true}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <CircularProgress color="inherit" size={60} />
        <Typography variant="h6" color="inherit">
          {message}
        </Typography>
      </Box>
    </Backdrop>
  );
};

export const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
}) => {
  return (
    <Box sx={{ width: '100%' }}>
      {/* Header skeleton */}
      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton
            key={`header-${index}`}
            variant="rectangular"
            height={40}
            sx={{ flex: 1 }}
          />
        ))}
      </Box>

      {/* Rows skeleton */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <Box key={`row-${rowIndex}`} sx={{ display: 'flex', gap: 2, mb: 1 }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton
              key={`cell-${rowIndex}-${colIndex}`}
              variant="rectangular"
              height={32}
              sx={{ flex: 1 }}
            />
          ))}
        </Box>
      ))}
    </Box>
  );
};

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  title = true,
  content = true,
  actions = false,
}) => {
  return (
    <Card>
      <CardContent>
        {title && (
          <Skeleton
            variant="text"
            height={32}
            width="60%"
            sx={{ mb: 2 }}
          />
        )}

        {content && (
          <>
            <Skeleton variant="text" height={20} sx={{ mb: 1 }} />
            <Skeleton variant="text" height={20} width="80%" sx={{ mb: 1 }} />
            <Skeleton variant="text" height={20} width="90%" sx={{ mb: 2 }} />
          </>
        )}

        {actions && (
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <Skeleton variant="rectangular" width={80} height={36} />
            <Skeleton variant="rectangular" width={80} height={36} />
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export const DashboardSkeleton: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Skeleton variant="text" height={48} width="40%" sx={{ mb: 3 }} />

      {/* Stats cards */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 2, mb: 3 }}>
        {Array.from({ length: 4 }).map((_, index) => (
          <CardSkeleton key={`stat-${index}`} title={true} content={false} />
        ))}
      </Box>

      {/* Charts */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: 2, mb: 3 }}>
        <Card>
          <CardContent>
            <Skeleton variant="text" height={32} width="50%" sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" height={300} />
          </CardContent>
        </Card>
        <Card>
          <CardContent>
            <Skeleton variant="text" height={32} width="50%" sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" height={300} />
          </CardContent>
        </Card>
      </Box>

      {/* Table */}
      <Card>
        <CardContent>
          <Skeleton variant="text" height={32} width="30%" sx={{ mb: 2 }} />
          <TableSkeleton rows={8} columns={5} />
        </CardContent>
      </Card>
    </Box>
  );
};

export const FormSkeleton: React.FC = () => {
  return (
    <Card>
      <CardContent>
        <Skeleton variant="text" height={32} width="40%" sx={{ mb: 3 }} />
        
        {/* Form fields */}
        {Array.from({ length: 6 }).map((_, index) => (
          <Box key={`field-${index}`} sx={{ mb: 2 }}>
            <Skeleton variant="text" height={20} width="20%" sx={{ mb: 1 }} />
            <Skeleton variant="rectangular" height={56} />
          </Box>
        ))}

        {/* Actions */}
        <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
          <Skeleton variant="rectangular" width={100} height={40} />
          <Skeleton variant="rectangular" width={80} height={40} />
        </Box>
      </CardContent>
    </Card>
  );
};

export const ProgressLoader: React.FC<{
  progress: number;
  message?: string;
}> = ({ progress, message }) => {
  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
        {message || 'Loading...'}
      </Typography>
      <LinearProgress
        variant="determinate"
        value={progress}
        sx={{ height: 8, borderRadius: 4 }}
      />
      <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
        {Math.round(progress)}%
      </Typography>
    </Box>
  );
};

// Higher-order component for loading states
export const withLoading = <P extends object>(
  Component: React.ComponentType<P>,
  LoadingComponent: React.ComponentType = LoadingSpinner
) => {
  return (props: P & { loading?: boolean }) => {
    const { loading, ...componentProps } = props;
    
    if (loading) {
      return <LoadingComponent />;
    }
    
    return <Component {...(componentProps as P)} />;
  };
};
