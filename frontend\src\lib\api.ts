import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import getKeycloak from './keycloak';
import { extractErrorMessage, retryWithBackoff } from '@onecrm/utils';

// API configuration - Kong Gateway URL for microservice architecture
const KONG_GATEWAY_URL = process.env.NEXT_PUBLIC_KONG_GATEWAY_URL || 'http://localhost:8000';
const API_BASE_URL = KONG_GATEWAY_URL;

// Create axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add authentication token and Kong-specific headers
axiosInstance.interceptors.request.use(
  async (config) => {
    const keycloakInstance = getKeycloak();

    // Add authentication token if available
    if (keycloakInstance?.authenticated && keycloakInstance.token) {
      config.headers.Authorization = `Bearer ${keycloakInstance.token}`;
    }

    // Add organization ID header if available (for multi-tenancy)
    if (keycloakInstance?.tokenParsed?.org_id) {
      config.headers['X-Org-Id'] = keycloakInstance.tokenParsed.org_id;
    }

    // Add Kong-specific headers
    config.headers['X-Kong-Request-ID'] = crypto.randomUUID();
    config.headers['X-Client-Version'] = process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0';
    config.headers['X-Client-Platform'] = 'web';

    // Add request timestamp for debugging
    if (process.env.NODE_ENV === 'development') {
      config.headers['X-Request-Time'] = new Date().toISOString();
      config.headers['X-Debug-Mode'] = 'true';
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors and Kong responses
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log Kong-specific headers in development
    if (process.env.NODE_ENV === 'development') {
      const kongHeaders = {
        'X-Kong-Upstream-Latency': response.headers['x-kong-upstream-latency'],
        'X-Kong-Proxy-Latency': response.headers['x-kong-proxy-latency'],
        'X-Kong-Request-ID': response.headers['x-kong-request-id'],
      };
      console.debug('Kong Gateway Response Headers:', kongHeaders);
    }

    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh the token
        const keycloakInstance = getKeycloak();

        if (!keycloakInstance) {
          // Keycloak not available, redirect to login page
          window.location.href = '/login';
          return Promise.reject(error);
        }

        const refreshed = await keycloakInstance.updateToken(30);
        
        if (refreshed && keycloakInstance.token) {
          // Update the authorization header and retry the request
          originalRequest.headers.Authorization = `Bearer ${keycloakInstance.token}`;
          return axiosInstance(originalRequest);
        } else {
          // Token refresh failed, redirect to login
          await keycloakInstance.login();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Token refresh failed, redirect to login
        const keycloakInstance = getKeycloak();
        if (keycloakInstance) {
          await keycloakInstance.login();
        } else {
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    }

    // Handle 403 Forbidden errors
    if (error.response?.status === 403) {
      console.error('Access forbidden:', error.response.data);
      // Optionally show a user-friendly error message
    }

    // Handle Kong Gateway specific errors
    if (error.response?.status === 502) {
      console.error('Kong Gateway: Bad Gateway - Service unavailable');
      // Service is down or unreachable through Kong
    }

    if (error.response?.status === 503) {
      console.error('Kong Gateway: Service Temporarily Unavailable');
      // Rate limiting or service overload
    }

    if (error.response?.status === 504) {
      console.error('Kong Gateway: Gateway Timeout');
      // Upstream service timeout
    }

    // Handle network errors
    if (!error.response) {
      console.error('Network error:', error.message);
      // Could be Kong Gateway unreachable or network issues
    }

    return Promise.reject(error);
  }
);

// API client wrapper with typed methods
export class ApiClient {
  private client: AxiosInstance;

  constructor(client: AxiosInstance) {
    this.client = client;
  }

  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // Upload file with progress tracking
  async uploadFile<T = any>(
    url: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<AxiosResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    return this.client.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
  }

  // Download file
  async downloadFile(url: string, filename?: string): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }
}

// Export the configured API client
export const apiClient = new ApiClient(axiosInstance);

// Export the raw axios instance for advanced usage
export { axiosInstance };

// Export API endpoints from shared types
export { API_ENDPOINTS } from '@onecrm/types';
