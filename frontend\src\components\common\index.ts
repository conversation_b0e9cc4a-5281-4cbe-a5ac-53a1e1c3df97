// Schema-based components
export {
  SchemaForm,
  ContactSchema,
  CompanySchema,
  DealSchema,
  ContactUISchema,
  CompanyUISchema,
  DealUISchema,
} from './SchemaForm';

// Data table components
export {
  DataTable,
  createColumnHelper,
} from './DataTable';

// Chart components
export {
  CustomLineChart,
  CustomAreaChart,
  CustomBarChart,
  CustomPieChart,
  formatChartData,
  CRMChartConfigs,
} from './ChartComponents';

// Loading components
export {
  LoadingSpinner,
  PageLoading,
  TableSkeleton,
  CardSkeleton,
  DashboardSkeleton,
  FormSkeleton,
  ProgressLoader,
  withLoading,
} from './LoadingComponents';

// Error boundary components
export {
  ErrorBoundary,
  ErrorBoundaryWrapper,
  SimpleErrorFallback,
  MinimalErrorFallback,
} from './ErrorBoundary';

// Notification system
export {
  NotificationProvider,
  useNotifications,
  withErrorNotification,
  NotificationUtils,
} from './NotificationSystem';

// Existing components
export { ResponsiveCard } from './ResponsiveCard';
