'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Avatar,
  LinearProgress,
} from '@mui/material';
import {
  MoreVert,
  Edit,
  Delete,
  Visibility,
  TrendingUp,
  AttachMoney,
} from '@mui/icons-material';

interface Deal {
  id: string;
  title: string;
  value: number;
  stage: string;
  probability: number;
  expectedCloseDate: string;
  contactName?: string;
  companyName?: string;
  assignedTo?: string;
  createdAt: string;
}

interface DealsTableProps {
  deals?: Deal[];
  searchQuery?: string;
  filters?: any;
  onEdit?: (deal: Deal) => void;
  onEditDeal?: (deal: Deal) => void;
  onDelete?: (dealId: string) => void;
  onView?: (deal: Deal) => void;
  loading?: boolean;
}

const stageColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
  'Prospecting': 'info',
  'Qualification': 'primary',
  'Proposal': 'warning',
  'Negotiation': 'secondary',
  'Closed Won': 'success',
  'Closed Lost': 'error',
};

export const DealsTable: React.FC<DealsTableProps> = ({
  deals = [],
  searchQuery,
  filters,
  onEdit,
  onEditDeal,
  onDelete,
  onView,
  loading = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, deal: Deal) => {
    setAnchorEl(event.currentTarget);
    setSelectedDeal(deal);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedDeal(null);
  };

  const handleEdit = () => {
    if (selectedDeal) {
      if (onEdit) onEdit(selectedDeal);
      if (onEditDeal) onEditDeal(selectedDeal);
    }
    handleMenuClose();
  };

  const handleDelete = () => {
    if (selectedDeal && onDelete) {
      onDelete(selectedDeal.id);
    }
    handleMenuClose();
  };

  const handleView = () => {
    if (selectedDeal && onView) {
      onView(selectedDeal);
    }
    handleMenuClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getProbabilityColor = (probability: number) => {
    if (probability >= 80) return 'success';
    if (probability >= 60) return 'warning';
    if (probability >= 40) return 'info';
    return 'error';
  };

  if (loading) {
    return (
      <Paper>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography>Loading deals...</Typography>
        </Box>
      </Paper>
    );
  }

  if (deals.length === 0) {
    return (
      <Paper>
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography color="text.secondary">
            No deals found. Create your first deal to get started.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Deal</TableCell>
            <TableCell>Value</TableCell>
            <TableCell>Stage</TableCell>
            <TableCell>Probability</TableCell>
            <TableCell>Expected Close</TableCell>
            <TableCell>Contact/Company</TableCell>
            <TableCell>Assigned To</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {deals.map((deal) => (
            <TableRow key={deal.id} hover>
              <TableCell>
                <Box>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {deal.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Created {formatDate(deal.createdAt)}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AttachMoney fontSize="small" color="success" />
                  <Typography variant="body2" fontWeight="bold">
                    {formatCurrency(deal.value)}
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Chip
                  label={deal.stage}
                  color={stageColors[deal.stage] || 'default'}
                  size="small"
                />
              </TableCell>
              
              <TableCell>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 120 }}>
                  <LinearProgress
                    variant="determinate"
                    value={deal.probability}
                    color={getProbabilityColor(deal.probability)}
                    sx={{ flex: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="caption" fontWeight="bold">
                    {deal.probability}%
                  </Typography>
                </Box>
              </TableCell>
              
              <TableCell>
                <Typography variant="body2">
                  {formatDate(deal.expectedCloseDate)}
                </Typography>
              </TableCell>
              
              <TableCell>
                <Box>
                  {deal.contactName && (
                    <Typography variant="body2" fontWeight="bold">
                      {deal.contactName}
                    </Typography>
                  )}
                  {deal.companyName && (
                    <Typography variant="caption" color="text.secondary">
                      {deal.companyName}
                    </Typography>
                  )}
                </Box>
              </TableCell>
              
              <TableCell>
                {deal.assignedTo && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar sx={{ width: 24, height: 24, fontSize: '0.75rem' }}>
                      {deal.assignedTo.charAt(0).toUpperCase()}
                    </Avatar>
                    <Typography variant="body2">
                      {deal.assignedTo}
                    </Typography>
                  </Box>
                )}
              </TableCell>
              
              <TableCell align="right">
                <IconButton
                  size="small"
                  onClick={(e) => handleMenuOpen(e, deal)}
                >
                  <MoreVert />
                </IconButton>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {onView && (
          <MenuItem onClick={handleView}>
            <Visibility fontSize="small" sx={{ mr: 1 }} />
            View Details
          </MenuItem>
        )}
        {onEdit && (
          <MenuItem onClick={handleEdit}>
            <Edit fontSize="small" sx={{ mr: 1 }} />
            Edit Deal
          </MenuItem>
        )}
        {onDelete && (
          <MenuItem onClick={handleDelete} sx={{ color: 'error.main' }}>
            <Delete fontSize="small" sx={{ mr: 1 }} />
            Delete Deal
          </MenuItem>
        )}
      </Menu>
    </TableContainer>
  );
};
