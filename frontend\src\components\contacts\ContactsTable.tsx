'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Avatar,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Skeleton,
  Checkbox,
  Tooltip,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface Contact {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  company?: string;
  title?: string;
  leadStatus?: string;
  leadSource?: string;
  tags?: string[];
  assignedTo?: {
    firstName: string;
    lastName: string;
  };
  createdAt: string;
}

interface ContactsTableProps {
  searchQuery: string;
  filters: any;
  onEditContact: (contact: Contact) => void;
}

const ContactRow: React.FC<{
  contact: Contact;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: (contact: Contact) => void;
}> = ({ contact, isSelected, onSelect, onEdit }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(contact);
  };

  const handleDelete = () => {
    handleMenuClose();
    // TODO: Implement delete functionality
    console.log('Delete contact:', contact.id);
  };

  const getInitials = () => {
    const first = contact.firstName?.[0] || '';
    const last = contact.lastName?.[0] || '';
    return (first + last).toUpperCase() || '?';
  };

  const getFullName = () => {
    return `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unnamed Contact';
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'qualified':
        return 'success';
      case 'unqualified':
        return 'error';
      case 'new':
        return 'info';
      case 'contacted':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <TableRow hover selected={isSelected}>
      <TableCell padding="checkbox">
        <Checkbox
          checked={isSelected}
          onChange={() => onSelect(contact.id)}
        />
      </TableCell>
      
      <TableCell>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            {getInitials()}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              {getFullName()}
            </Typography>
            {contact.title && (
              <Typography variant="caption" color="text.secondary">
                {contact.title}
              </Typography>
            )}
          </Box>
        </Box>
      </TableCell>
      
      <TableCell>
        <Box display="flex" alignItems="center" gap={1}>
          {contact.email && (
            <Tooltip title={`Email: ${contact.email}`}>
              <IconButton size="small" href={`mailto:${contact.email}`}>
                <EmailIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {contact.phone && (
            <Tooltip title={`Phone: ${contact.phone}`}>
              <IconButton size="small" href={`tel:${contact.phone}`}>
                <PhoneIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          <Box>
            {contact.email && (
              <Typography variant="body2">{contact.email}</Typography>
            )}
            {contact.phone && (
              <Typography variant="caption" color="text.secondary">
                {contact.phone}
              </Typography>
            )}
          </Box>
        </Box>
      </TableCell>
      
      <TableCell>
        {contact.company && (
          <Box display="flex" alignItems="center" gap={1}>
            <BusinessIcon fontSize="small" color="action" />
            <Typography variant="body2">{contact.company}</Typography>
          </Box>
        )}
      </TableCell>
      
      <TableCell>
        {contact.leadStatus && (
          <Chip
            label={contact.leadStatus}
            size="small"
            color={getStatusColor(contact.leadStatus) as any}
            variant="outlined"
          />
        )}
      </TableCell>
      
      <TableCell>
        {contact.leadSource && (
          <Typography variant="body2" color="text.secondary">
            {contact.leadSource}
          </Typography>
        )}
      </TableCell>
      
      <TableCell>
        <Box display="flex" gap={0.5} flexWrap="wrap">
          {contact.tags?.slice(0, 2).map((tag) => (
            <Chip
              key={tag}
              label={tag}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          ))}
          {contact.tags && contact.tags.length > 2 && (
            <Chip
              label={`+${contact.tags.length - 2}`}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
        </Box>
      </TableCell>
      
      <TableCell>
        {contact.assignedTo && (
          <Tooltip title={`${contact.assignedTo.firstName} ${contact.assignedTo.lastName}`}>
            <Avatar sx={{ width: 32, height: 32, fontSize: '0.8rem' }}>
              {contact.assignedTo.firstName[0]}{contact.assignedTo.lastName[0]}
            </Avatar>
          </Tooltip>
        )}
      </TableCell>
      
      <TableCell>
        <IconButton onClick={handleMenuOpen}>
          <MoreVertIcon />
        </IconButton>
        
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEdit}>
            <EditIcon sx={{ mr: 1 }} fontSize="small" />
            Edit
          </MenuItem>
          <MenuItem onClick={handleDelete}>
            <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        </Menu>
      </TableCell>
    </TableRow>
  );
};

export const ContactsTable: React.FC<ContactsTableProps> = ({
  searchQuery,
  filters,
  onEditContact,
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);

  // Build query parameters
  const queryParams = new URLSearchParams({
    page: (page + 1).toString(),
    limit: rowsPerPage.toString(),
    ...(searchQuery && { search: searchQuery }),
    ...(filters.leadStatus && { leadStatus: filters.leadStatus }),
    ...(filters.leadSource && { leadSource: filters.leadSource }),
    ...(filters.assignedToId && { assignedToId: filters.assignedToId }),
    ...(filters.tags.length > 0 && { tags: filters.tags.join(',') }),
  });

  const { data, isLoading } = useSWR(`/api/contacts?${queryParams}`);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = data?.contacts?.map((contact: Contact) => contact.id) || [];
      setSelectedContacts(newSelected);
    } else {
      setSelectedContacts([]);
    }
  };

  const handleSelectContact = (id: string) => {
    const selectedIndex = selectedContacts.indexOf(id);
    let newSelected: string[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedContacts, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedContacts.slice(1));
    } else if (selectedIndex === selectedContacts.length - 1) {
      newSelected = newSelected.concat(selectedContacts.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedContacts.slice(0, selectedIndex),
        selectedContacts.slice(selectedIndex + 1),
      );
    }

    setSelectedContacts(newSelected);
  };

  const isSelected = (id: string) => selectedContacts.indexOf(id) !== -1;

  if (isLoading) {
    return (
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                {Array.from({ length: 9 }).map((_, index) => (
                  <TableCell key={index}>
                    <Skeleton variant="text" />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.from({ length: 10 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 9 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedContacts.length > 0 && selectedContacts.length < (data?.contacts?.length || 0)}
                  checked={data?.contacts?.length > 0 && selectedContacts.length === data?.contacts?.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Contact Info</TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Source</TableCell>
              <TableCell>Tags</TableCell>
              <TableCell>Assigned To</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.contacts?.map((contact: Contact) => (
              <ContactRow
                key={contact.id}
                contact={contact}
                isSelected={isSelected(contact.id)}
                onSelect={handleSelectContact}
                onEdit={onEditContact}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={data?.total || 0}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};
