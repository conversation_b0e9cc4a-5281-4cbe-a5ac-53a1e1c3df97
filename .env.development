# OneCRM Development Environment

# Application
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=onecrm
DB_PASSWORD=onecrm_dev_password
DB_NAME=onecrm

# Keycloak Configuration
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=dev-client-secret

# Kong Gateway
KONG_ADMIN_URL=http://localhost:8001
API_BASE_URL=http://localhost:8000

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=dev-jwt-secret-key-change-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=8h

# Email Configuration (disabled in dev)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=debug

# Feature Flags
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_RATE_LIMITING=false

# Monitoring
PROMETHEUS_ENABLED=true
HEALTH_CHECK_ENABLED=true
