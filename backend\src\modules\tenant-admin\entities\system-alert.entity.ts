import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum AlertCategory {
  SYSTEM = 'system',
  SECURITY = 'security',
  PERFORMANCE = 'performance',
  BILLING = 'billing',
  USAGE = 'usage',
  TENANT = 'tenant',
  INTEGRATION = 'integration',
}

export enum AlertStatus {
  ACTIVE = 'active',
  ACKNOWLEDGED = 'acknowledged',
  RESOLVED = 'resolved',
  SUPPRESSED = 'suppressed',
}

@Entity('system_alerts')
@Index(['severity', 'status', 'createdAt'])
@Index(['category', 'status'])
@Index(['organizationId', 'status'])
export class SystemAlert {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: AlertSeverity,
  })
  severity: AlertSeverity;

  @Column({
    type: 'enum',
    enum: AlertCategory,
  })
  category: AlertCategory;

  @Column({
    type: 'enum',
    enum: AlertStatus,
    default: AlertStatus.ACTIVE,
  })
  status: AlertStatus;

  @Column({ name: 'organization_id', nullable: true })
  organizationId: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'affected_service' })
  affectedService: string;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'alert_source' })
  alertSource: string;

  @Column({ type: 'json', nullable: true })
  metadata: {
    metricName?: string;
    threshold?: number;
    currentValue?: number;
    endpoint?: string;
    errorCode?: string;
    stackTrace?: string;
    additionalData?: Record<string, any>;
  };

  @Column({ type: 'json', nullable: true })
  conditions: {
    triggerCondition?: string;
    resolveCondition?: string;
    suppressionRules?: string[];
  };

  @Column({ type: 'timestamp', nullable: true, name: 'first_occurred_at' })
  firstOccurredAt: Date;

  @Column({ type: 'timestamp', nullable: true, name: 'last_occurred_at' })
  lastOccurredAt: Date;

  @Column({ type: 'int', default: 1, name: 'occurrence_count' })
  occurrenceCount: number;

  @Column({ type: 'timestamp', nullable: true, name: 'acknowledged_at' })
  acknowledgedAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'acknowledged_by' })
  acknowledgedBy: string;

  @Column({ type: 'timestamp', nullable: true, name: 'resolved_at' })
  resolvedAt: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'resolved_by' })
  resolvedBy: string;

  @Column({ type: 'text', nullable: true, name: 'resolution_notes' })
  resolutionNotes: string;

  @Column({ type: 'timestamp', nullable: true, name: 'suppressed_until' })
  suppressedUntil: Date;

  @Column({ type: 'varchar', length: 255, nullable: true, name: 'suppressed_by' })
  suppressedBy: string;

  @Column({ type: 'json', nullable: true, name: 'notification_channels' })
  notificationChannels: {
    email?: string[];
    slack?: string[];
    webhook?: string[];
    sms?: string[];
  };

  @Column({ type: 'boolean', default: false, name: 'auto_resolve' })
  autoResolve: boolean;

  @Column({ type: 'int', nullable: true, name: 'auto_resolve_timeout_minutes' })
  autoResolveTimeoutMinutes: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Computed properties
  get isActive(): boolean {
    return this.status === AlertStatus.ACTIVE;
  }

  get isCritical(): boolean {
    return this.severity === AlertSeverity.CRITICAL;
  }

  get durationMinutes(): number {
    const endTime = this.resolvedAt || new Date();
    const startTime = this.firstOccurredAt || this.createdAt;
    return Math.round((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }

  get isSuppressed(): boolean {
    return this.status === AlertStatus.SUPPRESSED && 
           this.suppressedUntil && 
           new Date() < this.suppressedUntil;
  }

  get shouldAutoResolve(): boolean {
    if (!this.autoResolve || !this.autoResolveTimeoutMinutes) return false;
    if (this.status !== AlertStatus.ACTIVE) return false;
    
    const timeoutMs = this.autoResolveTimeoutMinutes * 60 * 1000;
    const cutoffTime = new Date(this.createdAt.getTime() + timeoutMs);
    return new Date() > cutoffTime;
  }
}
