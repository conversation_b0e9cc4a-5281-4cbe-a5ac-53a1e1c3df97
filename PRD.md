# **oneCRM (Improved) Plan, PRD, Developer’s Guide**

# **CRM App Product Requirement Document (PRD)**

## **1\. Background & Rationale**

Twenty is a modern, open‑source CRM (Customer Relationship Management) system written in a TypeScript monorepo (Nx) that bundles a React/Next.js front‑end with a Node.js/NestJS back‑end on PostgreSQL [innovirtuoso.com](https://innovirtuoso.com/open-source-software-2/twenty-the-ultimate-open-source-crm-for-self-hosting-and-local-setup/#:~:text=,end%20with%20internationalization). It was created as an alternative to expensive, proprietary CRMs, emphasizing customization and community-powered development [twenty.com](https://twenty.com/#:~:text=of%20Open%20Source). However, Twenty’s built‑in authentication is a custom implementation, and to align with our platform standards for a Software-as-a-Service (SaaS) offering, we need to **replace the proprietary auth with a standard SSO** (Single Sign-On) solution and add an API gateway layer for robust security. Specifically, we will **fork and adapt Twenty’s codebase** to integrate **Keycloak** for identity management and **Kong Gateway** for API management, enabling multi-tenant SaaS capabilities (multiple organizations on a single instance) while preserving all existing CRM features (contacts, deals, activities, etc.). This PRD defines the phased approach to carefully refactor Twenty’s architecture — adopting our development guidelines and branding — **without regressing current features**, and adding the necessary **SaaS platform components** such as tenant isolation, centralized auth, and subscription management to launch an MVP (Minimum Viable Product) for our SaaS.

## **2\. Goals**

1. **Single Sign‑On (SSO):** Replace Twenty’s local authentication with **Keycloak** using the OpenID Connect Authorization Code flow (with PKCE) for secure SSO across our suite [keycloak.org](https://www.keycloak.org/docs/25.0.6/securing_apps/index.html#:~:text=As%20an%20OAuth2%2C%20OpenID%20Connect%2C,looking%20at%20Server%20Administration%20Guide). Users will authenticate once via Keycloak and gain access to the CRM without separate login. Keycloak will serve as the central Identity Provider (IdP) for all tenants, managing user credentials, roles, and sessions.

2. **API Edge Security:** Front all backend API calls with **Kong Gateway** using its OpenID Connect (OIDC) plugin. Kong will validate JWT access tokens on each request (signature and expiry) [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=kong,Cryptographic%20signature%20validation%2C%20expiry%20check) and enforce scope/role-based access control via the OIDC plugin’s configuration (e.g. required scopes/claims) [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=1,roles_required). This ensures robust, centralized authz/authn at the gateway level, offloading security from the app. The gateway will also enable future rate-limiting, request transformation, and logging globally.

3. **GitOps for Config:** Manage Kong configuration declaratively using **Kong’s decK** CLI and a GitOps workflow. All gateway routes, services, and plugins will be defined in YAML and stored in Git; changes will go through pull requests and automated deployment via CI (GitHub Actions) [konghq.com](https://konghq.com/blog/engineering/gitops-for-kong-managing-kong-declaratively-with-deck-and-github-actions#:~:text=Kong%27s%20decK%20provides%20a%20command,the%20application%20of%20the%20configuration). This provides version control and auditability for edge configurations and allows rapid, consistent promotion of config changes across environments.

4. **Maintainable Codebase:** Refactor and modernize the codebase safely using automated **codemods** and other tools. By leveraging scripts (e.g. jscodeshift, ast-grep, ts-migrate), we will perform bulk transformations (such as removing legacy auth code) across the repository with minimal manual effort [martinfowler.com](https://martinfowler.com/articles/codemods-api-refactoring.html#:~:text=By%20automating%20refactoring%20tasks%2C%20codemods,to%20manually%20modify%20every%20file). The goal is to improve code quality (consistent patterns, proper typing) without altering functionality. The Nx monorepo structure will be retained and enhanced for improved developer experience.

5. **License Compliance:** Adhere to Twenty’s open-source license (GPL v3). All modifications to the forked core will remain open source [twenty.com](https://twenty.com/#:~:text=of%20Open%20Source), and any proprietary extensions we add in the future will be isolated as separate services to avoid contaminating the GPL code. We will ensure we fulfill obligations under GPL/AGPL (for example, sharing source code of the core to customers if network access is provided) to legally operate the SaaS.

6. **Multi‑Tenancy & SaaS Enablement:** Introduce multi-tenant architecture so that many organizations (tenants) can securely share the system. Each user will belong to an **organization (tenant)**, and all data operations will be scoped by `org_id` to prevent cross-tenant data leakage. We will implement necessary SaaS features: self-service tenant onboarding (sign-up), tenant-specific administration (user invites, role management), and usage metering & billing support. Strong tenant isolation at every layer (auth token, application logic, and database) is paramount [crunchydata.com](https://www.crunchydata.com/blog/row-level-security-for-tenants-in-postgres#:~:text=Postgres%20Row,their%20own%20data%2C%20enhancing%20the) to ensure data privacy. This prepares the product for a commercial SaaS launch where each customer has an isolated workspace within the shared system.

*(Success will be measured by achieving the above goals without regressing existing CRM capabilities. Users should experience the refactored app as a branded, secure SaaS product identical in features to Twenty.)*

## **3\. Scope**

### **3.1 In‑Scope**

* **Fork & Codebase Baseline:** Fork the Twenty repo and set up our own repository while preserving the upstream remote for potential future merges. Initial setup of the Nx workspace, build, and tests is included.

* **SSO Integration:** Strip out or disable Twenty’s built-in username/password auth. Integrate Keycloak for authentication and authorization: all login/logout flows redirected to Keycloak, and all API calls will require a valid Keycloak-issued token. This includes front-end changes to use Keycloak OIDC and back-end changes to verify tokens and handle user identities via Keycloak.

* **API Gateway Deployment:** Deploy Kong Gateway (OSS or Kong EE in DB-less mode for MVP) in front of the NestJS API. Configure OIDC plugin with Keycloak as the IdP, so that Kong will authenticate requests. Define Kong services/routes for the CRM APIs and apply global plugins (OIDC, rate limiting, request transformer, logging).

* **GitOps Config Management:** Use **decK** to manage Kong config. Store the declarative config files (YAML) in the repository (`/kong/config/` directory). Set up automation (CI pipeline using decK commands) to apply config to Kong on updates, and to detect configuration drift [konghq.com](https://konghq.com/blog/engineering/gitops-for-kong-managing-kong-declaratively-with-deck-and-github-actions#:~:text=Kong%27s%20decK%20provides%20a%20command,the%20application%20of%20the%20configuration).

* **Automated Refactoring:** Employ codemod scripts and other refactoring tools on the codebase. This includes removing legacy auth code, inserting multi-tenancy hooks (e.g. ensuring API calls include org context), renaming or restructuring code to fit our conventions, and upgrading any outdated libraries if needed. All refactoring should be done in a controlled, tool-assisted manner to minimize bugs.

* **Data Model Extensions:** Modify the database schema to support multi-tenancy and external identity mapping. For example, extend the `users` table to store the Keycloak user ID (subject) and an `org_id` tenant foreign key, and introduce an `orgs` (organizations) table to represent tenants. Migrations for these changes are in scope, as well as updates to the ORM/DAO layer to use these fields for data partitioning.

* **CI/CD Pipeline:** Establish continuous integration and deployment pipelines. Include building and testing both frontend and backend, linting, running codemods (in a dry-run or report mode to enforce code standards), and deploying to test environments. Include automated checks for things like Kong config drift (using decK) and license compliance (scans for GPL code usage in proprietary modules).

* **Multi-Tenant Logic:** Implement core multi-tenancy logic in the application: ensure that every API endpoint and database query is aware of the current `org_id` (tenant) and is properly scoped. This may involve adding global request interceptors or guards that attach `org_id` from the JWT token to queries, and updating service methods to filter by `org_id`. This is critical for data isolation.

* **Preserve Existing Features:** All existing CRM modules of Twenty (Contacts, Companies, Deals, Activities, etc.) and their functionality **must remain intact**. Our work should be purely refactoring and architectural. No new CRM features (beyond those needed for multi-tenancy or platform integration) will be added in the initial phase. We will verify that contact management, pipeline tracking, activity logging, permissions, etc., work the same after the refactor.

* **Branding/UI Tweaks:** Apply our branding (logo, color scheme, product name) to the front-end as needed, and ensure emails or notifications (if any) reflect the new product name. This is limited to cosmetic changes; the UX and layout generally remain as in Twenty.

### **3.2 Out‑of‑Scope**

* **New CRM Functionalities:** Any new end-user features not present in Twenty’s core (e.g., additional CRM modules, new integrations, mobile app, etc.) are out of scope for this refactoring project. Feature development will resume after the MVP is launched. For now, we focus only on enabling SaaS and maintaining what exists.

* **Mobile Clients:** A mobile app or PWA for the CRM will not be developed in this phase. The focus is on the web application. (Mobile can be planned in a future phase once the web SaaS is stable.)

* **Plugin Ecosystem:** Twenty hints at a future plugin system; implementing or refactoring for plugin support is not included now, except ensuring our modifications do not preclude adding plugins later.

* **Non-Core SaaS Features:** Advanced SaaS features like full billing automation beyond basic metering, in-app product analytics, or a dedicated support portal are out-of-scope for the MVP. We will lay groundwork (e.g., usage tracking, roles, plans) but not implement everything at once.

*(In summary, the project scope is to **re-platform** Twenty into our SaaS architecture without changing its functional feature set for end users in Phase 1\. We ensure a solid foundation – SSO, gateway, multi-tenancy – on which future enhancements can be built.)*

## **4\. Personas & Use‑Cases**

| Persona | Goal/Use-Case | Primary Flow |
| ----- | ----- | ----- |
| **Sales Rep** | Single sign-on to all tools, manage CRM data for their accounts and leads under their organization. | Logs into the CRM via Keycloak SSO → Uses the CRM Single-Page App (SPA) with their org’s data → All API calls go through Kong (which validates token) to the CRM backend [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=What%20does%20Kong%E2%80%99s%20OpenID%20Connect,plugin%20do). |
| **Admin (Org Admin)** | Manage organization settings, users, and permissions for their tenant. | Logs into Keycloak Admin Console (for advanced user/role management if needed) → Invites users or assigns roles in the CRM’s Org Management UI → Configures org-specific settings (like plan or quotas). |
| **DevOps Engineer** | Manage infrastructure and config through code (GitOps). | Writes config changes (e.g., adding a new route or plugin config in Kong) as code → Submits pull request → CI runs `decK diff` and `decK sync` to apply Kong config [konghq.com](https://konghq.com/blog/engineering/gitops-for-kong-managing-kong-declaratively-with-deck-and-github-actions#:~:text=Kong%27s%20decK%20provides%20a%20command,the%20application%20of%20the%20configuration) → Kong Gateway is updated without manual steps. |
| **Support Engineer** | Onboard new tenants and support tenant isolation. | Uses an internal tool or CLI to create a new tenant (org) which provisions in Keycloak (group/realm) and in the CRM DB → Verifies the new org admin can sign up and only see their data. |
| **End Customer** | (Future consideration) Sign up for a new organization on the SaaS. | Visits public sign-up page → Enters org and admin info → System provisions a new org, admin account, and sends verification → Admin logs into CRM and uses it immediately under a free trial plan. |

*These personas illustrate how different users interact after the refactor: sales reps and admins benefit from seamless SSO and org-scoped data, while our DevOps can manage the system via GitOps. Notably, the “End Customer” persona covers the new **self-service onboarding** flow we plan to introduce for SaaS.*

## **5\. Functional Requirements**

* **FR-1: Keycloak OIDC Authentication** – The system **must use Keycloak for all user authentication**. User login is via the external Keycloak login page (OIDC Auth Code \+ PKCE )[keycloak.org](https://www.keycloak.org/docs/25.0.6/securing_apps/index.html#:~:text=As%20an%20OAuth2%2C%20OpenID%20Connect%2C,looking%20at%20Server%20Administration%20Guide), and Keycloak tokens (ID & Access JWT) are stored in the front-end. The legacy email/password login endpoints in the CRM backend are removed or disabled.

* **FR-2: Token Refresh & Session Management** – The SPA (front-end) must silently refresh tokens before they expire, to keep users logged in without interruption. Use of the Keycloak JS adapter or `@react-keycloak/web` library will handle token refresh (using hidden iframes or refresh token grant) so that user sessions remain active (Access Token \~15min TTL, Refresh Token \~8h) without requiring re-login.

* **FR-3: API Gateway Enforcement** – **All API requests from the front-end must pass through Kong Gateway**. Kong’s OIDC plugin will validate the Access Token on each request (signature, expiration, issuer) [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=kong,Cryptographic%20signature%20validation%2C%20expiry%20check). If a token is missing or invalid, Kong will reject the call with 401/403. Kong will also inject user identity info (like user ID, org ID, roles) as HTTP headers to the upstream for use by the NestJS backend. The plugin’s configuration for required scopes/roles will be used to enforce route-level or service-level authorization [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=1,roles_required) as needed.

* **FR-4: Backend Authorization via Keycloak** – The NestJS backend must perform authorization checks based on Keycloak roles/claims. We will integrate **`nest-keycloak-connect`** or a similar Keycloak adapter in NestJS, and protect each API route with appropriate guards (e.g., only users with the “admin” role can access admin-only endpoints, etc.). This ensures that even if Kong is bypassed (or in non-production scenarios), the backend still enforces security using Keycloak JWT validation and role checks.

* **FR-5: Automatic User Provisioning** – On first login via Keycloak, if a user does not yet exist in the CRM database, the system must auto-create a corresponding user record and associate it with the correct organization. Keycloak will supply user profile info (subject ID, email, name) via token claims; the backend will map `Keycloak_Sub -> users.keycloak_sub` and attach `org_id` based on the Keycloak group or realm. This way, separate user provisioning steps are not needed – user identity flows from SSO into the app automatically on first use.

* **FR-6: Tenant Data Isolation** – The CRM must enforce that **all data is tenant-scoped**. Every CRUD operation should be filtered by the current user’s `org_id`. For example, a Sales Rep in Org A cannot query or update contacts that belong to Org B. This will be implemented in multiple layers (Kong passes an `org_id` header, backend checks against user’s org, and database queries include `WHERE org_id = X`). Ideally, an extra safety layer like PostgreSQL Row-Level Security will be in place to prevent cross-org data access even if a bug occurs [crunchydata.com](https://www.crunchydata.com/blog/row-level-security-for-tenants-in-postgres#:~:text=Postgres%20Row,their%20own%20data%2C%20enhancing%20the).

* **FR-7: Kong Config as Code** – All configuration for Kong (routes, services, plugins, consumers, etc.) must be maintained as code in the repository and applied through automation (decK). Manual changes to Kong in production are not allowed. This ensures that environment configs can be reproduced and that changes are tracked. For MVP, Kong will run in DB-less mode using the config file, or if DB mode is used, decK will be the source of truth to sync the DB.

* **FR-8: Multi-Tenant User Management** – The application will include basic UI or workflow for tenant admins to manage their users. An organization administrator should be able to invite new users to their org (which triggers creation of a Keycloak user and assignment to the org’s group/role) and remove users. Each user account can belong to one or multiple organizations (if we allow multi-org membership), and the UI must provide an organization switcher if a user has access to more than one org.

* **FR-9: Plan Management & Quotas** – The system should support the concept of subscription plans (e.g., Free, Pro, Enterprise) at the tenant level. This includes recording each tenant’s plan and possibly enforcing limits (such as number of contacts or storage). For MVP, hard enforcement can be minimal, but at least the data model and hooks for checking quotas should exist. For example, if on a Free plan, the UI might indicate usage or restrict certain features. Plan data is stored in the `orgs` table (e.g., a `plan` field).

* **FR-10: Billing Integration (Planned)** – While we may not fully implement billing in the initial MVP, the architecture must not preclude it. We plan to integrate with a payment gateway (such as Stripe or Razorpay) to handle subscriptions. There should be placeholders for capturing billing info and an API to handle webhooks (e.g., payment succeeded, subscription cancelled). If feasible, MVP may include a simple integration for collecting payment details for upgrading a plan, but full automation can follow in a later phase.

*(The above functional requirements ensure that from a user perspective and system perspective, the refactored CRM provides seamless SSO, secure API access, and multi-tenant capabilities, all while behaving functionally like the original Twenty CRM in terms of CRM features.)*

## **6\. Non‑Functional Requirements**

* **Performance:** The introduction of Keycloak and Kong should not degrade app performance beyond acceptable ranges. Target **P95 API latency ≤ 200 ms** for authenticated requests via Kong (excluding network latency). The SPA should load the initial dashboard within \~3 seconds (on a typical broadband connection) for an average organization’s data. We will enable HTTP caching and optimize static assets as needed.

* **Scalability:** The system should support **at least 10,000 concurrent users** across tenants in the MVP deployment. The architecture (Keycloak, Kong, NestJS, PostgreSQL) must be horizontally scalable: e.g., Kong and backend can run in multiple instances behind load balancers. Keycloak will be clustered or use a highly available setup. Multi-tenancy is implemented with a shared database schema (with org filters), which is scalable for moderate tenant counts; if needed in future, we may consider separate DB schemas or instances per tenant for very large deployments.

* **Security:** Adhere to OWASP ASVS Level 2 or equivalent security best practices. All data in transit must be encrypted (HTTPS everywhere, OIDC tokens are JWTs signed with strong algorithms). Implement strong input validation and output encoding in the backend to prevent injection attacks. **No sensitive data in logs.** Tokens are short-lived (15 min) with refresh tokens 8 hours max and revocation on logout. Admin operations (like creating a user) should require appropriate roles. We will also conduct a security review or penetration testing focusing on multi-tenant isolation (e.g., attempt to access other tenant’s data) to ensure there are no leaks.

* **Compliance:** Since Twenty is GPL-licensed, our SaaS must comply with the **GNU GPL/AGPL requirements**. Specifically, if AGPL applies (if any part of Twenty is AGPL), we must make the source code of our modified version available to users accessing the service over the network. We will maintain a public repository or a customer-accessible source download to satisfy this. Additionally, ensure compliance with data protection laws (GDPR) by design – e.g., ability to delete user data on request and keeping audit logs of data access.

* **Availability & Reliability:** Aim for at least **99.5% uptime** for the service. Use Keycloak’s and Kong’s high-availability features (multiple replicas, database redundancy for Keycloak if using its DB). The system should degrade gracefully: if Keycloak is down, users cannot log in, but already logged-in sessions remain valid until token expiry. If Kong is down, the API is unreachable – mitigate by running at least two Kong nodes. Implement health checks and auto-restart policies (Kubernetes or similar) for each component.

* **Observability:** Implement monitoring and logging across all components. Kong and the NestJS backend should export metrics (e.g., request rates, latencies, error counts) to Prometheus or a similar monitoring system. Set up basic alerts for high error rates or response times. Centralize logs (e.g., to ELK stack or Loki); include the tenant ID in log entries to enable per-tenant debugging. This is especially important in multi-tenant context to trace issues specific to an org. For Keycloak, enable audit logs for admin actions. Ensure we do not log sensitive PII or secrets.

* **Usability:** The transition to SSO and multi-tenant should be transparent to users. The CRM app’s UI/UX should remain as intuitive as Twenty’s. Any new UI elements (like an org switcher or invite dialog) should follow the existing design patterns. We will provide an admin guide for managing orgs, but end-users (sales reps) should not need additional training – their experience is essentially the same CRM, just with a unified login.

* **Maintainability:** The codebase must remain easy to update. By using automated refactors and aligning with Nx monorepo standards, contributors should be able to navigate the repo. Enforce code style and lint rules in CI. Provide documentation for the custom parts (Keycloak integration, Kong config) so future devs understand how it’s set up. Also, keep the fork updated with Twenty upstream periodically; plan for monthly upstream merges if Twenty releases improvements, which should be easier if our changes are modular.

*(These non-functional requirements ensure the SaaS CRM is robust, secure, and efficient. In essence, we are not just slapping new components on — we are engineering the system to be production-grade for SaaS.)*

## **7\. Technical Architecture**

mermaid

CopyEdit  
`flowchart LR`  
    `subgraph Frontend (Browser)`  
    `BrowserUser`  
    `end`  
    `subgraph Auth`  
    `Keycloak`  
    `end`  
    `subgraph Edge`  
    `Kong["Kong Gateway\n(OIDC Plugin)"]`  
    `end`  
    `subgraph Backend`  
    `CRM["Twenty CRM API\n(NestJS App)"]`   
    `DB[(PostgreSQL Database)]`  
    `CRM --> DB`  
    `end`  
    `subgraph DevOps`  
    `decK["decK CLI (GitOps)"]`  
    `end`

    `BrowserUser -->|OIDC Login| Keycloak`  
    `BrowserUser ==>|Bearer JWT| Kong`  
    `Keycloak ==> BrowserUser`

    `Kong -->|Validates JWT| Kong`  
    `Kong -->|Identity Headers| CRM`  
    `decK -->|Config Sync| Kong`  
    `CRM -->|API Responses| BrowserUser`

*Diagram: High-level architecture of the refactored system.* The **Browser SPA** uses OIDC to authenticate with **Keycloak**. Once logged in, the SPA receives a JWT and sends API requests with that token. **Kong Gateway** (with the OIDC plugin) sits in front of the **CRM backend** (NestJS application). Kong verifies tokens on each request and, if valid, forwards the request to the backend with added identity headers (e.g., X-User-ID, X-Org-ID). The **CRM API** interacts with the **PostgreSQL** database (and Redis cache, if applicable) to fulfill requests. DevOps engineers manage Kong’s configuration via **decK**, pushing changes through Git (decK sync ensures Kong’s routes and plugins match our config files). **Keycloak** manages user authentication and stores user credentials and roles, often using its own database (not shown). This architecture decouples auth and edge concerns from the application logic, improving security and scalability.

### **7.1 Front‑End**

* **Framework:** Next.js 14 (React 18\) SPA, residing in the Nx monorepo. It will be served as a single-page app with client-side routing. We might deploy it as a static app on CDN or via Node depending on SSR needs (though Twenty likely uses Next for development convenience, it may run mostly as SPA).

* **Keycloak Integration:** We will use the official Keycloak JS adapter or a React-specific wrapper (e.g., `@react-keycloak/web`) to integrate OIDC. On app startup, the Keycloak library will check for an existing session or perform a redirect to Keycloak for login. The front-end will be configured with the Keycloak realm URL, client ID, etc.

* **Token Storage:** Access Token and ID Token will be stored in memory (in a React context managed by the Keycloak adapter) for security – we avoid localStorage to mitigate XSS risks. The Keycloak adapter will handle refreshing tokens (it can automatically use the Refresh Token via an iframe or timed refresh). No sensitive tokens will be persisted in an insecure manner on the client.

* **API Calls:** The front-end will attach the bearer Access Token to every API request (e.g., in the Authorization header). With the OIDC plugin on Kong, if the token is missing or invalid, the request will be rejected, so the front-end should handle redirecting to login if it receives a 401\. We’ll also ensure the SPA gracefully handles token expiration (e.g., by refreshing or prompting login).

* **State Management:** Likely continue using Recoil (as in Twenty) for client state management, adjusting if needed to accommodate user/tenant info from Keycloak. For example, when the user switches organizations, we might need to update some global state or even refresh data.

* **Branding and UX:** Update the app’s theming to match our brand. This includes replacing logos and names. Ensure that the login button or any auth-related UI (if present) now corresponds to Keycloak (though often it’s just a redirect). The UI will gain a few new elements: for instance, an org selector (dropdown) if multi-org, or a section in settings for org management. We’ll follow Twenty’s design patterns for these additions.

### **7.2 Edge (API Gateway Layer)**

* **Kong Gateway:** We plan to use **Kong Gateway 3.x** (Open-Source or Enterprise). For the MVP, **DB-less mode** is preferred for simplicity – the entire config is in a `kong.yaml` that decK will apply. This avoids needing a separate database for Kong and fits well with containerized deployment (config can be baked in or mounted).

* **OIDC Plugin:** Kong’s OpenID Connect plugin (if using Enterprise) will be configured to authorize with Keycloak. In practice, since our SPA will already have tokens, we’ll likely use the plugin in its **Token Validation** mode (i.e., Kong as a resource server) where it expects a JWT on incoming requests and validates it against Keycloak’s JWKS. We’ll set `config.discovery` to Keycloak’s discovery endpoint and `config.auth_methods` to `bearer` only (since we won’t use the plugin’s full login flow). The plugin can also inject user info to upstream, which we will leverage: map Keycloak token claims to headers (e.g., `sub` \-\> `X-User-Id`, `org_id` claim \-\> `X-Org-Id`, roles \-\> `X-Role-Ids`). The plugin will be global or on the specific route for the API.

* **Other Plugins:** Enable a **Rate Limiting plugin** (per user or per org) to prevent abuse (for MVP, we can set a sensible global rate limit, e.g., 100 requests/sec per token). Also use **Request Transformer** plugin if needed to clean or augment requests (for example, to remove any client-sent org\_id and solely rely on Kong’s injected header to prevent tampering). Logging plugin will send logs to a file or HTTP endpoint for aggregation. If using Kong OSS (which lacks the OIDC plugin), we may use an alternative like an OAuth2 Introspection plugin or a custom plugin – but assumption is we have Kong Enterprise for full OIDC support.

* **Endpoint Routing:** Kong will route `https://api.ourcrm.com/` (or similar) to the NestJS CRM service. We define a Service for the CRM API (targeting the upstream Node service, e.g., `http://crm-backend:3000`), and Routes that match `/*` or specific paths. We’ll likely have a wildcard route for all API paths, since the CRM is a single service (unless we break it into microservices later). The static front-end could be served separately (not via Kong, or via a CDN), so Kong mainly handles API and maybe Keycloak callbacks if needed.

* **TLS and Networking:** Kong will terminate SSL (we’ll provision a certificate for the custom domain). Between Kong and the backend we can use HTTP (in cluster network). Ensure CORS is configured on Kong or backend such that the SPA (which may be on a different subdomain) can call the API successfully. Typically, Kong can pass through appropriate CORS headers, or we configure NestJS to allow the front-end origin.

* **decK & GitOps:** As noted, all Kong configuration will be stored in files (e.g., `kong.yaml`). We will use **decK** to sync this to Kong. For example, on a Git push to main branch, a GitHub Action could run `deck validate && deck sync`. decK can also do `diff` to show changes. This ensures that any config drift (manual changes) get detected and reverted, and we have an audit trail of changes via Git history.

### **7.3 Backend (Server Application)**

* **Framework:** **NestJS** (likely NestJS 9 or 10, as used by Twenty) for the server, with TypeScript. The backend is already structured as a NestJS application (with modules for each domain, e.g., Contacts, Deals, etc.). We will introduce NestJS guards and interceptors as needed for auth.

* **Auth Guard:** Use the `@nestjs/keycloak` strategy (via `nest-keycloak-connect` or a generic JWT auth guard) to protect routes. Essentially, every incoming request will have a JWT (validated by Kong). The backend can either trust that Kong already validated, or it can also verify signature (since it has access to the public key via Keycloak). To be safe, we might implement a middleware to parse the JWT from the header and attach it to the request context (so controllers can access user info). We will enforce authorization either in controllers (e.g., with `@Roles()` decorators that `nest-keycloak-connect` provides) or via custom guards that check the `roles` claim in the token.

* **Multi-Tenancy Enforcement:** We will implement a global interceptor or incorporate logic in the service layer to apply `org_id` filtering. For example, if using an ORM like Prisma or TypeORM, we could set up a middleware or plugin such that any query on a multi-tenant entity automatically adds a `WHERE org_id = currentOrgId`. The `currentOrgId` will be derived from the JWT (Keycloak can provide an `org_id` claim via a custom protocol mapper, which we will configure). Alternatively, each request could carry an `X-Org-Id` header injected by Kong (from the token), and our backend would trust that header (as Kong is internal) and use it to filter. We will update service functions in modules (e.g., ContactService.getContacts(orgId)) to ensure they require an `orgId` parameter or read from context, so that even developers cannot accidentally fetch cross-org data. A comprehensive approach also includes adding **Row-Level Security** policies in PostgreSQL as a safety net[crunchydata.com](https://www.crunchydata.com/blog/row-level-security-for-tenants-in-postgres#:~:text=Postgres%20Row,their%20own%20data%2C%20enhancing%20the), which we might do if time permits (noting that RLS requires setting the `app.current_org_id` at session, which may be done via a DB proxy or a session initializer).

* **Data Model:** Using Prisma (Twenty uses Prisma ORM based on hints in their docs) or another ORM, we’ll update the schema to include the new multi-tenant fields. The `User` entity gets a `keycloakSub` (string/UUID) and `orgId` (foreign key to Org). The `Org` entity is new with fields like `id`, `name`, `plan`, etc. Other entities (Contact, Deal, Activity) likely have an implicit organization via relationships (e.g., a Deal belongs to a Pipeline which belongs to an Org) – we will review if we need to add `orgId` directly to each main entity or rely on join through user ownership. For simplicity and clarity, we may add `orgId` to major tables so that queries are straightforward.

* **Cache:** Twenty uses Redis (as indicated by their stack) for caching or background jobs (BullMQ for job queue). We will ensure any cache keys include tenant context where appropriate. For example, if caching some query results, the key should incorporate `orgId` to avoid serving data across tenants.

* **Background Jobs:** If the CRM has background workers (for emails, etc.), those should also be multi-tenant aware. Likely they use BullMQ (Redis based). We should include the tenant context in any jobs (job data or queue segmentation) so tasks are performed per org. We’ll also ensure that the job scheduler (NestJS) still runs under the new auth (jobs might not need auth, since they run internally, but if they call internal APIs, they might need a service account token or skip Kong).

* **Integrations:** Twenty might have integrations (e.g., Google Calendar sync). We must verify those flows still work when we remove local auth. Possibly, we’ll need to update redirect URLs or how OAuth tokens for integrations are stored (ensuring tied to org or user). This is a minor point; we primarily ensure no integration tries to use legacy credentials.

* **API Changes:** The external API (if any clients exist beyond our own SPA) remains the same except for auth. If any third-party were using Twenty’s API, they now must present a Keycloak token – but since this is an internal product now, that’s fine.

### **7.4 Toolchain & Development**

* **Nx Monorepo:** Continue using Nx to manage the monorepo. It gives us tasks running and orchestration. We will add custom build targets if needed (like a target to run codemods). Nx will help ensure front-end and back-end stay in sync and can be tested together.

* **Code Mods:** We will write **jscodeshift** scripts for automating code changes. For instance, removing any import of legacy auth modules in one sweep[innovirtuoso.com](https://innovirtuoso.com/open-source-software-2/twenty-the-ultimate-open-source-crm-for-self-hosting-and-local-setup/#:~:text=modify%2C%20and%20scale%20without%20license,the%20GitHub%20repo%20for%20details), or appending an `orgId` parameter to function calls across files. We will use **AST-Grep** to detect any leftover patterns (like direct access to authentication context) and ensure they are handled. These tools allow consistent changes in hundreds of files quickly.

* **TypeScript Migration:** If some parts of the code were loosely typed or using JavaScript, use **ts-migrate** to incrementally add types. The goal is to catch errors introduced by refactoring at compile time. All new code (Keycloak integration, etc.) will be in TypeScript with appropriate types (e.g., JWT payload interfaces).

* **Linting & Formatting:** Enforce **ESLint (with TypeScript rules)** and **Prettier** formatting. We will adopt a slightly modified config if needed to align with our org’s style, but likely we keep what Twenty had. CI will fail if code doesn’t pass lint or format checks.

* **CI/CD:** Use GitHub Actions (or our CI system) to run tests, linters, codemod checks on each PR. Also set up pipeline for deployment (containerize the app, push to a registry, etc.). While CI/CD is not user-facing, it’s crucial for maintainability and fast iteration.

* **Testing:** Ensure that existing tests (if any in Twenty) still pass. We will add new tests for the auth integration (e.g., a test that verifies an unauthorized request is rejected, a test that a user from another org cannot access data). Use both unit tests and integration tests (possibly employing Keycloak’s testing tools or a dummy OIDC token for backend tests). We might employ a mutation testing tool or snapshot comparisons to ensure codemods didn’t break functionality (e.g., run a set of API calls before and after refactor, compare results).

## **8\. Data Model Changes**

The following schema changes are proposed to enable multi-tenancy and SSO integration:

| Table | New Columns | Purpose |
| ----- | ----- | ----- |
| **users** | `keycloak_sub` UUID (indexed) `org_id` UUID (indexed, FK to orgs) | **Map external identity:** Links a Keycloak user account to this user record, and associates the user to an organization (tenant). On login, we find or create a user by `keycloak_sub`. A user can have one primary `org_id` (in future, many-to-many if multi-org membership is allowed via a join table or a user-org linking table, but for MVP one user \= one org). |
| **orgs** | `id` UUID (PK) `name` VARCHAR(100) `plan` VARCHAR(50) `keycloak_group` VARCHAR(50) | **Tenant record:** Each row represents a tenant organization. `name` is the company/org name. `plan` is the subscription plan (Free, Pro, etc.) for billing. `keycloak_group` stores the Keycloak group or realm that corresponds to this org (if using realm-per-tenant, this might be realm name; if using groups in a single realm, this is the group ID/path). We may also use `orgs` to store config like feature flags or custom settings per tenant (could extend with JSONB column for miscellaneous config). |

Additionally, all major data tables (contacts, deals, etc.) will implicitly be tied to `orgs`. If not already present, we add an `org_id` FK on those tables as well, or ensure they link via user → org. E.g., a **contacts** table might get `org_id` so that even if a contact isn’t owned by a user, it’s tagged to an org for querying ease. We will update Prisma schema or migrations accordingly.

*(After these changes, every important record can be traced to an `org_id`, enabling per-tenant data partitioning. Database queries and ORM models will incorporate this for security.)*

## **9\. Milestones & Timeline**

We plan the refactoring in **phases**, which can run partially in parallel where possible. Below is a high-level timeline with key milestones:

| Phase | Description | Owner | Start | End |
| ----- | ----- | ----- | ----- | ----- |
| **A** | *Legal & License Confirmation* – Verify Twenty’s GPL license terms, ensure we comply; set up base fork repository. | Product Owner & Legal | Day 0 | Day 3 |
| **B** | *SSO Integration (Keycloak)* – Deploy Keycloak and replace local auth in front-end and back-end with OIDC. | Backend & Frontend Devs | Day 3 | Day 10 |
| **C** | *API Gateway Setup (Kong)* – Deploy Kong Gateway and configure OIDC plugin \+ decK GitOps. Basic end-to-end API calls working via Kong. | DevOps Engineer | Day 5 | Day 12 |
| **D** | *Automated Code Refactors* – Run codemods to excise old auth code and apply project guidelines. Ensure codebase builds and passes tests after bulk changes. | Frontend & Backend Devs | Day 10 | Day 20 |
| **E** | *Data Model Migration* – Add multi-tenant schema changes, run migrations, update ORM and ensure app still functions with new `org_id` logic. | Database Engineer | Day 15 | Day 22 |
| **F** | *CI/CD & Core Hardening* – Implement CI pipelines, monitoring, and conduct initial QA on core functionality (auth flows, basic usage with one tenant). Fix bugs, ensure stability. | DevOps & QA | Day 18 | Day 28 |
| **G** | *Tenant Onboarding & SaaS Features* – Build self-service sign-up, tenant admin management UI, and basic usage metering. Prepare for multi-tenant production use. | Full Stack Devs | Day 22 | Day 35 |
| **H** | *MVP Launch Prep* – Final UAT testing across multiple tenants, documentation, and go-live deployment to production environment. | QA & DevOps | Day 30 | Day 40 |

**Target MVP Launch: Day 40** – At the end of Phase H, we expect to have an MVP ready for real tenants, with SSO, gateway, multi-tenancy, and branding in place. The phased timeline is aggressive (roughly 6 weeks); tasks are structured to overlap and enable parallel work (e.g., Phase C can start before B finishes, since Kong setup can proceed while front-end integration is in progress). Contingency: If any phase runs over, we will adjust subsequent phases but maintain sequence (Keycloak before Kong, etc.).

## **10\. KPIs / Success Metrics**

We will consider the project successful if the following key metrics are met post-launch:

* **SSO Success Rate ≥ 99%** – Percentage of login attempts that succeed without errors (reflects reliability of Keycloak integration). We expect very few authentication failures outside of user error.

* **API AuthZ Error Rate \< 0.5%** – Fewer than 0.5% of API requests result in HTTP 401/403 errors due to auth issues in steady-state usage. This indicates tokens are being properly handled and not expiring unexpectedly. Essentially, once users are logged in, API calls should rarely be refused (unless the user is truly unauthorized for that action).

* **Page Load Time (TTI) ≤ 3s** – The Time to Interactive for the main app (after login) should be 3 seconds or less on average, measured on typical broadband. Even with the gateway and SSO, the user experience should remain snappy. Any regression from Twenty’s original performance should be minimal.

* **No Config Drift Incidents** – Using decK for Kong, we aim for **0 incidents of config drift** in production. This means our GitOps process is effective: the config in Git is always what’s running. We’ll monitor Kong’s state; any manual change would be caught and reverted via CI.

* **Tenant Isolation Breaches \= 0** – Throughout testing and after launch, there should be zero instances of a user accessing another tenant’s data. We will simulate attempts and also rely on bug bounty or internal pen-testers to confirm this. This is an absolute must for trust in the SaaS.

* **Crash-Free Uptime ≥ 99%** – The application (front-end and back-end) should be stable. No crashes or unhandled exceptions under normal usage. This will be measured via monitoring logs.

*(KPIs focus on security, performance, and reliability – critical for an enterprise-grade SaaS. We will gather these metrics through monitoring tools and user feedback after MVP launch.)*

## **11\. Assumptions**

* **Keycloak as Central IDP:** We assume Keycloak will serve as the **central identity provider for all our applications going forward**, not just this CRM. Thus, efforts spent on Keycloak (realm setup, user directories) benefit the broader organization (SSO across multiple apps).

* **Single Realm Multi-Tenancy:** Tentatively, we plan to use **one Keycloak realm for all tenants**, using groups or attributes to separate organizations. This is to avoid managing dozens of realms. (If we find it better, we might use realm-per-tenant, but that’s more overhead – see Open Decisions.)

* **Kong in Kubernetes:** It’s assumed Kong and the Node backend will be deployed on Kubernetes (or similar container platform) managed by our DevOps. We will use Kong’s ingress or stand-alone mode as fits. Kong in DB-less mode will have config loaded via an init container or config map. For MVP, a simpler docker-compose dev environment will be used, then containerized for staging/prod.

* **Upstream Sync:** Twenty’s team is actively developing the open-source CRM. We assume we will **rebase from Twenty upstream regularly (e.g., monthly)** to incorporate security patches and improvements. Our fork will thus remain close to the open-source trunk. We will document our changes clearly to ease these merges.

* **No Major DB Sharding Needed:** The multi-tenant approach will use shared tables with orgID keys. We assume this will scale for the near term (tens of thousands of records per tenant, and maybe a few hundred tenants). If we succeed, eventually we might need to shard by tenant or move big tenants to separate DBs, but MVP doesn’t require that complexity.

* **Email/Notification Services:** If Twenty had any built-in email sending (like invite emails or logging activities via email), we assume we can reuse that, just configuring SMTP or API keys as needed. Integrations like email/calendar will continue to work after SSO (assuming they didn’t depend on the old auth scheme).

* **Licensing of Dependencies:** All third-party additions (Keycloak, Kong’s OIDC plugin) are properly licensed for commercial use (Keycloak is open-source Apache 2.0, Kong OIDC plugin is part of Kong Enterprise which we have licensing for). We also assume using Twenty’s GPL code in a SaaS is acceptable as long as we provide source on request (AGPL would mandate it; GPL likely too in SaaS due to network clause if AGPL).

## **12\. Risks & Mitigations**

| Risk | Impact | Likelihood | Mitigation |
| ----- | ----- | ----- | ----- |
| **AGPL License Obligations** – If any part of Twenty is AGPL, our SaaS might be forced to disclose source to anyone who uses it (AGPL’s network clause). This could expose proprietary extensions we add. | High | Medium | **Mitigation:** Separate any proprietary code into distinct microservices not derived from Twenty (so not subject to AGPL). Also, clearly document which parts of the system are GPL licensed and ensure we are prepared to publish those source changes. Legal can also explore obtaining a commercial license from Twenty if available (to relicense under fair terms). |
| **Codemod Errors/Regressions** – Automated refactors might unintentionally break functionality (e.g., remove needed code) if patterns aren’t precise. | Medium | Medium | **Mitigation:** Write unit tests for critical auth and multi-tenant logic now (before codemods) to catch breakage. Use version control – commit each codemod change separately for review. Perform manual code review on the codemod diffs, especially for areas where automated changes occurred. Also, run a suite of integration tests and compare key workflows pre- and post-refactor (e.g., create a contact, make sure it still works). |
| **Performance Degradation** – The extra network hop through Kong and Keycloak token introspection could slow responses, and multi-tenant org checks could add DB overhead. | Medium | Low | **Mitigation:** Use local token validation (JWT) rather than introspection on each request to avoid extra calls. Kong’s JWT validation is in-memory after first key fetch. For DB, ensure `org_id` is indexed wherever used in WHERE clauses. Perform load testing; if latency issues, consider caching frequent reads per org or increasing resource allocation. |
| **Refresh Token Handling** – If the silent token refresh fails (e.g., Keycloak iframe issues), users might be logged out unexpectedly. This could be disruptive. | Low | Medium | **Mitigation:** Follow Keycloak best practices for SPAs (use `check-sso` on load, `silentCheckSsoRedirectUri`). Test token refresh thoroughly, including network edge cases. Also, configure Keycloak with sliding sessions if needed. Provide a good UX – if session expires, redirect to login smoothly. |
| **Multi-tenant Data Leak (Bug)** – A mistake in scoping could allow one tenant to see another’s data (e.g., a missing org filter on one API endpoint). | High | Low | **Mitigation:** Implement global safeguards: use an ORM middleware that automatically injects org filters so even if a developer forgets, the query is constrained. Additionally, utilize Postgres Row-Level Security as a backstop [crunchydata.com](https://www.crunchydata.com/blog/row-level-security-for-tenants-in-postgres#:~:text=Conclusion) – even if a query slips through, the DB itself will prevent returning rows not matching the set `org_id` for the session. Finally, do a security audit focusing exclusively on data separation (pentest with attempts to change org\_id in requests, etc.). |
| **Keycloak Availability** – Keycloak becomes a single point of failure for auth (if it’s down, no one can log in, and possibly token validation fails if keys cannot be fetched). | High | Low | **Mitigation:** Run Keycloak in HA (clustered or at least active-passive). For token validation, Kong caches the JWKS keys, so short Keycloak outages won’t affect JWT verification. Also, tokens are valid for 15m; a short Keycloak downtime shouldn’t log out users. Implement health checks and consider Keycloak’s cross-DC features if needed. |
| **Onboarding Complexity** – Building self-service sign-up and payment flows might extend beyond MVP timeline or complicate the launch. | Medium | Medium | **Mitigation:** If time is short, we can launch MVP with a manual onboarding (we create tenant accounts ourselves for initial customers). The self-service signup and billing could be behind feature flags, enabled gradually. Ensure architecture supports adding it later without refactoring (hence including it in design). Focus for MVP on multi-tenancy and SSO core; treat self-service as an extension that can roll out shortly after. |

*(We will maintain a risk log throughout the project and update mitigations. The above are initial identified risks.)*

## **13\. Open Decisions (TBD)**

1. **Tenant Model in Keycloak:** Whether to use a **Realm-per-Tenant** model in Keycloak versus a single realm with Groups for tenants is still under discussion. *Realm-per-tenant* provides stronger isolation (separate user namespaces) but is harder to manage at scale. *Single realm \+ groups* are simpler and what we lean towards (each user belongs to exactly one org group). We need to confirm after some load testing of the group approach and maybe advice from the Keycloak community.

2. **Kong Mode (DB-less vs DB)**: For MVP we assume **DB-less** Kong (config as code). Long-term, if we need dynamic config changes at runtime (e.g., on-boarding a new tenant might require new Kong consumer credentials), DB-less might become cumbersome. We will decide if production will stick to DB-less or move to DB-backed Kong. DB-backed could still be GitOps managed but with decK connecting to the DB. The decision depends on how frequently config changes in production and ops preference.

3. **User Identity Mapping:** Decide how to **map Keycloak users to CRM users** exactly. Two approaches: (a) Create CRM user record at first login (on the fly). (b) Pre-provision CRM user when invited (i.e., when admin invites via our app, simultaneously create Keycloak user and CRM user). The simpler path is (a) lazy creation on login, which we will do for MVP. But this means until a user logs in, they don’t exist in the CRM side (which might complicate assigning them things ahead of time). Option (b) is more enterprise-friendly (admin can add a user and set roles before they ever log in). We’ll aim to implement (b) if possible in the tenant admin UI.

4. **Scope of Billing**: We need to decide how much of billing to include in MVP. Options: just track usage and have manual invoicing vs. integrate Stripe fully for automated card charging. Given timeline, likely basic metering in MVP (so we can understand usage patterns and set pricing), and actual payment integration shortly after MVP or for GA (General Availability).

5. **Expose Source Code:** Since GPL requires exposing source to users, we need to decide *how* to do that for SaaS. We could have a public GitHub repo for our fork (making it open-source as well), or provide tarball downloads of source upon request. The method and timing (e.g., do we publish source with each release cycle) is to be determined by product/legal. For MVP, we lean towards making the fork public on GitHub to play safe with compliance and even attract community contributions, but will confirm with stakeholders.

*(These decisions should be resolved by mid Phase A, especially the Keycloak model, as they affect implementation approach significantly.)*

## **14\. Appendices**

* **A. Keycloak OIDC Endpoints and Flows:** Reference documentation for Keycloak’s OpenID Connect endpoints and configuring clients [keycloak.org](https://www.keycloak.org/securing-apps/oidc-layers#:~:text=Authorization%20code)[keycloak.org](https://www.keycloak.org/securing-apps/oidc-layers#:~:text=For%20more%20details%20refer%20to,in%20the%20OpenID%20Connect%20specification). This includes the authorization endpoint, token endpoint, logout endpoint, and how PKCE is supported. Also see Keycloak’s guidance on securing applications via OIDC (Keycloak Documentation, Securing Applications Guide).

* **B. Kong OIDC Plugin Configuration:** Links to Kong’s documentation on the OpenID Connect plugin usage and config options [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=The%20OpenID%20Connect%20plugin%20enables,authentication%20to%20new%20upstream%20services)[developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=Supported%20flows%20and%20grants). Contains details on how to set up Kong as a relying party, supported OAuth flows, and how to map token claims to headers. Also the Kong Hub “How-To” guides for the OIDC plugin are useful for real examples (like integrating Kong with Keycloak).

* **C. decK CLI & GitOps:** Kong Inc. blog post on using decK with GitHub Actions [konghq.com](https://konghq.com/blog/engineering/gitops-for-kong-managing-kong-declaratively-with-deck-and-github-actions#:~:text=Kong%27s%20decK%20provides%20a%20command,the%20application%20of%20the%20configuration), and decK’s official repository/docs for syntax. This shows how we will implement the configuration pipeline (with `deck diff` and `deck sync`). It’s a model for infra-as-code that we follow.

* **D. Codemod Tooling Examples:** Articles on large-scale refactoring using codemods [martinfowler.com](https://martinfowler.com/articles/codemods-api-refactoring.html#:~:text=By%20automating%20refactoring%20tasks%2C%20codemods,to%20manually%20modify%20every%20file) and AST tooling. Airbnb’s Tech Blog “Turbocharged JavaScript Refactoring with Codemods” and Martin Fowler’s article on codemods provide insight into strategies we’ll use to safely transform the code. This is the inspiration for our approach to remove legacy auth and insert org guards in a systematic way rather than editing hundreds of files manually.

---

## **15\. Implementation Plan: Phases & Tasks**

Below is a detailed breakdown of tasks and subtasks for each phase of the project. This serves as a checklist and guide for the development team. Each phase groups related workstreams; some can proceed in parallel as noted. The emphasis is on **preserving functionality** while layer-by-layer inserting our new architecture components.

### **PHASE A: Legal & Upstream Baseline**

1. **Review and Confirm Twenty License Compliance**

   * Audit the upstream Twenty repository for license information. Locate the LICENSE file and identify if it’s GPLv3 or AGPLv3 (and if the frontend/backend have separate licenses) [twenty.com](https://twenty.com/#:~:text=of%20Open%20Source).

   * Consult with Legal on implications of using GPL components in SaaS. Document what we must provide to users (e.g., source code) and what we must avoid (e.g., commingling GPL and proprietary code in same module).

   * Create a brief internal memo **“License Obligations for Twenty CRM”** for the engineering team so everyone is aware (e.g., “We must open-source any modifications to these modules…”). This ensures compliance isn’t neglected.

2. **Fork Repository & Initial Setup**

   * Fork the `twentyhq/twenty` GitHub repo to our organization (private repo initially, unless we decide on public). Preserve the git history. Also set the original as an upstream remote for pulling updates.

   * Set up the development environment: install Nx, Node, database, etc. Ensure the project runs locally in its original state.

   * Initialize our CI pipeline on the repo: at minimum, run existing tests (if any) and lint on push. Verify that the baseline fork passes CI (ensures our forked codebase is intact and working before changes).

   * If Nx is not fully configured, configure an Nx workspace (the Twenty repo likely already uses Nx). Make sure we can run `nx serve` for the app, `nx test`, etc., to execute tasks easily.

3. **Baseline Build & Feature Verification**

   * **Run the application locally** (both front-end and back-end). Document the steps in README if needed. For example, start PostgreSQL, run `npm run db:migrate`, then `npm run dev` and ensure the app comes up (likely at localhost:3000).

   * Perform a quick **smoke test of core features**: create a contact, create a deal, move a deal through pipeline stages, etc. Note any obvious bugs or configuration steps needed (the open-source project might have some default demo data or require certain environment variables).

   * Verify the built-in auth flow of Twenty (since we’re replacing it): e.g., create a user via their sign-up if possible and log in. This helps understand what we’re replacing (like any email verification?).

   * **Document** any issues encountered or any assumptions about how features work. This will help later when ensuring we didn’t break anything. If possible, export some sample data (like a few contacts) to use later in testing our refactor.

### **PHASE B: Keycloak Identity Integration**

4. **Deploy Keycloak for Dev/Test**

   * Stand up a local Keycloak instance (e.g., using Docker). Use the latest stable Keycloak (at least v20+).

   * Create a realm (call it “CRM” or our product name). Within this realm, create a **client** for the CRM front-end (e.g., client ID "crm-app"). Configure this client for **public** access type (since our SPA can’t keep a secret) and enable standard flow (Authorization Code) with PKCE [developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=8,with%20client%20secret%20or%20PKCE)[developer.konghq.com](https://developer.konghq.com/plugins/openid-connect/#:~:text=). Set redirect URIs to our dev front-end URL (http://localhost:3000/\*).

   * In Keycloak, set up initial **Roles** that mirror what Twenty had for permissions (e.g., if Twenty has “Admin”, “SalesRep” roles internally, create them in Keycloak as realm roles or client roles). Also, decide how to model organization membership: we could use **Keycloak Groups** where each group is an organization and users are members. Create a couple of example groups (Org1, Org2) for testing. Possibly, add a custom attribute mapper so that when a user logs in, their group membership (org) is included in the token as `org_id`.

   * Configure Keycloak’s token mappers: e.g., include `preferred_username` or email, and include group information. If using groups, add a mapper for Group list or a specific group attribute. If not using groups, we might later maintain a separate mapping table, but groups is easier for now.

   * Create a **test user** in Keycloak (e.g., \[email protected\]) with a password, assign them to a test group (Org1) and roles (SalesRep role). This will be used to validate login flow.

5. **Integrate SPA with Keycloak SSO**

   * Add Keycloak JS adapter to the front-end. For Next.js, possibly create a higher-order component or context provider that initializes Keycloak using the config (realm, clientId, URL of Keycloak).

   * Remove or bypass the Twenty local login page/component. Instead, implement a “Login” button or redirect that triggers Keycloak’s login. Possibly use the **KeycloakProvider** from `@react-keycloak/web` to wrap the app. This should handle redirects to Keycloak and back.

   * After login, ensure the Keycloak token is stored and the React app knows the user is authenticated. Remove references to legacy auth context (for example, Twenty might have had an AuthContext providing current user; now it should derive from Keycloak’s user info). Codemods can help strip out old context usage.

   * Implement **Logout** by calling Keycloak’s logout function (which will redirect to Keycloak’s logout endpoint).

   * Test the front-end: Start the app, it should redirect to Keycloak (on protected route). Log in with the test user, and upon redirect back, the app should show the CRM UI. Verify that API calls now include the Authorization header with the JWT.

6. **Backend Authentication & Authorization Refactor**

   * In the NestJS app, install `keycloak-connect` (the Node adapter) and `nest-keycloak-connect` library. Set up the Keycloak middleware (KeycloakConnectModule) with the realm config (server URL, realm name, resource). This will allow us to use `@AuthGuard()` etc.

   * Disable or remove any existing auth guards or JWT verification that was part of Twenty (they might have their own JWT or session logic). Ensure global `AuthGuard` now uses Keycloak’s JWT strategy. Essentially, every request should either be allowed through if Keycloak verifies the token, or denied if not.

   * Protect routes: Using `@Roles()` decorators or `@Scopes()` as provided by nest-keycloak-connect, annotate controller methods according to requirements. For example, an endpoint that deletes an organization might require an “admin” role. If needed, create Keycloak client scopes for finer control.

   * Implement a **user resolution logic**: when a Keycloak-authenticated request comes in, if we need to know the CRM user, use the `keycloak_sub` from token to look up (or create) a `users` table entry. This could be done in an authentication guard or an interceptor that runs on first login. Possibly, store a mapping in memory for repeated calls. Ensure this logic also sets the `org_id` in the request context for use by controllers or services.

   * Verify with a manual test: using Postman or the front-end, call a few API endpoints (e.g., GET contacts) with a valid token. The request should pass Kong (in dev, you might call backend directly for now), hit NestJS, the Keycloak guard should allow it, and data returns. Then try without a token or with an invalid token – should get 401\.

7. **Keycloak User Lifecycle & Admin Setup**

   * Decide how to manage Keycloak users in non-dev environments. Likely we will need a **Keycloak Admin UI or CLI** to create initial admin user(s) and tenants. For dev/testing, write a script using Keycloak’s admin API (or export realm JSON) to create a new realm and users so that testers can easily set up.

   * Plan how invites will work: possibly, the CRM’s “Invite User” action will call our backend, which in turn calls Keycloak admin REST API to create a user and send an email. For now, maybe just document or stub this – actual implementation might be Phase G.

   * Ensure Keycloak settings like token lifespans, allowed CORS origins, etc., are properly configured for our app domain. For instance, adjust Keycloak realm to set web origins to our front-end URL to allow token refresh via CORS if needed.

*(Outcome of Phase B: The application should no longer use its internal auth. All logins go through Keycloak, and the app accepts Keycloak tokens. A user can log in via Keycloak and use the CRM normally. However, at this stage, we might not have Kong or multi-tenancy fully working yet; those come next.)*

### **PHASE C: Kong Gateway \+ decK GitOps**

8. **Kong Gateway Deployment (Dev Env)**

   * Deploy **Kong** locally (e.g., Docker image of Kong OSS or Kong Enterprise if we have it). In DB-less mode, prepare a `kong.yml` declarative config. At minimum, define a Service for the CRM API (pointing to `localhost:3001` if NestJS runs there) and a Route (e.g., paths: `/api/v1/` or just `/`).

   * Enable the **OpenID Connect plugin** on that route or service. Configure it with Keycloak specifics: set `issuer` to Keycloak realm URL (so Kong can fetch .well-known/openid-configuration). For now, since SPA provides tokens, configure plugin to **not initiate redirect** (there is a mode where Kong can redirect to IdP, but in our case, SPA handles login). Instead, plugin should just validate tokens (this is config like `config.auth_methods=["bearer"]`, and possibly `config.scopes_required` if we want to enforce any scope).

   * Other plugins: add a **Rate Limiting plugin** (local or cluster, e.g., 100 requests/min per consumer or per token) to the service for basic abuse prevention. Add a **Logging plugin** (file log or stdout) to capture requests.

   * Test Kong manually: Make an HTTP request to Kong’s proxy URL (e.g., `http://localhost:8000/api/v1/contacts`) without a token – Kong should respond 401 Unauthorized. Then add an Authorization header with a valid JWT (from our earlier login test) – Kong should forward it to the NestJS service and you should get data back. This validates the OIDC plugin is working (Kong will fetch Keycloak’s keys and verify the token’s signature and expiry).

9. **Configure Identity Propagation**

   * Adjust Kong plugin settings to **forward user info** to upstream. In Kong’s OIDC plugin, there are config options like `config.userinfo_headers` or similar (depending on plugin version) to send claims. If not available, we can use a **Request Transformer** plugin to take JWT claims (maybe via environment variables or by decoding token in plugin) and add them as headers. Specifically, ensure `X-User-Id` (with Keycloak `sub` claim or our internal user id if known, though Kong doesn’t know internal ID), and `X-Org-Id` (with Keycloak group or org claim) are passed. Roles can also be passed if needed for backend, e.g., `X-Roles`.

   * Secure these headers: instruct the NestJS app to trust these headers only if coming from Kong (in production this is fine as Kong sits in front). In local dev, since we might skip Kong for direct calls, ensure that when Kong is used, it does send them.

   * Update the backend to actually use these headers if present. For example, our auth guard could check `req.headers['x-org-id']` and set current org accordingly. This is an alternative to reading from token. But since in dev we might call without Kong, have a fallback to parse JWT in backend.

   * Test end-to-end: Log in via front-end (which gives JWT), front-end calls an API through Kong, Kong injects headers, backend uses those to respond. Perhaps add a temporary debug endpoint that returns what user/org it sees to validate it’s correct.

10. **decK & GitOps Setup**

    * Install **decK** CLI on the dev machine or CI environment. Use `deck dump` to get the current Kong config (or write one from scratch to match what we did). Store this in the repo under `kong/config/kong.yaml`. This file becomes the single source of truth for Kong’s desired state.

    * Test decK sync: make a small change in the YAML (like add a dummy header plugin) and run `deck diff` to see that it plans to add it, then `deck sync` to apply. Confirm the change took effect on Kong. Then remove it and sync again to ensure it’s reflected.

    * Set up a GitHub Action (or our CI) to run decK. For example, on push to main or on a specific workflow dispatch, run `deck validate` (to lint the YAML) and possibly connect to a test Kong to run `deck sync`. For production, we might not auto-sync on every push to main (maybe manual promotion), but for a staging environment we can.

    * Document the process: team members should know that to change any Kong route or plugin, they must edit the YAML and commit, not do it in Kong’s admin API directly. Also, ensure to secure Kong’s admin API (in DB-less, there might not be one, which is good for security – config only via files).

    * Lastly, integrate decK state with multiple environments if needed: e.g., separate files for dev and prod if differences (or better, parameterize via environment variables in Kong’s config for things like hostnames).

***(Outcome of Phase C: All API traffic is now intended to flow through Kong with proper auth. We have a reproducible config for Kong under source control. At this point, we effectively have the core of our new architecture in place: SSO via Keycloak, gateway enforcement via Kong. The next phases deal with codebase cleanup and multi-tenancy.)***

### **PHASE D: Automated Code Refactoring (Codemods)**

11. **Plan Codebase Refactor Passes**

    * Inventory places in code that reference the old authentication: search the code for terms like “login”, “password”, “session”, etc. Identify modules to remove (e.g., AuthController, LocalAuthService). Also find any frontend usage of these (maybe a login page component, login form, etc.).

    * Write small **codemod scripts** to remove or replace these references. For example, a script to remove imports of `LocalAuthService` and replace with Keycloak context usage (or if no equivalent, just remove calls). Another script could update API calls to no longer hit `/login` endpoint but instead assume user is logged in.

    * AST-Grep patterns: define patterns for risky code that might need review. E.g., any direct SQL queries that might not have org filter. Or any usage of now-deprecated library. These patterns can be run to produce a report for manual inspection.

    * Prepare to run **ts-migrate** if needed: if we find parts of code that are JavaScript or loosely typed, planning a migration to tighten types in those areas (especially around user/org models) can help avoid runtime errors.

12. **Execute Codemods**

    * Run the **auth removal codemod**: This will e.g. delete the import of Twenty’s auth library (if they had one) from all files, remove any related code blocks (like `<LoginPage>` component usage). Use jscodeshift for this – test on a single file first, then run across the project.

    * Replace guards: If certain routes had a custom auth guard, swap it with Keycloak guard usage. Possibly use codemod to insert `@UseGuards(KeycloakGuard)` on controllers that need it.

    * **Insert orgId parameter** where needed: for instance, if service functions like `getContacts(userId)` exist, and we want `getContacts(orgId, userId)` instead, we can use a codemod to add `orgId` as the first argument in all calls. This is tricky but feasible by AST matching function calls to known service methods. Alternatively, we might decide to get orgId from context instead of passing everywhere to minimize signature changes. Evaluate and apply accordingly.

    * Format & Lint: After codemods, run the linter and prettier on the codebase to catch any leftover issues (codemods might leave some unused variables or imports). Fix those (some can be automated by lint – e.g., remove unused imports).

    * Commit these changes in batches, and run tests after each batch to isolate any breakage. E.g., commit “Remove legacy auth code”, run tests. Then commit “Add orgId param in services”, run tests.

13. **Progressive Typing & Lint Enforcement**

    * Run **ts-migrate** (or manually add types) to critical modules that were previously `any` heavy. Focus on anything around security or multi-tenancy (e.g., ensure the User entity has a proper type with orgId, etc.).

    * Strengthen ESLint rules if needed to catch things like usage of any, or direct database queries. If Twenty did not already have a rule for it, consider adding a custom rule: e.g., forbid using the raw Prisma client without a tenant context. This can help maintain the discipline going forward.

    * Re-enable all tests and ensure they pass. If some tests were for old auth (like testing login), update them to reflect new auth (maybe now just test that our keycloak guard is called, or adjust to simulate a JWT). Some tests might be obsolete and can be removed if they pertained solely to removed functionality.

    * Ensure the code is consistent in style. Run `nx affected:lint` or similar to ensure no file breaks standards after our refactor.

14. **Testing & Code Review of Refactor**

    * Perform a thorough **code review** of the codemod outputs. Particularly check sensitive areas: e.g., did we accidentally remove something we still need? Are controllers still handling errors properly now that auth is external?

    * Write specific tests for the new auth: e.g., using a JWT of a certain role, call an endpoint and expect 403 if role not allowed. This ensures nest-keycloak guard and role decorators are working.

    * If possible, incorporate **mutation testing** (using Stryker or similar) for key security functions to ensure our tests catch tampering (this is a stretch goal).

    * Do a quick functional test: run the app with a couple of users, ensure they can still do everything: create records, edit, etc. This is to confirm that removing auth code didn’t break some flow (like maybe an old welcome screen that no longer shows – likely fine).

    * After this phase, the codebase should be cleaner: no dead auth code, and prepared for multi-tenancy changes. All major transformations are done, setting stage for the next phase where we actually enforce multi-tenant restrictions.

### **PHASE E: Data Model & Multi-Tenancy Migration**

15. **Extend Database Schema for Multi-Tenancy**

    * Using Prisma or TypeORM migration, add the **`orgs`** table and new columns as defined in Section 8\. Write migration scripts carefully to handle existing data (if any). For instance, existing users could all be assigned to a default org if needed (since Twenty wasn’t multi-tenant, assume one implicit org).

    * Run the migration against the dev database. Verify new tables/columns are created. Update the ORM models (Prisma schema or TypeORM entities) to include these fields.

    * Adjust application logic: anywhere a new `org_id` is required (non-null), the code creating that record must supply it. E.g., when creating a new Contact, now provide `org_id`. This likely means using the current user’s org. Implement helpers to fetch `currentOrgId` from context easily, so we can add it to service calls.

    * Ensure that unique constraints that should be per-org are updated. For example, if contact emails were unique globally, maybe now they only need to be unique per org (depending on design). Adjust constraints or logic if needed to allow same email in two different orgs. Document these changes as they slightly alter the data model semantics.

16. **Synchronize Keycloak and CRM Org Data**

    * Decide how to link Keycloak groups/realms to `orgs` table. If using a single realm with groups: when we create an `org` in CRM DB, also create a group in Keycloak with a matching ID (store that in `org.keycloak_group`). Conversely, if a user logs in with a group claim and we don’t have that org, perhaps auto-create it (for safety, maybe we pre-provision orgs, but at least log a warning if mismatch).

    * Implement a script or admin endpoint to **provision an org**: It should create a row in `orgs` table, create corresponding Keycloak group, and maybe assign an admin user. This will be used by our onboarding flow later, but for now can be a manual utility.

    * If we plan realm-per-tenant, then each org might correspond to a realm – that’s more complex (one Keycloak realm per org, possibly with the same client). Likely not doing that in MVP due to overhead. So assume group model.

    * Update token validation logic: ensure the `org_id` we trust (from token or header) actually corresponds to an existing `org` in our DB. If not, either create it (if we trust Keycloak to be source of truth) or reject (to avoid ghost data). Probably okay to auto-create on first sign-in of an org from token (with some default plan).

    * Test with multiple organizations: create two orgs, each with a user in Keycloak (and each org’s user should only see their data). In the CRM DB, make some dummy data for each org. Then login as each user and confirm via the UI or API that they only retrieve their respective data. This is the key verification of multi-tenancy working.

*(Outcome of Phase E: The system is now multi-tenant at the data layer. All data operations require an org context and that context flows from Keycloak down to the DB. We have also laid groundwork for managing tenants in the system. At this point, the MVP could technically host multiple orgs manually, but we haven’t built nice UI for signup or invite – that’s Phase G – and we haven’t fully hardened the system – Phase F.)*

### **PHASE F: CI/CD, Testing & Hardening**

17. **CI/CD Pipeline Automation**

    * Finalize the CI pipeline: include steps to run all tests, lint, build both frontend and backend. Integrate the **codemod checks**: we can have a step that runs ast-grep patterns to ensure no forbidden code remains (like no direct usage of `findMany` without org filter, if we wrote such patterns).

    * Add a **license scan** step: ensure we are not introducing any library with incompatible license, and that we have headers in files if needed for GPL compliance (some projects add a comment in modified files). Possibly not critical for pipeline, but do an audit.

    * Ensure **decK integration in CI** is working: test that a dummy PR with a Kong config change triggers the decK diff properly. We might not auto-sync to production from CI (manual promotion instead), but at least have validation.

    * Set up a **dev deployment environment** (maybe a docker-compose or a Kubernetes kind cluster) where on each merge to main, we deploy the whole stack (Keycloak, Kong, NestJS, Postgres). This can be used by QA for testing integrated flows. Use automation (GitHub Actions or Jenkins, etc.) to deploy to this env.

    * Document the CI/CD process and how developers can deploy new changes. This ensures a smooth path from code to running service.

18. **Monitoring & Logging Implementation**

    * Deploy or configure a **Prometheus** instance scraping metrics from Kong and the NestJS app. Kong exposes metrics if configured (with a plugin or the Enterprise version’s Vitals, or via StatsD). If not straightforward, skip detailed metrics for MVP, but at least ensure we can get basic stats from logs.

    * The backend (NestJS) can use a middleware or interceptor to record request timings and outcomes. Integrate with a monitoring system or at least print them in logs.

    * Set up **Loki or ELK** for centralized logging if part of our platform. At minimum, ensure container logs can be aggregated. Use structured logging (JSON) including fields for `orgId`, `userId` on each log entry for traceability. We might implement a NestJS interceptor to append `orgId` to the logger’s context.

    * Configure Keycloak to send logs as well (Keycloak can log admin events – those can help audit user management actions). Possibly integrate those logs similarly.

    * Establish basic **alerting rules**: e.g., if 5 consecutive login failures, or if API error rate \> 5% for 5 minutes. These can be set up later, but define what we want to monitor for a stable MVP (mostly performance and errors).

19. **Quality Assurance (QA) and Security Testing**

    * **Test Plan:** QA team (or developers in absence of dedicated QA) should execute test cases covering: user login/logout, token expiration (does it auto-refresh?), role-based access (e.g., a user without admin role should be forbidden from an admin API), multi-tenant isolation (User A in Org1 should not see Org2 data). Create test accounts in Keycloak for various roles and orgs to perform these tests.

    * **Automate integration tests:** Possibly use a tool like Postman or Cypress to simulate a user journey: login via Keycloak (maybe we can programmatically obtain a token), call a sequence of APIs (create contact, list contacts, attempt cross-tenant access by modifying token or using another token, etc.). Ensure all behavior is as expected (success where it should, forbidden where it should).

    * **Security Review:** Have security-focused team members attempt common attacks: JWT tampering (e.g., change `org_id` in a JWT and re-sign with our own key – should be impossible without our private key, but test), direct calls to backend bypassing Kong with an invalid token (the NestJS guard should catch it), SQL injection attempts in any new code (since we added org filters, ensure using parameters properly). Also run a vulnerability scan on dependencies (Keycloak and Kong are well-known, just ensure up to date).

    * **Performance Test (if time):** Simulate multiple concurrent users (maybe 100\) performing typical actions to see if any bottlenecks or memory issues arise. Particularly observe Keycloak CPU during login and Kong latency during heavy API calls. This helps ensure our config (like token cache in Kong) is tuned.

*(By end of Phase F, we should have a stable, well-tested application that is ready for final feature additions and go-live. We’ve ironed out most issues by this point and set up infrastructure to catch regressions quickly.)*

### **PHASE G: Tenant Onboarding & SaaS Platform Features**

20. **Self-Service Signup & Tenant Creation**

    * Implement a **public signup page** (likely separate from the main app, could even be a simple Next.js page or form) where a new customer can register their organization. This will collect org name, admin user email, and password (or we can generate a temp password and send email invite).

    * When the form is submitted, backend will:

      * Create a new `orgs` record in the database with the provided org name and default plan (e.g., “Free”).

      * Use Keycloak Admin API to create a new user for the admin (with email/username given) in our realm. If using groups, create a new group for the org and add the user to that group, and give them an “admin” role.

      * If email verification is desired, set the user as required to verify email and send a verification email (Keycloak can handle verification emails if configured with SMTP). If not, we can mark email verified for simplicity in MVP.

      * Optionally, generate an initial password or invite link. If not doing email flow, we might just set a password and return it (not very secure). Better to send an invite email with a link for the user to set their password via Keycloak’s reset password. This might require Keycloak SMTP config.

    * After creation, either automatically log the user in (if we somehow have credentials) or direct them to Keycloak to login with their new account (or to check email). This flow can be tricky, so for MVP we might choose a simpler approach like: admin signs up with a password \-\> we create user with that password (since it’s a public form, but ensure using https). This is less ideal security-wise (transmitting password) but easier. Alternatively, we implement a quick “set your password” step.

    * Test the signup flow end-to-end: start with no account, sign up as a new org, then log in and see an empty CRM ready to use. Ensure that behind the scenes, the org and user exist as expected in both Keycloak and our DB.

21. **Tenant Administration Portal & Multi-Org Support**

    * In the CRM web app, create a **“Organization Settings”** section accessible to org admins. This page allows:

      * Viewing the list of users in the org and their roles.

      * Inviting a new user: admin enters email, optionally role. Our front-end calls our backend API to invite user. The backend uses Keycloak admin API to create the user (and sends them an email invite via Keycloak or by sending a registration link). Also possibly creates a CRM user entry (or will be created on first login as per earlier design).

      * Removing or disabling a user from the org: backend either removes the user or more likely removes their Keycloak group membership (so they no longer have access to this org’s data). Possibly keep them in Keycloak if they might join another org, but since one realm, one user can be in multiple groups.

      * Assigning roles: if we have roles like “Admin”, “User”, allow toggling those roles per user (which correspond to Keycloak roles).

      * Perhaps display organization API keys or other settings if relevant (not in MVP likely).

    * Ensure that these admin APIs themselves are secured (only org admin role can call them).

    * Implement UI for **organization switching** if a user belongs to multiple orgs. Perhaps in the header, show a dropdown of org names. When switched, the app should refresh data for that org. Since our backend is already always scoped by the token’s org claim, the mechanism could be: have user select an org, then obtain a new token from Keycloak for that org (if using groups, that might require an action... Alternatively, we might allow a user to have a session with multiple orgs? Not straightforward with single realm single login).

      * A simpler approach: restrict one user \= one org for MVP to avoid complexity. But we did plan for multi-org support possibly. If allowed: Keycloak could include all group memberships in token; we then maybe default to one org context at a time. We could allow user to switch by storing a currentOrg in app state and including that org’s ID in API calls or as a header. But we prefer relying on token.

      * One solution: Issue a new token when switching org by having user re-login specifying the org. Or maintain separate user accounts per org (not great UX).

      * Given time, likely MVP will assume users are single-org. If multi-org needed, we might implement a naive approach: user has a dropdown, switching sets a cookie or header that backend uses to override org (but that weakens security because token still contains all orgs). Alternatively, create separate accounts per org for now.

    * For completeness, note: If multi-org is too complex, we explicitly disallow for MVP (i.e., an email can only sign up once, to get access to another org they need separate email or wait for multi-org feature later).

    * Test the admin portal: as an admin, invite a new user, ensure that user can accept invite and log in. Remove a user, ensure they can’t log in anymore (Keycloak might need their account disabled or group removed). Try non-admin cannot access admin UI.

22. **Usage Metering & Plan Enforcement**

    * Implement basic tracking of usage per tenant. For example, whenever a significant event happens (contact created, deal won, API call made), log it to a new **Usage** table with org\_id, event type, timestamp. This can be aggregated later for billing or monitoring. At minimum, track number of records (contacts, etc.) an org has and maybe monthly API calls count.

    * On the front-end, maybe display a simple usage widget for admins (“You have 95/1000 contacts used in Free Plan” if we have such limits). This requires we define plan limits: e.g., Free plan \= up to 1000 contacts, no custom workflows; Pro plan \= unlimited, etc. For MVP, we may not strictly enforce limits beyond possibly read-only warnings.

    * Integrate a **payment gateway** for upgrading plan (if in scope): e.g., integrate Stripe checkout. Possibly provide an “Upgrade” button that goes to a Stripe checkout page for a subscription. This involves backend endpoints to create Stripe customer and subscription, and webhook to handle payment success (upgrading the org’s plan in DB).

    * Given MVP timeline, we might not complete Stripe integration, but we should include a placeholder: e.g., an Upgrade button that says “Contact Sales to Upgrade” or triggers a mock payment flow that just changes plan in DB for testing.

    * Ensure that if an org is on Free and hits a limit, the system responds appropriately: maybe disable further creation or show an alert. Full enforcement can be soft (just warnings) in MVP to avoid cutting off users erroneously.

    * Add tasks to periodically aggregate usage (or do on the fly). Not critical to MVP operation but needed before billing cycle.

*(Phase G delivers the SaaS-specific features that turn a multi-tenant capable app into a user-friendly SaaS product: users can sign up on their own, admins can manage their org, and we have the beginnings of a billing model. After this, we’re ready to launch.)*

### **PHASE H: Go-Live, Documentation & Handover**

23. **Release Management & Deployment**

    * Prepare the **production environment**: set up Keycloak on a secure, highly-available cluster (or managed Keycloak service) with our domain (e.g., auth.ourcrm.com). Set up Kong in the production cluster, configured via our decK files (pointing Kong to prod Keycloak and backend). Set up the NestJS backend and front-end hosting. Ensure environment variables (database URLs, Keycloak client secrets if any, etc.) are configured.

    * Do a **staging deployment** first: simulate production with perhaps a subset of data. Have a few internal users test end-to-end on staging, including multi-tenant scenarios, to be as close to real as possible.

    * Create a checklist and execute a **cut-over plan**: what day/time to deploy, how to handle any existing data (if migrating some customers from another system, ensure data import is done), etc. We don’t have legacy users since this is MVP of new product, so mostly straightforward.

    * Rollout: Tag a release in git (v1.0.0), generate release notes summarizing changes. Deploy to production. Monitor logs and metrics closely in the initial hours.

    * Ensure a **rollback plan** is in place: for example, if something goes horribly wrong, we can revert DNS to an under-maintenance page or roll back images to a previous known state (here previous state might be nothing if new launch, but have a plan to disable signups temporarily if needed).

    * After deployment, do sanity checks: can new users sign up, can existing test user log in, etc., on the live environment.

24. **Documentation & Training**

    * Write a **“Getting Started for Developers”** document for this project. Include how to set up the dev environment, how auth is structured now (Keycloak & Kong usage), where things live (e.g., “all Kong config in `kong/` folder, Keycloak realm config in `docs/keycloak-realm.json` if we export one”). This will help new team members or open-source contributors to understand the repo.

    * Document the **Ops Runbook** for the system: how to add a new tenant manually (e.g., via Keycloak admin, or through our signup if it fails), how to scale components (increase Kong nodes, etc.), steps for rotating certificates or integrating with our CI. Also, instructions for debugging issues – e.g., if a user can’t login, check Keycloak realm settings or if Kong is returning 401, maybe the JWKS is wrong, etc.

    * Create an **AGPL/GPL Compliance archive** (if needed): a zip of the source code or a link to the GitHub repo where source is available, so that we can provide it to any user on request, fulfilling license requirements. Document the process for updating this with each release. Possibly incorporate it into the build (like build an `opensource.zip` artifact with relevant source).

    * End-user documentation: Update any user guides or tooltips in the app to reflect changes (e.g., “Use your company SSO account to log in” instead of “set a password”). For admins, write a short guide on how to use the new Org Management features and what multi-tenancy means (like reassure that data is isolated). Possibly an FAQ for common questions (security, data ownership, etc., since clients will care).

25. **Post-Launch Support & Monitoring**

    * Set up a period of heightened monitoring for the first week. Assign team members on rotation to watch alerts and respond quickly. For example, if an error spikes or the service goes down at 3 AM, who will be alerted (on-call schedule).

    * Monitor **Key metrics** (from KPIs): login success rate, any 500 errors in logs, response times. If issues arise (e.g., tokens expire too quickly, causing many refreshes), fine-tune configurations (maybe extend token TTL or fix refresh logic).

    * Gather user feedback from initial tenants. Are they experiencing any confusion with SSO? Are invites working for them? Use this feedback for quick fixes.

    * Plan regular **maintenance tasks**: e.g., schedule monthly (or as appropriate) merges from Twenty upstream to incorporate their bug fixes. When doing so, carefully diff our changes vs theirs to merge without overriding our multi-tenant logic. Possibly contribute back improvements if allowed (since our modifications are open-source, maybe Twenty community would take some changes like bug fixes, though our changes are quite specific).

    * Prepare a support procedure for new tenants: e.g., a checklist when a new customer signs up – verify their org is created properly, reach out with a welcome email, etc. This is more business process, but important to ensure smooth onboarding.

*(Phase H concludes with the system live as a SaaS product and knowledge transferred to relevant teams. We will have a retrospective to document lessons and stabilize the platform.)*

---

### **Milestone Review Checklist**

Before concluding the project, ensure all major objectives are met:

* **Complete Auth Replacement:** All legacy authentication code paths have been removed or disabled (verify no `/login` or password endpoints exist). ✅ *Check:* Grep the code for any occurrence of old auth logic – should find none.

* **100% API Gatekeeping:** Kong Gateway is in front of all client API traffic. Direct access to the backend is not used in production. Every API route requires a valid JWT (tested via attempts without token resulting in 401). We confirm that without going through Kong (or without proper token), the backend denies access. Kong is configured with OIDC and working as expected.

* **SSO Functionality:** Users can log in via Keycloak seamlessly. Onboarded users get created in our DB upon first login automatically. Logging out from the app logs out of Keycloak (or at least clears session). New user invite flows via Keycloak email are working.

* **Multi-Tenant Data Isolation:** Verified through tests that no cross-tenant data is accessible. Each API response only contains data for the authenticated user’s org. Attempting to circumvent (e.g., changing an `id` to one from another org) results in not found or forbidden. We even enabled RLS at the DB as a safety net (if implemented) to double-protect this [crunchydata.com](https://www.crunchydata.com/blog/row-level-security-for-tenants-in-postgres#:~:text=Conclusion).

* **Org Management Features:** Admins can invite and remove users, and those actions correctly propagate to Keycloak (e.g., invite creates Keycloak account). The org switcher (if multi-org enabled) works to toggle context without mixing data.

* **Kong GitOps in Place:** Kong config is fully managed via decK and stored in the repo. We tested that making a change via Git updates Kong after CI runs. Kong’s runtime config matches our `kong.yaml`. No manual hacks were needed in production.

* **Quality Gates Passing:** All lint checks, tests (unit/integration), and security scans are passing in CI. CI pipeline is green, indicating code quality is maintained. ESLint/Prettier rules are enforced on push and developers adhere to them.

* **Documentation Completed:** All necessary docs (developer docs, user guides, runbooks) are written and reviewed. They are accessible to the team or users as appropriate (e.g., in a Wiki or PDF sent to customers for user guide).

* **License Compliance Done:** Our modified source code for GPL parts is packaged or published, fulfilling our obligations. We have a notice in-app (maybe in About dialog or docs) that source is available and instructions to obtain it.

*(Only when all the above are checked off do we consider the project truly complete and ready for broader rollout.)*

---

This phased approach is engineered to allow **parallel workstreams** and to reduce blockers. For example, while backend engineers integrate Keycloak (Phase B), DevOps can start on Kong (Phase C), and in parallel, some team members can begin writing codemods (Phase D) on a branch. Regular integration and testing at each milestone will catch regressions early. Each phase’s completion yields a demonstrable increment (e.g., after Phase B, “login with Keycloak works”; after Phase C, “API calls protected by Kong”; after Phase E, “multi-tenant data model in place”). We will hold review meetings at the end of each phase to show progress (e.g., a short demo of a user logging in and using the system through Kong after Phase C). This ensures visibility and confidence as we march towards the MVP.

By the end of Phase H, we expect to have a **refactored, secure, multi-tenant SaaS CRM** that retains all the strengths of Twenty’s feature set but is re-architected for our cloud platform. We will then be ready to onboard our first set of customers and gather feedback for iterative improvements. **MVP launch is the beginning, not the end** – but with this strong architectural foundation, we’ll be well-positioned to build on it, whether by adding more CRM features or further SaaS capabilities (like advanced billing, analytics, etc.) in future iterations.

# **OneCRM CRM Refactor & SaaS Enablement: Developer Tasks & Agent Roles**

## **I. MCP (Modular Control Plane) Servers & Clients**

### **MCP Servers to Deploy**

* **MCP Orchestrator:**

  * Central coordinator for triggering workflows, deployments, codemods, test runs.

* **MCP API Gateway Monitor:**

  * Watches Kong API Gateway for config changes, health, and API contract enforcement.

* **MCP Auth Server Proxy:**

  * Mediates identity sync and SSO between Keycloak and the application.

* **MCP Test Runner:**

  * Automates running unit, integration, and regression test suites on code or environment change.

* **MCP Codemod Executor:**

  * Executes and tracks codemods for refactoring across monorepo branches.

* **MCP Multi-Tenant Provisioner:**

  * Orchestrates tenant onboarding, migration scripts, and org-wide config (sync with Keycloak).

* **MCP Monitoring/Telemetry Collector:**

  * Aggregates logs, metrics, and agent signals from all SaaS modules for observability.

### **MCP Clients/Endpoints**

* **Frontend Client:**

  * SPA agent, responsible for calling backend via Kong, session handling, reporting telemetry.

* **Backend Client:**

  * Handles API requests, multi-tenancy checks, org\_id enforcement, and sync with MCP Auth Server.

* **Script Runner:**

  * Triggers one-off utilities, migrations, codemods, or hotfixes from the MCP Orchestrator.

* **Test Harness:**

  * Automated E2E, integration, mutation test launcher; runs on code PR or at set intervals.

---

## **II. AI-Powered Agents: Role-Based Development & Testing Automation**

### **A. Development Agents**

**1\. Code Refactoring Agent**

* Role: Automates renaming, legacy auth stripping, `org_id` propagation, and code hygiene.

* Tasks:

  * Parse codebase for all “twenty” identifiers, update to “oneCRM”.

  * Replace/remap legacy auth flows with Keycloak SSO context.

  * Inject `org_id` (tenant context) into all API layers.

**2\. API Contract Agent**

* Role: Ensures every backend endpoint matches OpenAPI spec; auto-generates/updates OpenAPI YAML.

* Tasks:

  * Scan NestJS controllers for missing or mismatched OpenAPI decorators.

  * Run contract tests for backend vs frontend, flagging discrepancies.

  * Autogenerate OpenAPI spec on code merge.

**3\. Type Safety & Linting Agent**

* Role: Enforces Zod schemas, detects TypeScript type mismatches.

* Tasks:

  * Run Zod type checks on API payloads and service boundaries.

  * Scan/patch all modules for unsafe `any` usage.

  * Block merges with type errors.

**4\. Monorepo Structure/Discipline Agent**

* Role: Watches for directory drift, duplicate files, or improper code in wrong layers.

* Tasks:

  * Flag code outside `/frontend`, `/backend`, `/mobile`, `/scripts`, `/docs`.

  * Report/auto-refactor duplicate file names or business logic misplaced in frontend/backend.

**5\. MCP Integration Agent**

* Role: Verifies MCP endpoints, agent heartbeat, and inter-service events.

* Tasks:

  * Check all MCP client endpoints are reachable and versioned.

  * Report broken orchestration links (e.g., between Codemod Executor and API service).

### **B. Testing & QA Agents**

**1\. TDD Enforcement Agent**

* Role: Ensures all business logic and APIs have corresponding, passing tests before merge.

* Tasks:

  * Block PRs missing new or updated tests.

  * Run coverage checks and require ≥90% coverage for approval.

  * Suggest missing edge/corner cases.

**2\. Regression Watchdog Agent**

* Role: Replays baseline user flows, flags UI/API regressions post-refactor.

* Tasks:

  * Run recorded E2E tests on each major refactor (e.g., after codemods or multi-tenancy insert).

  * Capture/compare results, flag deviations.

**3\. Security/Secrets Agent**

* Role: Monitors for accidental credential commits, token leaks, or unsafe debug code.

* Tasks:

  * Scan for `.env`, API keys, hardcoded secrets on PR.

  * Alert/block merge on detection.

**4\. Multi-Tenancy Sentinel Agent**

* Role: Simulates data access attempts across org boundaries.

* Tasks:

  * Attempt forbidden access scenarios (e.g., OrgA user fetching OrgB data).

  * Report/auto-create test cases for any access violation found.

**5\. CI/CD Pipeline Health Agent**

* Role: Monitors all pipeline jobs (tests, codemods, deployments).

* Tasks:

  * Alert on failed builds, flaky tests, or drift between code and deployed config (e.g., Kong, Keycloak).

---

## **III. Developer Task List: Execution Flow**

### **1\. MCP Server/Client Bootstrapping**

* Deploy MCP Orchestrator, Auth Proxy, Gateway Monitor, and Test Runner.

* Integrate MCP Test Runner and Codemod Executor with CI (GitHub Actions, etc.).

* Establish event hooks between all MCP modules and the Nx monorepo.

### **2\. MCP-AI Agent Integration**

* Configure all development and QA AI agents to operate on relevant repo directories.

* Set up webhooks or agent triggers on PR events, merges, and deployments.

* Ensure agent reports are written to CI logs and block merges as per critical failures.

### **3\. Development/Refactor Tasks**

* Run Codemod Executor for full codebase auth/branding/multi-tenancy refactor.

* Use Type Safety Agent to enforce Zod and TS strict mode.

* Migrate OpenAPI spec coverage with API Contract Agent.

* Monitor all tasks for file placement/duplication with Structure Discipline Agent.

### **4\. Testing/Release Tasks**

* Execute TDD/coverage checks with QA/TDD Agent on every code merge.

* Run Regression Watchdog Agent post-major refactor and before milestone releases.

* Multi-Tenancy Sentinel runs isolation checks before each deployment.

* Security Agent audits for secrets and credential leaks.

* CI/CD Health Agent watches all jobs for pipeline or config failures.

---

## **IV. Deliverable Example**

**“Upon each PR, the MCP orchestrator triggers the Codemod Executor and Type Safety Agent. The Codemod Executor replaces all legacy 'twenty' branding and injects `org_id` parameters, while the Type Safety Agent runs Zod schema validation and flags any TS mismatch. The MCP Test Runner launches all unit, integration, and regression tests. The Multi-Tenancy Sentinel executes simulated cross-tenant attacks to verify isolation. All agent results are logged to the CI dashboard and PR thread; merges are blocked if any agent flags a critical issue.”**

---

**Recommendation:**  
 Integrate these agents early—**automation is your quality moat.** Assign leads for each agent/role, but ensure shared responsibility for passing agent health. This approach guarantees every code change is tested, reviewed, type-safe, secure, and tenant-ready—**no exceptions, no regressions.**

## **OneCRM MCP-Agent Orchestration Workflow**

mermaid

CopyEdit

`flowchart TD`

    `subgraph Developers`

      `Dev1((Developer))`

      `Dev2((Developer))`

    `end`

    `subgraph "MCP Control Plane"`

      `MCPOrch(MCP Orchestrator)`

      `MCPAuth(MCP Auth Proxy)`

      `MCPKong(MCP API Gateway Monitor)`

      `MCPCodemod(MCP Codemod Executor)`

      `MCPTest(MCP Test Runner)`

      `MCPTenant(MCP Multi-Tenant Provisioner)`

      `MCPMonitor(MCP Telemetry Collector)`

    `end`

    `subgraph "Monorepo / Source"`

      `Frontend[Frontend (Next.js)]`

      `Backend[Backend (NestJS)]`

      `Scripts[Scripts & Tools]`

      `Docs[Docs]`

    `end`

    `subgraph "CI/CD Pipeline"`

      `AgentRefactor[Code Refactoring Agent]`

      `AgentAPI[API Contract Agent]`

      `AgentType[Type Safety Agent]`

      `AgentStruct[Monorepo Structure Agent]`

      `AgentMCP[MCP Integration Agent]`

      `AgentTDD[TDD Enforcement Agent]`

      `AgentRegr[Regression Watchdog]`

      `AgentSec[Security/Secrets Agent]`

      `AgentMT[Multi-Tenancy Sentinel]`

      `AgentCICD[CI/CD Health Agent]`

    `end`

    `subgraph Infra`

      `Kong[Kong API Gateway]`

      `Keycloak[Keycloak SSO]`

      `DB[(PostgreSQL)]`

      `Metrics[Monitoring/Prometheus]`

    `end`

    `Dev1 -- "Push PR" --> MCPOrch`

    `Dev2 -- "Push PR" --> MCPOrch`

    `MCPOrch -- "Trigger" --> MCPCodemod`

    `MCPOrch -- "Trigger" --> MCPTest`

    `MCPOrch -- "Notify" --> MCPKong`

    `MCPOrch -- "Notify" --> MCPAuth`

    `MCPOrch -- "Sync" --> MCPMonitor`

    `MCPCodemod -- "Codemods" --> Frontend`

    `MCPCodemod -- "Codemods" --> Backend`

    `MCPCodemod -- "Codemods" --> Scripts`

    `MCPTest -- "Test Jobs" --> CI/CD Pipeline`

    `CI/CD Pipeline -- "Checks" --> AgentRefactor`

    `CI/CD Pipeline -- "Checks" --> AgentAPI`

    `CI/CD Pipeline -- "Checks" --> AgentType`

    `CI/CD Pipeline -- "Checks" --> AgentStruct`

    `CI/CD Pipeline -- "Checks" --> AgentMCP`

    `CI/CD Pipeline -- "Checks" --> AgentTDD`

    `CI/CD Pipeline -- "Checks" --> AgentRegr`

    `CI/CD Pipeline -- "Checks" --> AgentSec`

    `CI/CD Pipeline -- "Checks" --> AgentMT`

    `CI/CD Pipeline -- "Checks" --> AgentCICD`

    `AgentRefactor -- "Report/Block" --> MCPOrch`

    `AgentAPI -- "Report/Block" --> MCPOrch`

    `AgentType -- "Report/Block" --> MCPOrch`

    `AgentStruct -- "Report/Block" --> MCPOrch`

    `AgentMCP -- "Report/Block" --> MCPOrch`

    `AgentTDD -- "Report/Block" --> MCPOrch`

    `AgentRegr -- "Report/Block" --> MCPOrch`

    `AgentSec -- "Report/Block" --> MCPOrch`

    `AgentMT -- "Report/Block" --> MCPOrch`

    `AgentCICD -- "Report/Block" --> MCPOrch`

    `MCPKong -- "Sync / Validate" --> Kong`

    `MCPAuth -- "Sync / Validate" --> Keycloak`

    `MCPTenant -- "Provision / Sync" --> DB`

    `MCPMonitor -- "Metrics/Logs" --> Metrics`

    `Frontend -- "Deploy" --> Kong`

    `Backend -- "Deploy" --> Kong`

    `Kong -- "Authz" --> Keycloak`

    `Kong -- "Proxy API" --> Backend`

    `Backend -- "DB Ops" --> DB`

    `MCPMonitor -- "Health" --> MCPOrch`

    `style MCPOrch fill:#e0f2fe,stroke:#0369a1,stroke-width:2px`

    `style CI/CD Pipeline fill:#f1f5f9,stroke:#a1a1aa,stroke-width:1px`

    `style AgentRefactor fill:#fef3c7`

    `style AgentAPI fill:#fef3c7`

    `style AgentType fill:#fef3c7`

    `style AgentStruct fill:#fef3c7`

    `style AgentMCP fill:#fef3c7`

    `style AgentTDD fill:#dcfce7`

    `style AgentRegr fill:#dcfce7`

    `style AgentSec fill:#f3e8ff`

    `style AgentMT fill:#f3e8ff`

    `style AgentCICD fill:#f3e8ff`

---

## **How It Works (Narrative):**

1. **Developer PR/Code Change:**  
    Any code push/PR triggers the MCP Orchestrator.

2. **Orchestration:**  
    The MCP Orchestrator coordinates:

   * **Codemod Executor**: Bulk refactors codebase for branding, multi-tenancy, etc.

   * **Test Runner**: Launches all relevant test suites.

   * **Auth/Edge Monitors**: Ensures Keycloak/Kong are synchronized and configs are valid.

   * **Provisioner**: For tenant onboarding, schema migration.

3. **CI/CD & Agents:**  
    The CI pipeline invokes **AI-powered agents** for:

   * **Refactor/Type/Contract/Structure checks** (fail/auto-fix/block if required)

   * **Testing enforcement, regression checks**

   * **Multi-tenancy, security, and CI health validation**

4. **Infra Sync:**  
    On successful validation, Kong and Keycloak are updated, services are deployed, and telemetry is collected for ongoing monitoring.

5. **Automated Blocking/Reporting:**  
    Agents feedback into the orchestrator; any “red flag” blocks merge/deployment, forcing issues to be resolved before code is accepted.

6. **Feedback Loop:**  
    Telemetry and agent reports inform the MCP Orchestrator for continuous improvement, and health checks are visible in dashboards.

---

**Opinion:**  
 This agentic, orchestrated pipeline **guarantees discipline, security, multi-tenancy, and reliability**—every push is scrutinized, auto-corrected, and validated by specialized agents. This is the minimum standard for any serious SaaS platform and the only way to scale safely in a modern engineering organization.

