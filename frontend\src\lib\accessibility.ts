// Accessibility utilities and helpers

/**
 * Generate unique IDs for accessibility attributes
 */
export function generateId(prefix = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Announce text to screen readers
 */
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  if (typeof window === 'undefined') return;

  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message;

  document.body.appendChild(announcement);

  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Focus management utilities
 */
export class FocusManager {
  private static focusStack: HTMLElement[] = [];

  /**
   * Trap focus within an element
   */
  static trapFocus(element: HTMLElement) {
    const focusableElements = this.getFocusableElements(element);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement.focus();
        }
      }
    };

    element.addEventListener('keydown', handleKeyDown);
    firstElement.focus();

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
    };
  }

  /**
   * Save current focus and restore later
   */
  static saveFocus() {
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
      this.focusStack.push(activeElement);
    }
  }

  /**
   * Restore previously saved focus
   */
  static restoreFocus() {
    const element = this.focusStack.pop();
    if (element && element.focus) {
      element.focus();
    }
  }

  /**
   * Get all focusable elements within a container
   */
  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors)) as HTMLElement[];
  }
}

/**
 * Keyboard navigation utilities
 */
export class KeyboardNavigation {
  /**
   * Handle arrow key navigation for lists
   */
  static handleListNavigation(
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void
  ) {
    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        newIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
        break;
      case 'ArrowUp':
        event.preventDefault();
        newIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
      default:
        return;
    }

    onIndexChange(newIndex);
    items[newIndex]?.focus();
  }

  /**
   * Handle grid navigation (2D)
   */
  static handleGridNavigation(
    event: KeyboardEvent,
    rows: number,
    cols: number,
    currentRow: number,
    currentCol: number,
    onPositionChange: (row: number, col: number) => void
  ) {
    let newRow = currentRow;
    let newCol = currentCol;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        newRow = currentRow < rows - 1 ? currentRow + 1 : 0;
        break;
      case 'ArrowUp':
        event.preventDefault();
        newRow = currentRow > 0 ? currentRow - 1 : rows - 1;
        break;
      case 'ArrowRight':
        event.preventDefault();
        newCol = currentCol < cols - 1 ? currentCol + 1 : 0;
        break;
      case 'ArrowLeft':
        event.preventDefault();
        newCol = currentCol > 0 ? currentCol - 1 : cols - 1;
        break;
      default:
        return;
    }

    onPositionChange(newRow, newCol);
  }
}

/**
 * ARIA utilities
 */
export class AriaUtils {
  /**
   * Set ARIA attributes for expanded/collapsed state
   */
  static setExpanded(element: HTMLElement, expanded: boolean) {
    element.setAttribute('aria-expanded', expanded.toString());
  }

  /**
   * Set ARIA attributes for selected state
   */
  static setSelected(element: HTMLElement, selected: boolean) {
    element.setAttribute('aria-selected', selected.toString());
  }

  /**
   * Set ARIA attributes for pressed state
   */
  static setPressed(element: HTMLElement, pressed: boolean) {
    element.setAttribute('aria-pressed', pressed.toString());
  }

  /**
   * Set ARIA live region
   */
  static setLiveRegion(element: HTMLElement, politeness: 'off' | 'polite' | 'assertive' = 'polite') {
    element.setAttribute('aria-live', politeness);
    element.setAttribute('aria-atomic', 'true');
  }

  /**
   * Set ARIA describedby relationship
   */
  static setDescribedBy(element: HTMLElement, describedByIds: string[]) {
    element.setAttribute('aria-describedby', describedByIds.join(' '));
  }

  /**
   * Set ARIA labelledby relationship
   */
  static setLabelledBy(element: HTMLElement, labelledByIds: string[]) {
    element.setAttribute('aria-labelledby', labelledByIds.join(' '));
  }
}

/**
 * Color contrast utilities
 */
export class ColorContrast {
  /**
   * Calculate relative luminance of a color
   */
  static getRelativeLuminance(r: number, g: number, b: number): number {
    const [rs, gs, bs] = [r, g, b].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
  }

  /**
   * Calculate contrast ratio between two colors
   */
  static getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
    const l1 = this.getRelativeLuminance(...color1);
    const l2 = this.getRelativeLuminance(...color2);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);

    return (lighter + 0.05) / (darker + 0.05);
  }

  /**
   * Check if contrast ratio meets WCAG standards
   */
  static meetsWCAG(contrastRatio: number, level: 'AA' | 'AAA' = 'AA', size: 'normal' | 'large' = 'normal'): boolean {
    const thresholds = {
      AA: { normal: 4.5, large: 3 },
      AAA: { normal: 7, large: 4.5 },
    };

    return contrastRatio >= thresholds[level][size];
  }
}

/**
 * React hooks for accessibility
 */

/**
 * Hook for managing focus trap
 */
export function useFocusTrap(isActive: boolean) {
  const containerRef = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    if (!isActive || !containerRef.current) return;

    FocusManager.saveFocus();
    const cleanup = FocusManager.trapFocus(containerRef.current);

    return () => {
      cleanup?.();
      FocusManager.restoreFocus();
    };
  }, [isActive]);

  return containerRef;
}

/**
 * Hook for managing ARIA live announcements
 */
export function useAnnouncement() {
  const announce = React.useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announceToScreenReader(message, priority);
  }, []);

  return announce;
}

/**
 * Hook for keyboard navigation
 */
export function useKeyboardNavigation(items: HTMLElement[], onSelectionChange?: (index: number) => void) {
  const [currentIndex, setCurrentIndex] = React.useState(0);

  const handleKeyDown = React.useCallback((event: KeyboardEvent) => {
    KeyboardNavigation.handleListNavigation(event, items, currentIndex, (newIndex) => {
      setCurrentIndex(newIndex);
      onSelectionChange?.(newIndex);
    });
  }, [items, currentIndex, onSelectionChange]);

  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { currentIndex, setCurrentIndex };
}
