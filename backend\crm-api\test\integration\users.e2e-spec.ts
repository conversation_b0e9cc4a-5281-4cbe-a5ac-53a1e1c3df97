import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../src/app.module';
import { UsersService } from '../../src/modules/users/users.service';

describe('Users API (e2e)', () => {
  let app: INestApplication;
  let usersService: UsersService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    usersService = moduleFixture.get<UsersService>(UsersService);
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/users/profile (GET)', () => {
    it('should return user profile in development mode', async () => {
      if (process.env.NODE_ENV === 'development') {
        // Mock user data for testing
        const mockUser = {
          id: 'test-user-id',
          keycloakSub: 'test-keycloak-sub',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          orgId: 'test-org-id',
          role: 'user',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        jest.spyOn(usersService, 'findById').mockResolvedValue(mockUser as any);

        return request(app.getHttpServer())
          .get('/users/profile')
          .expect(200)
          .expect((res) => {
            expect(res.body).toHaveProperty('email', '<EMAIL>');
            expect(res.body).toHaveProperty('role', 'user');
          });
      }
    });
  });

  describe('/users/profile/:keycloakSub (GET)', () => {
    it('should return user by Keycloak subject', async () => {
      if (process.env.NODE_ENV === 'development') {
        const mockUser = {
          id: 'test-user-id',
          keycloakSub: 'test-keycloak-sub',
          email: '<EMAIL>',
          orgId: 'test-org-id',
          role: 'user',
        };

        jest.spyOn(usersService, 'findByKeycloakSub').mockResolvedValue(mockUser as any);

        return request(app.getHttpServer())
          .get('/users/profile/test-keycloak-sub')
          .expect(200)
          .expect((res) => {
            expect(res.body).toHaveProperty('keycloakSub', 'test-keycloak-sub');
          });
      }
    });

    it('should return null for non-existent user', async () => {
      if (process.env.NODE_ENV === 'development') {
        jest.spyOn(usersService, 'findByKeycloakSub').mockResolvedValue(null);

        return request(app.getHttpServer())
          .get('/users/profile/non-existent-sub')
          .expect(200)
          .expect((res) => {
            expect(res.body).toBeNull();
          });
      }
    });
  });

  describe('Multi-tenant isolation', () => {
    it('should filter users by organization', async () => {
      if (process.env.NODE_ENV === 'development') {
        const mockUsers = [
          {
            id: 'user1',
            email: '<EMAIL>',
            orgId: 'org1',
            role: 'user',
          },
          {
            id: 'user2',
            email: '<EMAIL>',
            orgId: 'org1',
            role: 'admin',
          },
        ];

        jest.spyOn(usersService, 'findByOrganization').mockResolvedValue(mockUsers as any);

        return request(app.getHttpServer())
          .get('/users/organization/org1')
          .set('X-Org-Id', 'org1')
          .expect(200)
          .expect((res) => {
            expect(res.body).toHaveLength(2);
            expect(res.body.every((user: any) => user.orgId === 'org1')).toBe(true);
          });
      }
    });
  });
});
