apiVersion: v1
kind: ConfigMap
metadata:
  name: onecrm-config
  namespace: onecrm
  labels:
    app: onecrm
data:
  # API Configuration
  api-url: "https://api.onecrm.example.com"
  cors-origin: "https://onecrm.example.com"
  
  # Keycloak Configuration
  keycloak-url: "https://auth.onecrm.example.com"
  keycloak-realm: "onecrm"
  keycloak-client-id: "onecrm-frontend"
  
  # Database Configuration
  postgres-host: "postgres-service"
  postgres-port: "5432"
  postgres-db: "onecrm"
  
  # Redis Configuration
  redis-host: "redis-service"
  redis-port: "6379"
  
  # Application Configuration
  log-level: "info"
  jwt-expiration: "1d"
  
  # Feature Flags
  enable-analytics: "true"
  enable-notifications: "true"
  enable-file-upload: "true"
  
  # Rate Limiting
  rate-limit-window: "15"
  rate-limit-max: "100"
  
  # File Upload
  max-file-size: "10485760"
  allowed-file-types: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: onecrm-config-staging
  namespace: onecrm-staging
  labels:
    app: onecrm
    environment: staging
data:
  # API Configuration
  api-url: "https://api-staging.onecrm.example.com"
  cors-origin: "https://staging.onecrm.example.com"
  
  # Keycloak Configuration
  keycloak-url: "https://auth-staging.onecrm.example.com"
  keycloak-realm: "onecrm-staging"
  keycloak-client-id: "onecrm-frontend-staging"
  
  # Database Configuration
  postgres-host: "postgres-service"
  postgres-port: "5432"
  postgres-db: "onecrm_staging"
  
  # Redis Configuration
  redis-host: "redis-service"
  redis-port: "6379"
  
  # Application Configuration
  log-level: "debug"
  jwt-expiration: "1h"
  
  # Feature Flags
  enable-analytics: "true"
  enable-notifications: "false"
  enable-file-upload: "true"
  
  # Rate Limiting
  rate-limit-window: "15"
  rate-limit-max: "1000"
  
  # File Upload
  max-file-size: "10485760"
  allowed-file-types: "jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx"
