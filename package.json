{"name": "onecrm", "version": "1.0.0", "description": "OneCRM - Multi-tenant CRM based on Twenty with Keycloak SSO and Kong Gateway", "private": true, "workspaces": ["frontend", "backend/*", "libs/*", "scripts"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:frontend:hot": "cd frontend && npm run dev:hot", "dev:backend": "cd backend/crm-api && npm run dev", "dev:nx": "nx run-many --target=dev --all --parallel", "build": "nx run-many --target=build --all", "test": "nx run-many --target=test --all", "lint": "nx run-many --target=lint --all", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "nx run-many --target=typecheck --all", "clean": "nx reset && rm -rf node_modules && rm -rf */node_modules && rm -rf */*/node_modules", "setup": "npm install && nx run-many --target=setup --all"}, "devDependencies": {"@nx/eslint-plugin": "^17.0.0", "@nx/js": "^17.0.0", "@nx/next": "^17.0.0", "@nx/node": "^17.0.0", "@nx/workspace": "^17.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "nx": "^17.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/onecrm.git"}, "keywords": ["crm", "multi-tenant", "keycloak", "kong", "nextjs", "<PERSON><PERSON><PERSON>", "typescript"], "author": "OneCRM Team", "license": "GPL-3.0", "dependencies": {"concurrently": "^8.2.2", "next-auth": "^4.24.11", "sqlite3": "^5.1.7"}}