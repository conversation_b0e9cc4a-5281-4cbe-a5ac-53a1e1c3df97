import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { ClientProviders } from '../components/providers/ClientProviders';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'OneCRM - Enterprise Customer Relationship Management',
  description: 'Modern, multi-tenant CRM system built for enterprise teams',
  keywords: ['CRM', 'Customer Management', 'Sales Pipeline', 'Enterprise'],
  authors: [{ name: 'OneCRM Team' }],
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ClientProviders>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
