#!/bin/bash

# OneCRM Staging Deployment Script
# This script handles the complete staging deployment process

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT="staging"
NAMESPACE="onecrm-staging"
TAG=${1:-latest}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-""}

echo -e "${BLUE}🚀 OneCRM Staging Deployment${NC}"
echo "=================================================="
echo "Environment: $ENVIRONMENT"
echo "Namespace: $NAMESPACE"
echo "Tag: $TAG"
echo ""

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_section "🔍 Checking Prerequisites"
    
    # Check Docker
    if ! command_exists docker; then
        echo -e "${RED}❌ Docker is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker available${NC}"
    
    # Check kubectl
    if ! command_exists kubectl; then
        echo -e "${RED}❌ kubectl is not installed${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ kubectl available${NC}"
    
    # Check Kubernetes connection
    if ! kubectl cluster-info >/dev/null 2>&1; then
        echo -e "${RED}❌ Cannot connect to Kubernetes cluster${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Kubernetes cluster connected${NC}"
    
    # Check if staging namespace exists
    if ! kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Creating staging namespace...${NC}"
        kubectl create namespace $NAMESPACE
    fi
    echo -e "${GREEN}✅ Staging namespace ready${NC}"
}

# Build and tag images for staging
build_staging_images() {
    print_section "🏗️  Building Staging Images"
    
    echo "Building backend image for staging..."
    docker build -t onecrm/backend:$TAG-staging \
        --build-arg NODE_ENV=staging \
        -f backend/Dockerfile.prod backend/
    
    echo "Building frontend image for staging..."
    docker build -t onecrm/frontend:$TAG-staging \
        --build-arg NODE_ENV=staging \
        -f frontend/Dockerfile.prod frontend/
    
    echo -e "${GREEN}✅ Staging images built successfully${NC}"
}

# Push images to registry (if configured)
push_staging_images() {
    if [ -n "$DOCKER_REGISTRY" ]; then
        print_section "📤 Pushing Staging Images"
        
        # Tag for registry
        docker tag onecrm/backend:$TAG-staging $DOCKER_REGISTRY/onecrm/backend:$TAG-staging
        docker tag onecrm/frontend:$TAG-staging $DOCKER_REGISTRY/onecrm/frontend:$TAG-staging
        
        # Push to registry
        docker push $DOCKER_REGISTRY/onecrm/backend:$TAG-staging
        docker push $DOCKER_REGISTRY/onecrm/frontend:$TAG-staging
        
        echo -e "${GREEN}✅ Images pushed to registry${NC}"
    else
        echo -e "${YELLOW}⚠️  No registry configured, using local images${NC}"
    fi
}

# Deploy staging secrets
deploy_staging_secrets() {
    print_section "🔐 Deploying Staging Secrets"
    
    # Generate staging secrets if they don't exist
    if [ ! -f "infrastructure/kubernetes/secrets-staging.yaml" ]; then
        echo "Generating staging secrets..."
        ./scripts/manage-secrets.sh staging generate
    fi
    
    # Apply secrets
    kubectl apply -f infrastructure/kubernetes/secrets-staging.yaml
    
    echo -e "${GREEN}✅ Staging secrets deployed${NC}"
}

# Deploy staging configuration
deploy_staging_config() {
    print_section "⚙️  Deploying Staging Configuration"
    
    # Apply staging configmap
    kubectl apply -f infrastructure/kubernetes/configmap.yaml
    
    echo -e "${GREEN}✅ Staging configuration deployed${NC}"
}

# Deploy PostgreSQL for staging
deploy_staging_postgres() {
    print_section "🗄️  Deploying Staging PostgreSQL"
    
    # Create PostgreSQL deployment for staging
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: $NAMESPACE
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: postgres-user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: postgres-password
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: postgres-db
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: $NAMESPACE
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF

    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ PostgreSQL deployed and ready${NC}"
}

# Deploy Redis for staging
deploy_staging_redis() {
    print_section "🔴 Deploying Staging Redis"
    
    # Create Redis deployment for staging
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: $NAMESPACE
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command: ["redis-server", "--requirepass", "\$(REDIS_PASSWORD)"]
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: redis-password
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: $NAMESPACE
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
EOF

    # Wait for Redis to be ready
    echo "Waiting for Redis to be ready..."
    kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Redis deployed and ready${NC}"
}

# Deploy Keycloak for staging
deploy_staging_keycloak() {
    print_section "🔑 Deploying Staging Keycloak"
    
    # Create Keycloak deployment for staging
    cat << EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: keycloak
  namespace: $NAMESPACE
  labels:
    app: keycloak
spec:
  replicas: 1
  selector:
    matchLabels:
      app: keycloak
  template:
    metadata:
      labels:
        app: keycloak
    spec:
      containers:
      - name: keycloak
        image: quay.io/keycloak/keycloak:22.0.1
        ports:
        - containerPort: 8080
        env:
        - name: KEYCLOAK_ADMIN
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: keycloak-admin
        - name: KEYCLOAK_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: keycloak-admin-password
        - name: KC_DB
          value: "postgres"
        - name: KC_DB_URL
          value: "********************************************************"
        - name: KC_DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: postgres-user
        - name: KC_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: postgres-password
        - name: KC_HOSTNAME
          value: "auth-staging.onecrm.example.com"
        command: ["start-dev"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: keycloak-service
  namespace: $NAMESPACE
spec:
  selector:
    app: keycloak
  ports:
  - port: 8080
    targetPort: 8080
EOF

    # Wait for Keycloak to be ready
    echo "Waiting for Keycloak to be ready..."
    kubectl wait --for=condition=ready pod -l app=keycloak -n $NAMESPACE --timeout=600s
    
    echo -e "${GREEN}✅ Keycloak deployed and ready${NC}"
}

# Run database migrations
run_staging_migrations() {
    print_section "🔄 Running Database Migrations"
    
    # Create migration job
    cat << EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: onecrm-migration-$(date +%s)
  namespace: $NAMESPACE
spec:
  template:
    spec:
      containers:
      - name: migration
        image: onecrm/backend:$TAG-staging
        command: ["npm", "run", "migration:run"]
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: database-url
      restartPolicy: Never
  backoffLimit: 3
EOF

    # Wait for migration to complete
    echo "Waiting for migrations to complete..."
    kubectl wait --for=condition=complete job -l job-name=onecrm-migration -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Database migrations completed${NC}"
}

# Deploy staging backend
deploy_staging_backend() {
    print_section "🖥️  Deploying Staging Backend"
    
    # Update backend deployment for staging
    sed "s|onecrm/backend:latest|onecrm/backend:$TAG-staging|g; s|namespace: onecrm|namespace: $NAMESPACE|g" \
        infrastructure/kubernetes/backend-deployment.yaml | kubectl apply -f -
    
    # Wait for backend to be ready
    echo "Waiting for backend to be ready..."
    kubectl rollout status deployment/onecrm-backend -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Backend deployed and ready${NC}"
}

# Deploy staging frontend
deploy_staging_frontend() {
    print_section "🌐 Deploying Staging Frontend"
    
    # Update frontend deployment for staging
    sed "s|onecrm/frontend:latest|onecrm/frontend:$TAG-staging|g; s|namespace: onecrm|namespace: $NAMESPACE|g" \
        infrastructure/kubernetes/frontend-deployment.yaml | kubectl apply -f -
    
    # Wait for frontend to be ready
    echo "Waiting for frontend to be ready..."
    kubectl rollout status deployment/onecrm-frontend -n $NAMESPACE --timeout=300s
    
    echo -e "${GREEN}✅ Frontend deployed and ready${NC}"
}

# Run smoke tests
run_smoke_tests() {
    print_section "🧪 Running Smoke Tests"
    
    # Get service URLs
    local backend_url="http://$(kubectl get svc onecrm-backend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}'):8000"
    local frontend_url="http://$(kubectl get svc onecrm-frontend-service -n $NAMESPACE -o jsonpath='{.spec.clusterIP}'):3000"
    
    echo "Testing backend health endpoint..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f $backend_url/health; then
        echo -e "${GREEN}✅ Backend health check passed${NC}"
    else
        echo -e "${RED}❌ Backend health check failed${NC}"
        exit 1
    fi
    
    echo "Testing frontend health endpoint..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-frontend -- wget --spider -q $frontend_url/api/health; then
        echo -e "${GREEN}✅ Frontend health check passed${NC}"
    else
        echo -e "${RED}❌ Frontend health check failed${NC}"
        exit 1
    fi
    
    echo "Testing database connectivity..."
    if kubectl exec -n $NAMESPACE deployment/onecrm-backend -- curl -f $backend_url/health/db; then
        echo -e "${GREEN}✅ Database connectivity test passed${NC}"
    else
        echo -e "${RED}❌ Database connectivity test failed${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All smoke tests passed${NC}"
}

# Display staging status
show_staging_status() {
    print_section "📊 Staging Deployment Status"
    
    echo "Pods in staging:"
    kubectl get pods -n $NAMESPACE
    
    echo -e "\nServices in staging:"
    kubectl get services -n $NAMESPACE
    
    echo -e "\nDeployment status:"
    kubectl get deployments -n $NAMESPACE
    
    echo -e "\nStaging URLs:"
    echo "Frontend: https://staging.onecrm.example.com"
    echo "API: https://api-staging.onecrm.example.com"
    echo "Auth: https://auth-staging.onecrm.example.com"
    
    echo -e "\nTo access staging environment:"
    echo "kubectl port-forward -n $NAMESPACE svc/onecrm-frontend-service 3000:3000"
    echo "kubectl port-forward -n $NAMESPACE svc/onecrm-backend-service 8000:8000"
}

# Main deployment function
main() {
    echo -e "${BLUE}Starting staging deployment...${NC}"
    
    check_prerequisites
    build_staging_images
    push_staging_images
    deploy_staging_secrets
    deploy_staging_config
    deploy_staging_postgres
    deploy_staging_redis
    deploy_staging_keycloak
    run_staging_migrations
    deploy_staging_backend
    deploy_staging_frontend
    run_smoke_tests
    show_staging_status
    
    echo -e "\n${GREEN}🎉 Staging deployment completed successfully!${NC}"
    echo -e "${YELLOW}💡 Run './scripts/test-staging.sh' to execute comprehensive tests${NC}"
}

# Run main function
main
