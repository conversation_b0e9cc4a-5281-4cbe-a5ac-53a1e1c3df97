import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { Deal } from '../../deals/entities/deal.entity';
import { Contact } from '../../contacts/entities/contact.entity';
import { Company } from '../../companies/entities/company.entity';
import { User } from '../../users/entities/user.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { DataSource } from 'typeorm';

describe('DealsController (Integration)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let orgId: string;
  let userId: string;
  let contactId: string;
  let companyId: string;

  const testOrganization = {
    name: 'Test Organization',
    domain: 'test.com',
  };

  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Test',
    lastName: 'User',
  };

  const testCompany = {
    name: 'Test Company',
    industry: 'Technology',
    size: 'medium',
  };

  const testContact = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
  };

  const testDeal = {
    title: 'Enterprise Software Deal',
    amount: 50000,
    stage: 'qualification',
    probability: 75,
    expectedCloseDate: '2024-12-31',
    source: 'website',
    type: 'new-business',
    priority: 'high',
    description: 'Large enterprise software deal',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
    await app.close();
  });

  beforeEach(async () => {
    // Clear deals before each test
    await dataSource.getRepository(Deal).delete({ orgId });
  });

  async function setupTestData() {
    // Create test organization
    const orgRepository = dataSource.getRepository(Organization);
    const organization = orgRepository.create(testOrganization);
    const savedOrg = await orgRepository.save(organization);
    orgId = savedOrg.id;

    // Create test user
    const userRepository = dataSource.getRepository(User);
    const user = userRepository.create({
      ...testUser,
      orgId,
      role: 'admin',
    });
    const savedUser = await userRepository.save(user);
    userId = savedUser.id;

    // Create test company
    const companyRepository = dataSource.getRepository(Company);
    const company = companyRepository.create({
      ...testCompany,
      orgId,
    });
    const savedCompany = await companyRepository.save(company);
    companyId = savedCompany.id;

    // Create test contact
    const contactRepository = dataSource.getRepository(Contact);
    const contact = contactRepository.create({
      ...testContact,
      orgId,
    });
    const savedContact = await contactRepository.save(contact);
    contactId = savedContact.id;

    authToken = 'Bearer test-jwt-token';
  }

  async function cleanupTestData() {
    await dataSource.getRepository(Deal).delete({ orgId });
    await dataSource.getRepository(Contact).delete({ orgId });
    await dataSource.getRepository(Company).delete({ orgId });
    await dataSource.getRepository(User).delete({ orgId });
    await dataSource.getRepository(Organization).delete({ id: orgId });
  }

  describe('POST /deals', () => {
    it('should create a new deal', async () => {
      const dealData = {
        ...testDeal,
        contactId,
        companyId,
        ownerId: userId,
      };

      const response = await request(app.getHttpServer())
        .post('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(dealData)
        .expect(201);

      expect(response.body).toMatchObject({
        title: testDeal.title,
        amount: testDeal.amount,
        stage: testDeal.stage,
        probability: testDeal.probability,
        source: testDeal.source,
        type: testDeal.type,
        priority: testDeal.priority,
        orgId,
      });

      expect(response.body.id).toBeDefined();
      expect(response.body.contact).toMatchObject({
        id: contactId,
        firstName: testContact.firstName,
        lastName: testContact.lastName,
      });
      expect(response.body.company).toMatchObject({
        id: companyId,
        name: testCompany.name,
      });
    });

    it('should validate required fields', async () => {
      const invalidDeal = {
        // Missing title and amount
        stage: 'lead',
      };

      await request(app.getHttpServer())
        .post('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(invalidDeal)
        .expect(400);
    });

    it('should validate amount is positive', async () => {
      const invalidDeal = {
        ...testDeal,
        amount: -1000,
      };

      await request(app.getHttpServer())
        .post('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(invalidDeal)
        .expect(400);
    });

    it('should validate probability range', async () => {
      const invalidDeal = {
        ...testDeal,
        probability: 150,
      };

      await request(app.getHttpServer())
        .post('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(invalidDeal)
        .expect(400);
    });

    it('should return 404 for non-existent contact', async () => {
      const dealWithInvalidContact = {
        ...testDeal,
        contactId: 'non-existent-id',
      };

      await request(app.getHttpServer())
        .post('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(dealWithInvalidContact)
        .expect(404);
    });
  });

  describe('GET /deals', () => {
    beforeEach(async () => {
      // Create test deals
      const dealRepository = dataSource.getRepository(Deal);
      const deals = [
        { ...testDeal, title: 'Deal 1', amount: 10000, stage: 'lead' },
        { ...testDeal, title: 'Deal 2', amount: 20000, stage: 'qualification' },
        { ...testDeal, title: 'Deal 3', amount: 30000, stage: 'proposal' },
      ].map(deal => dealRepository.create({ ...deal, orgId }));

      await dealRepository.save(deals);
    });

    it('should return paginated deals', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ page: 1, limit: 2 })
        .expect(200);

      expect(response.body).toMatchObject({
        deals: expect.any(Array),
        total: 3,
        page: 1,
        limit: 2,
        totalPages: 2,
      });

      expect(response.body.deals).toHaveLength(2);
    });

    it('should filter deals by stage', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ stage: 'qualification' })
        .expect(200);

      expect(response.body.deals).toHaveLength(1);
      expect(response.body.deals[0].stage).toBe('qualification');
    });

    it('should search deals by title', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ search: 'Deal 2' })
        .expect(200);

      expect(response.body.deals).toHaveLength(1);
      expect(response.body.deals[0].title).toBe('Deal 2');
    });

    it('should sort deals by amount', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ sortBy: 'amount', sortOrder: 'DESC' })
        .expect(200);

      const amounts = response.body.deals.map(d => d.amount);
      expect(amounts).toEqual([30000, 20000, 10000]);
    });
  });

  describe('PUT /deals/:id/stage', () => {
    let dealId: string;

    beforeEach(async () => {
      const dealRepository = dataSource.getRepository(Deal);
      const deal = dealRepository.create({ ...testDeal, orgId });
      const savedDeal = await dealRepository.save(deal);
      dealId = savedDeal.id;
    });

    it('should update deal stage', async () => {
      const response = await request(app.getHttpServer())
        .put(`/deals/${dealId}/stage`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send({ stage: 'proposal' })
        .expect(200);

      expect(response.body.stage).toBe('proposal');
    });

    it('should validate stage transition', async () => {
      // First move to closed-won
      await request(app.getHttpServer())
        .put(`/deals/${dealId}/stage`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send({ stage: 'closed-won' })
        .expect(200);

      // Try to move back to earlier stage (should fail)
      await request(app.getHttpServer())
        .put(`/deals/${dealId}/stage`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send({ stage: 'lead' })
        .expect(400);
    });

    it('should return 404 for non-existent deal', async () => {
      await request(app.getHttpServer())
        .put('/deals/non-existent-id/stage')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send({ stage: 'proposal' })
        .expect(404);
    });
  });

  describe('GET /deals/pipeline', () => {
    beforeEach(async () => {
      // Create deals in different stages
      const dealRepository = dataSource.getRepository(Deal);
      const deals = [
        { ...testDeal, title: 'Lead Deal', stage: 'lead', amount: 10000 },
        { ...testDeal, title: 'Qualified Deal 1', stage: 'qualification', amount: 20000 },
        { ...testDeal, title: 'Qualified Deal 2', stage: 'qualification', amount: 30000 },
        { ...testDeal, title: 'Proposal Deal', stage: 'proposal', amount: 40000 },
      ].map(deal => dealRepository.create({ ...deal, orgId }));

      await dealRepository.save(deals);
    });

    it('should return pipeline data grouped by stage', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals/pipeline')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(200);

      expect(response.body.pipeline).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            stage: 'lead',
            count: 1,
            totalValue: 10000,
            deals: expect.any(Array),
          }),
          expect.objectContaining({
            stage: 'qualification',
            count: 2,
            totalValue: 50000,
            deals: expect.any(Array),
          }),
          expect.objectContaining({
            stage: 'proposal',
            count: 1,
            totalValue: 40000,
            deals: expect.any(Array),
          }),
        ])
      );
    });
  });

  describe('GET /deals/forecast', () => {
    beforeEach(async () => {
      // Create deals with different probabilities and close dates
      const dealRepository = dataSource.getRepository(Deal);
      const deals = [
        {
          ...testDeal,
          title: 'Q4 Deal 1',
          amount: 50000,
          probability: 75,
          expectedCloseDate: new Date('2024-12-15'),
        },
        {
          ...testDeal,
          title: 'Q4 Deal 2',
          amount: 30000,
          probability: 50,
          expectedCloseDate: new Date('2024-12-30'),
        },
        {
          ...testDeal,
          title: 'Q1 Deal',
          amount: 40000,
          probability: 25,
          expectedCloseDate: new Date('2025-03-15'),
        },
      ].map(deal => dealRepository.create({ ...deal, orgId }));

      await dealRepository.save(deals);
    });

    it('should return revenue forecast', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals/forecast')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ months: 6 })
        .expect(200);

      expect(response.body).toMatchObject({
        totalPipeline: expect.any(Number),
        weightedPipeline: expect.any(Number),
        monthlyForecast: expect.any(Array),
      });

      expect(response.body.totalPipeline).toBe(120000);
      expect(response.body.weightedPipeline).toBe(62500); // (50000*0.75) + (30000*0.5) + (40000*0.25)
    });
  });

  describe('GET /deals/stats', () => {
    beforeEach(async () => {
      // Create deals with various properties
      const dealRepository = dataSource.getRepository(Deal);
      const deals = [
        { ...testDeal, stage: 'qualification', amount: 10000 },
        { ...testDeal, stage: 'proposal', amount: 20000 },
        { ...testDeal, stage: 'closed-won', amount: 30000 },
        { ...testDeal, stage: 'closed-lost', amount: 15000 },
      ].map(deal => dealRepository.create({ ...deal, orgId }));

      await dealRepository.save(deals);
    });

    it('should return deal statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/deals/stats')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(200);

      expect(response.body).toMatchObject({
        totalDeals: 4,
        totalRevenue: 30000, // Only closed-won deals
        averageAmount: expect.any(Number),
        conversionRate: expect.any(Number),
        byStage: expect.objectContaining({
          qualification: 1,
          proposal: 1,
          'closed-won': 1,
          'closed-lost': 1,
        }),
      });
    });
  });

  describe('Authentication and Authorization', () => {
    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .get('/deals')
        .expect(401);
    });

    it('should return 403 without organization header', async () => {
      await request(app.getHttpServer())
        .get('/deals')
        .set('Authorization', authToken)
        .expect(403);
    });

    it('should not access deals from different organization', async () => {
      // Create deal in current org
      const dealRepository = dataSource.getRepository(Deal);
      const deal = dealRepository.create({ ...testDeal, orgId });
      const savedDeal = await dealRepository.save(deal);

      // Try to access with different org ID
      await request(app.getHttpServer())
        .get(`/deals/${savedDeal.id}`)
        .set('Authorization', authToken)
        .set('X-Org-Id', 'different-org-id')
        .expect(404);
    });
  });
});
