apiVersion: apps/v1
kind: Deployment
metadata:
  name: onecrm-backend
  namespace: onecrm
  labels:
    app: onecrm-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: onecrm-backend
  template:
    metadata:
      labels:
        app: onecrm-backend
        version: v1
    spec:
      containers:
      - name: backend
        image: onecrm/backend:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "8000"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: jwt-secret
        - name: KEYCLOAK_URL
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-url
        - name: K<PERSON><PERSON>CLOAK_REALM
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-realm
        - name: <PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID
          valueFrom:
            configMapKeyRef:
              name: onecrm-config
              key: keycloak-client-id
        - name: KEYCLOAK_SECRET
          valueFrom:
            secretKeyRef:
              name: onecrm-secrets
              key: keycloak-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      securityContext:
        fsGroup: 1001
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - onecrm-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: onecrm-backend-service
  namespace: onecrm
  labels:
    app: onecrm-backend
spec:
  selector:
    app: onecrm-backend
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: onecrm-backend-hpa
  namespace: onecrm
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: onecrm-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
