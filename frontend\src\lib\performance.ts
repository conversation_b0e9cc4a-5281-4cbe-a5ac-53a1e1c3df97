// Performance monitoring and optimization utilities

interface PerformanceMetrics {
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
}

interface ApiPerformanceMetrics {
  url: string;
  method: string;
  duration: number;
  status: number;
  timestamp: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private apiMetrics: ApiPerformanceMetrics[] = [];
  private observer?: PerformanceObserver;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeWebVitals();
      this.initializeNavigationTiming();
    }
  }

  /**
   * Initialize Web Vitals monitoring
   */
  private initializeWebVitals() {
    // Monitor Core Web Vitals
    if ('PerformanceObserver' in window) {
      // Largest Contentful Paint
      this.observeMetric('largest-contentful-paint', (entry) => {
        this.metrics.lcp = entry.startTime;
        this.reportMetric('LCP', entry.startTime);
      });

      // First Input Delay
      this.observeMetric('first-input', (entry) => {
        this.metrics.fid = entry.processingStart - entry.startTime;
        this.reportMetric('FID', this.metrics.fid);
      });

      // Cumulative Layout Shift
      this.observeMetric('layout-shift', (entry) => {
        if (!entry.hadRecentInput) {
          this.metrics.cls = (this.metrics.cls || 0) + entry.value;
          this.reportMetric('CLS', this.metrics.cls);
        }
      });
    }
  }

  /**
   * Initialize Navigation Timing monitoring
   */
  private initializeNavigationTiming() {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        this.metrics.ttfb = navigation.responseStart - navigation.requestStart;
        this.metrics.fcp = navigation.loadEventEnd - navigation.navigationStart;
        
        this.reportMetric('TTFB', this.metrics.ttfb);
        this.reportMetric('FCP', this.metrics.fcp);
      }
    });
  }

  /**
   * Observe specific performance metrics
   */
  private observeMetric(type: string, callback: (entry: any) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(callback);
      });
      
      observer.observe({ type, buffered: true });
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  /**
   * Report metric to console in development
   */
  private reportMetric(name: string, value: number) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`📊 ${name}: ${Math.round(value)}ms`);
    }
  }

  /**
   * Track API performance
   */
  trackApiCall(url: string, method: string, duration: number, status: number) {
    const metric: ApiPerformanceMetrics = {
      url,
      method,
      duration,
      status,
      timestamp: Date.now(),
    };

    this.apiMetrics.push(metric);

    // Keep only last 100 API calls
    if (this.apiMetrics.length > 100) {
      this.apiMetrics.shift();
    }

    // Log slow API calls in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`🐌 Slow API call: ${method} ${url} took ${duration}ms`);
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get API performance metrics
   */
  getApiMetrics(): ApiPerformanceMetrics[] {
    return [...this.apiMetrics];
  }

  /**
   * Get performance summary
   */
  getSummary() {
    const apiStats = this.getApiStats();
    
    return {
      webVitals: this.metrics,
      api: apiStats,
      recommendations: this.getRecommendations(),
    };
  }

  /**
   * Get API statistics
   */
  private getApiStats() {
    if (this.apiMetrics.length === 0) return null;

    const durations = this.apiMetrics.map(m => m.duration);
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    const errorRate = this.apiMetrics.filter(m => m.status >= 400).length / this.apiMetrics.length;

    return {
      totalCalls: this.apiMetrics.length,
      averageDuration: Math.round(avgDuration),
      maxDuration: Math.round(maxDuration),
      errorRate: Math.round(errorRate * 100),
    };
  }

  /**
   * Get performance recommendations
   */
  private getRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.metrics.lcp && this.metrics.lcp > 2500) {
      recommendations.push('LCP is slow. Consider optimizing images and reducing server response time.');
    }

    if (this.metrics.fid && this.metrics.fid > 100) {
      recommendations.push('FID is high. Consider reducing JavaScript execution time.');
    }

    if (this.metrics.cls && this.metrics.cls > 0.1) {
      recommendations.push('CLS is high. Ensure images and ads have dimensions set.');
    }

    const apiStats = this.getApiStats();
    if (apiStats && apiStats.averageDuration > 500) {
      recommendations.push('API calls are slow. Consider caching or optimizing backend.');
    }

    if (apiStats && apiStats.errorRate > 5) {
      recommendations.push('High API error rate detected. Check network connectivity and backend health.');
    }

    return recommendations;
  }

  /**
   * Cleanup observers
   */
  cleanup() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// Singleton instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * Higher-order component for performance monitoring
 */
export function withPerformanceMonitoring<T extends object>(
  WrappedComponent: React.ComponentType<T>,
  componentName: string
) {
  return function PerformanceMonitoredComponent(props: T) {
    React.useEffect(() => {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const renderTime = endTime - startTime;
        
        if (process.env.NODE_ENV === 'development' && renderTime > 16) {
          console.warn(`⚠️ ${componentName} render took ${Math.round(renderTime)}ms`);
        }
      };
    }, []);

    return React.createElement(WrappedComponent, props);
  };
}

/**
 * Hook for measuring component performance
 */
export function usePerformanceMetrics(componentName: string) {
  const [renderTime, setRenderTime] = React.useState<number>(0);
  const startTimeRef = React.useRef<number>(0);

  React.useEffect(() => {
    startTimeRef.current = performance.now();
  }, []);

  React.useEffect(() => {
    const endTime = performance.now();
    const duration = endTime - startTimeRef.current;
    setRenderTime(duration);

    if (process.env.NODE_ENV === 'development' && duration > 16) {
      console.warn(`⚠️ ${componentName} render took ${Math.round(duration)}ms`);
    }
  });

  return { renderTime };
}

/**
 * Debounced function for performance optimization
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Lazy loading utility for components
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>,
  fallback?: React.ReactElement
) {
  const LazyComponent = React.lazy(importFunc);

  return function LazyWrapper(props: React.ComponentProps<T>) {
    return React.createElement(
      React.Suspense,
      { fallback: fallback || React.createElement('div', null, 'Loading...') },
      React.createElement(LazyComponent, props)
    );
  };
}
