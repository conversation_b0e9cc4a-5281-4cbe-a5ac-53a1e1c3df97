'use client';

import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Button,
  IconButton,
  Tabs,
  Tab,
  useTheme,
  useMediaQuery,
  Skeleton,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Assignment as AssignmentIcon,
  AttachMoney as MoneyIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Event as EventIcon,
  ArrowForward as ArrowForwardIcon,
} from '@mui/icons-material';
import { useRouter } from 'next/navigation';
import useSWR from 'swr';
import { ResponsiveCard } from '../common/ResponsiveCard';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mobile-dashboard-tabpanel-${index}`}
      aria-labelledby={`mobile-dashboard-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const QuickStatCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  onClick?: () => void;
  isLoading?: boolean;
}> = ({ title, value, icon, color, onClick, isLoading }) => {
  if (isLoading) {
    return (
      <Card sx={{ cursor: onClick ? 'pointer' : 'default' }}>
        <CardContent sx={{ textAlign: 'center', py: 2 }}>
          <Skeleton variant="circular" width={40} height={40} sx={{ mx: 'auto', mb: 1 }} />
          <Skeleton variant="text" width="60%" sx={{ mx: 'auto' }} />
          <Skeleton variant="text" width="40%" sx={{ mx: 'auto' }} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      sx={{
        cursor: onClick ? 'pointer' : 'default',
        '&:hover': onClick ? { boxShadow: 4 } : {},
      }}
      onClick={onClick}
    >
      <CardContent sx={{ textAlign: 'center', py: 2 }}>
        <Avatar
          sx={{
            bgcolor: color,
            width: 40,
            height: 40,
            mx: 'auto',
            mb: 1,
          }}
        >
          {icon}
        </Avatar>
        <Typography variant="h6" component="div" fontWeight={600}>
          {value}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {title}
        </Typography>
      </CardContent>
    </Card>
  );
};

export const MobileDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const { data: dashboardData, isLoading: dashboardLoading } = useSWR('/api/dashboard/overview');
  const { data: recentContacts, isLoading: contactsLoading } = useSWR('/api/contacts?limit=5&sortBy=createdAt&sortOrder=DESC');
  const { data: recentDeals, isLoading: dealsLoading } = useSWR('/api/deals?limit=5&sortBy=createdAt&sortOrder=DESC');
  const { data: upcomingActivities, isLoading: activitiesLoading } = useSWR('/api/activities/upcoming?days=7&limit=5');

  if (!isMobile) {
    return null; // Use regular dashboard on desktop
  }

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const quickStats = [
    {
      title: 'Contacts',
      value: dashboardData?.totalContacts || 0,
      icon: <PeopleIcon />,
      color: 'primary.main',
      onClick: () => router.push('/contacts'),
    },
    {
      title: 'Companies',
      value: dashboardData?.totalCompanies || 0,
      icon: <BusinessIcon />,
      color: 'secondary.main',
      onClick: () => router.push('/companies'),
    },
    {
      title: 'Active Deals',
      value: dashboardData?.activeDeals || 0,
      icon: <TrendingUpIcon />,
      color: 'success.main',
      onClick: () => router.push('/deals'),
    },
    {
      title: 'Revenue',
      value: dashboardData?.totalRevenue ? formatCurrency(dashboardData.totalRevenue) : '$0',
      icon: <MoneyIcon />,
      color: 'warning.main',
      onClick: () => router.push('/analytics'),
    },
  ];

  return (
    <Box>
      {/* Quick Stats */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {quickStats.map((stat, index) => (
          <Grid item xs={6} sm={3} key={index}>
            <QuickStatCard {...stat} isLoading={dashboardLoading} />
          </Grid>
        ))}
      </Grid>

      {/* Tabbed Content */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                minHeight: 48,
                fontSize: '0.875rem',
              },
            }}
          >
            <Tab label="Recent" />
            <Tab label="Deals" />
            <Tab label="Tasks" />
          </Tabs>
        </Box>

        {/* Recent Contacts Tab */}
        <TabPanel value={activeTab} index={0}>
          <Box p={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight={600}>
                Recent Contacts
              </Typography>
              <Button
                size="small"
                endIcon={<ArrowForwardIcon />}
                onClick={() => router.push('/contacts')}
              >
                View All
              </Button>
            </Box>

            {contactsLoading ? (
              <Box>
                {[1, 2, 3].map((i) => (
                  <Box key={i} mb={2}>
                    <Skeleton variant="rectangular" height={80} sx={{ borderRadius: 1 }} />
                  </Box>
                ))}
              </Box>
            ) : recentContacts?.contacts?.length > 0 ? (
              <Box>
                {recentContacts.contacts.map((contact: any) => (
                  <ResponsiveCard
                    key={contact.id}
                    type="contact"
                    data={contact}
                    onView={() => router.push(`/contacts?id=${contact.id}`)}
                    compact
                  />
                ))}
              </Box>
            ) : (
              <Box textAlign="center" py={4}>
                <PeopleIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  No recent contacts
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>

        {/* Recent Deals Tab */}
        <TabPanel value={activeTab} index={1}>
          <Box p={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight={600}>
                Recent Deals
              </Typography>
              <Button
                size="small"
                endIcon={<ArrowForwardIcon />}
                onClick={() => router.push('/deals')}
              >
                View All
              </Button>
            </Box>

            {dealsLoading ? (
              <Box>
                {[1, 2, 3].map((i) => (
                  <Box key={i} mb={2}>
                    <Skeleton variant="rectangular" height={80} sx={{ borderRadius: 1 }} />
                  </Box>
                ))}
              </Box>
            ) : recentDeals?.deals?.length > 0 ? (
              <Box>
                {recentDeals.deals.map((deal: any) => (
                  <ResponsiveCard
                    key={deal.id}
                    type="deal"
                    data={deal}
                    onView={() => router.push(`/deals?id=${deal.id}`)}
                    compact
                  />
                ))}
              </Box>
            ) : (
              <Box textAlign="center" py={4}>
                <TrendingUpIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  No recent deals
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>

        {/* Upcoming Tasks Tab */}
        <TabPanel value={activeTab} index={2}>
          <Box p={2}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight={600}>
                Upcoming Tasks
              </Typography>
              <Button
                size="small"
                endIcon={<ArrowForwardIcon />}
                onClick={() => router.push('/activities')}
              >
                View All
              </Button>
            </Box>

            {activitiesLoading ? (
              <Box>
                {[1, 2, 3].map((i) => (
                  <Box key={i} mb={2}>
                    <Skeleton variant="rectangular" height={80} sx={{ borderRadius: 1 }} />
                  </Box>
                ))}
              </Box>
            ) : upcomingActivities?.length > 0 ? (
              <Box>
                {upcomingActivities.map((activity: any) => (
                  <ResponsiveCard
                    key={activity.id}
                    type="activity"
                    data={activity}
                    onView={() => router.push(`/activities?id=${activity.id}`)}
                    compact
                  />
                ))}
              </Box>
            ) : (
              <Box textAlign="center" py={4}>
                <AssignmentIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  No upcoming tasks
                </Typography>
              </Box>
            )}
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};
