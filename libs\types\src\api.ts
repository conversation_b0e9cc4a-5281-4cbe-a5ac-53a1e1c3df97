import { z } from 'zod';

// Generic API response wrapper
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema,
    message: z.string().optional(),
    timestamp: z.string(),
  });

export type ApiResponse<T> = {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
};

// Paginated response
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    success: z.boolean(),
    data: z.object({
      items: z.array(itemSchema),
      total: z.number(),
      page: z.number(),
      limit: z.number(),
      totalPages: z.number(),
    }),
    message: z.string().optional(),
    timestamp: z.string(),
  });

export type PaginatedResponse<T> = {
  success: boolean;
  data: {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
  timestamp: string;
};

// Query parameters for list endpoints
export const ListQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  filters: z.record(z.string(), z.any()).optional(),
});

export type ListQuery = z.infer<typeof ListQuerySchema>;

// API endpoints constants
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    PROFILE: '/auth/profile',
    REFRESH: '/auth/refresh',
  },
  
  // Users
  USERS: {
    BASE: '/users',
    PROFILE: (id: string) => `/users/profile/${id}`,
    BY_ORG: (orgId: string) => `/users/organization/${orgId}`,
  },
  
  // Organizations
  ORGANIZATIONS: {
    BASE: '/organizations',
    BY_ID: (id: string) => `/organizations/${id}`,
    USERS: (id: string) => `/organizations/${id}/users`,
    SETTINGS: (id: string) => `/organizations/${id}/settings`,
  },
  
  // Contacts
  CONTACTS: {
    BASE: '/contacts',
    BY_ID: (id: string) => `/contacts/${id}`,
    SEARCH: '/contacts/search',
    EXPORT: '/contacts/export',
  },
  
  // Companies
  COMPANIES: {
    BASE: '/companies',
    BY_ID: (id: string) => `/companies/${id}`,
    SEARCH: '/companies/search',
    EXPORT: '/companies/export',
  },
  
  // Deals
  DEALS: {
    BASE: '/deals',
    BY_ID: (id: string) => `/deals/${id}`,
    SEARCH: '/deals/search',
    PIPELINE: '/deals/pipeline',
    EXPORT: '/deals/export',
  },
  
  // Health check
  HEALTH: '/health',
} as const;
