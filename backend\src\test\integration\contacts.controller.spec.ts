import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import * as request from 'supertest';
import { AppModule } from '../../app.module';
import { Contact } from '../../contacts/entities/contact.entity';
import { Company } from '../../companies/entities/company.entity';
import { User } from '../../users/entities/user.entity';
import { Organization } from '../../organizations/entities/organization.entity';
import { DataSource } from 'typeorm';

describe('ContactsController (Integration)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let authToken: string;
  let orgId: string;
  let userId: string;
  let companyId: string;

  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123',
    firstName: 'Test',
    lastName: 'User',
  };

  const testOrganization = {
    name: 'Test Organization',
    domain: 'test.com',
  };

  const testCompany = {
    name: 'Test Company',
    industry: 'Technology',
    size: 'medium',
  };

  const testContact = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    title: 'Software Engineer',
    leadStatus: 'new',
    leadSource: 'website',
    tags: ['developer', 'tech'],
    notes: 'Test contact for integration testing',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        AppModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    dataSource = moduleFixture.get<DataSource>(DataSource);

    // Setup test data
    await setupTestData();
  });

  afterAll(async () => {
    // Cleanup test data
    await cleanupTestData();
    await app.close();
  });

  beforeEach(async () => {
    // Clear contacts before each test
    await dataSource.getRepository(Contact).delete({ orgId });
  });

  async function setupTestData() {
    // Create test organization
    const orgRepository = dataSource.getRepository(Organization);
    const organization = orgRepository.create(testOrganization);
    const savedOrg = await orgRepository.save(organization);
    orgId = savedOrg.id;

    // Create test user
    const userRepository = dataSource.getRepository(User);
    const user = userRepository.create({
      ...testUser,
      orgId,
      role: 'admin',
    });
    const savedUser = await userRepository.save(user);
    userId = savedUser.id;

    // Create test company
    const companyRepository = dataSource.getRepository(Company);
    const company = companyRepository.create({
      ...testCompany,
      orgId,
    });
    const savedCompany = await companyRepository.save(company);
    companyId = savedCompany.id;

    // Get auth token (mock JWT for testing)
    authToken = 'Bearer test-jwt-token';
  }

  async function cleanupTestData() {
    await dataSource.getRepository(Contact).delete({ orgId });
    await dataSource.getRepository(Company).delete({ orgId });
    await dataSource.getRepository(User).delete({ orgId });
    await dataSource.getRepository(Organization).delete({ id: orgId });
  }

  describe('POST /contacts', () => {
    it('should create a new contact', async () => {
      const response = await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(testContact)
        .expect(201);

      expect(response.body).toMatchObject({
        firstName: testContact.firstName,
        lastName: testContact.lastName,
        email: testContact.email,
        phone: testContact.phone,
        title: testContact.title,
        leadStatus: testContact.leadStatus,
        leadSource: testContact.leadSource,
        orgId,
      });

      expect(response.body.id).toBeDefined();
      expect(response.body.createdAt).toBeDefined();
      expect(response.body.updatedAt).toBeDefined();
    });

    it('should create contact with company reference', async () => {
      const contactWithCompany = {
        ...testContact,
        companyId,
      };

      const response = await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(contactWithCompany)
        .expect(201);

      expect(response.body.company).toMatchObject({
        id: companyId,
        name: testCompany.name,
      });
    });

    it('should return 400 for invalid email', async () => {
      const invalidContact = {
        ...testContact,
        email: 'invalid-email',
      };

      await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(invalidContact)
        .expect(400);
    });

    it('should return 404 for non-existent company', async () => {
      const contactWithInvalidCompany = {
        ...testContact,
        companyId: 'non-existent-id',
      };

      await request(app.getHttpServer())
        .post('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(contactWithInvalidCompany)
        .expect(404);
    });

    it('should return 401 without authentication', async () => {
      await request(app.getHttpServer())
        .post('/contacts')
        .send(testContact)
        .expect(401);
    });
  });

  describe('GET /contacts', () => {
    beforeEach(async () => {
      // Create test contacts
      const contactRepository = dataSource.getRepository(Contact);
      const contacts = [
        { ...testContact, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        { ...testContact, firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
        { ...testContact, firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>' },
      ].map(contact => contactRepository.create({ ...contact, orgId }));

      await contactRepository.save(contacts);
    });

    it('should return paginated contacts', async () => {
      const response = await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ page: 1, limit: 2 })
        .expect(200);

      expect(response.body).toMatchObject({
        contacts: expect.any(Array),
        total: 3,
        page: 1,
        limit: 2,
        totalPages: 2,
      });

      expect(response.body.contacts).toHaveLength(2);
    });

    it('should filter contacts by search query', async () => {
      const response = await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ search: 'jane' })
        .expect(200);

      expect(response.body.contacts).toHaveLength(1);
      expect(response.body.contacts[0].firstName).toBe('Jane');
    });

    it('should filter contacts by lead status', async () => {
      // Update one contact to have different status
      const contactRepository = dataSource.getRepository(Contact);
      const contact = await contactRepository.findOne({ where: { firstName: 'Jane', orgId } });
      await contactRepository.update(contact.id, { leadStatus: 'qualified' });

      const response = await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ leadStatus: 'qualified' })
        .expect(200);

      expect(response.body.contacts).toHaveLength(1);
      expect(response.body.contacts[0].leadStatus).toBe('qualified');
    });

    it('should sort contacts by specified field', async () => {
      const response = await request(app.getHttpServer())
        .get('/contacts')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .query({ sortBy: 'firstName', sortOrder: 'ASC' })
        .expect(200);

      const firstNames = response.body.contacts.map(c => c.firstName);
      expect(firstNames).toEqual(['Bob', 'Jane', 'John']);
    });
  });

  describe('GET /contacts/:id', () => {
    let contactId: string;

    beforeEach(async () => {
      const contactRepository = dataSource.getRepository(Contact);
      const contact = contactRepository.create({ ...testContact, orgId });
      const savedContact = await contactRepository.save(contact);
      contactId = savedContact.id;
    });

    it('should return contact by id', async () => {
      const response = await request(app.getHttpServer())
        .get(`/contacts/${contactId}`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(200);

      expect(response.body).toMatchObject({
        id: contactId,
        firstName: testContact.firstName,
        lastName: testContact.lastName,
        email: testContact.email,
      });
    });

    it('should return 404 for non-existent contact', async () => {
      await request(app.getHttpServer())
        .get('/contacts/non-existent-id')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(404);
    });

    it('should not return contact from different organization', async () => {
      // Create contact in different org
      const otherOrgId = 'other-org-id';
      const contactRepository = dataSource.getRepository(Contact);
      const otherContact = contactRepository.create({ ...testContact, orgId: otherOrgId });
      const savedOtherContact = await contactRepository.save(otherContact);

      await request(app.getHttpServer())
        .get(`/contacts/${savedOtherContact.id}`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(404);
    });
  });

  describe('PUT /contacts/:id', () => {
    let contactId: string;

    beforeEach(async () => {
      const contactRepository = dataSource.getRepository(Contact);
      const contact = contactRepository.create({ ...testContact, orgId });
      const savedContact = await contactRepository.save(contact);
      contactId = savedContact.id;
    });

    it('should update contact', async () => {
      const updateData = {
        firstName: 'Updated John',
        leadStatus: 'qualified',
      };

      const response = await request(app.getHttpServer())
        .put(`/contacts/${contactId}`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send(updateData)
        .expect(200);

      expect(response.body).toMatchObject({
        id: contactId,
        firstName: 'Updated John',
        leadStatus: 'qualified',
      });
    });

    it('should return 404 for non-existent contact', async () => {
      await request(app.getHttpServer())
        .put('/contacts/non-existent-id')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .send({ firstName: 'Updated' })
        .expect(404);
    });
  });

  describe('DELETE /contacts/:id', () => {
    let contactId: string;

    beforeEach(async () => {
      const contactRepository = dataSource.getRepository(Contact);
      const contact = contactRepository.create({ ...testContact, orgId });
      const savedContact = await contactRepository.save(contact);
      contactId = savedContact.id;
    });

    it('should delete contact', async () => {
      await request(app.getHttpServer())
        .delete(`/contacts/${contactId}`)
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(200);

      // Verify contact is deleted
      const contactRepository = dataSource.getRepository(Contact);
      const deletedContact = await contactRepository.findOne({ where: { id: contactId } });
      expect(deletedContact).toBeNull();
    });

    it('should return 404 for non-existent contact', async () => {
      await request(app.getHttpServer())
        .delete('/contacts/non-existent-id')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(404);
    });
  });

  describe('GET /contacts/stats', () => {
    beforeEach(async () => {
      // Create contacts with different statuses
      const contactRepository = dataSource.getRepository(Contact);
      const contacts = [
        { ...testContact, firstName: 'John1', leadStatus: 'new', leadSource: 'website' },
        { ...testContact, firstName: 'John2', leadStatus: 'qualified', leadSource: 'referral' },
        { ...testContact, firstName: 'John3', leadStatus: 'new', leadSource: 'website' },
      ].map(contact => contactRepository.create({ ...contact, orgId }));

      await contactRepository.save(contacts);
    });

    it('should return contact statistics', async () => {
      const response = await request(app.getHttpServer())
        .get('/contacts/stats')
        .set('Authorization', authToken)
        .set('X-Org-Id', orgId)
        .expect(200);

      expect(response.body).toMatchObject({
        total: 3,
        byStatus: {
          new: 2,
          qualified: 1,
        },
        bySource: {
          website: 2,
          referral: 1,
        },
        recentlyCreated: expect.any(Number),
      });
    });
  });
});
