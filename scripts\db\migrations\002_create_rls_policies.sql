-- OneCRM Row Level Security Policies
-- Migration 002: Create comprehensive RLS policies for multi-tenant isolation
-- This ensures complete data isolation between organizations

-- Enable Row Level Security on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE deals ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_usage ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate function to get current user's organization ID
CREATE OR REPLACE FUNCTION current_user_org_id() RETURNS UUID AS $$
BEGIN
    -- In development mode, allow bypass via session variable
    IF current_setting('app.environment', true) = 'development' THEN
        RETURN current_setting('app.current_org_id', true)::UUID;
    END IF;
    
    -- Get org_id from current user context (set by application)
    RETURN current_setting('app.current_org_id')::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION current_user_is_admin() RETURNS BOOLEAN AS $$
BEGIN
    -- In development mode, allow bypass
    IF current_setting('app.environment', true) = 'development' THEN
        RETURN true;
    END IF;
    
    -- Check if current user has admin role
    RETURN current_setting('app.current_user_role', true) = 'admin';
EXCEPTION
    WHEN OTHERS THEN
        RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get current user ID
CREATE OR REPLACE FUNCTION current_user_id() RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_user_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Organizations RLS Policies
-- Users can only see their own organization
CREATE POLICY organizations_tenant_isolation ON organizations
    FOR ALL
    USING (id = current_user_org_id());

-- Users can only update their own organization if they're admin
CREATE POLICY organizations_admin_update ON organizations
    FOR UPDATE
    USING (id = current_user_org_id() AND current_user_is_admin());

-- Users RLS Policies
-- Users can only see users in their organization
CREATE POLICY users_tenant_isolation ON users
    FOR ALL
    USING (org_id = current_user_org_id());

-- Only admins can create/update/delete users
CREATE POLICY users_admin_modify ON users
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id() AND current_user_is_admin());

CREATE POLICY users_admin_update ON users
    FOR UPDATE
    USING (org_id = current_user_org_id() AND (current_user_is_admin() OR id = current_user_id()));

CREATE POLICY users_admin_delete ON users
    FOR DELETE
    USING (org_id = current_user_org_id() AND current_user_is_admin());

-- Organization Invitations RLS Policies
-- Users can only see invitations for their organization
CREATE POLICY invitations_tenant_isolation ON organization_invitations
    FOR ALL
    USING (org_id = current_user_org_id());

-- Only admins can manage invitations
CREATE POLICY invitations_admin_modify ON organization_invitations
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id() AND current_user_is_admin());

CREATE POLICY invitations_admin_update ON organization_invitations
    FOR UPDATE
    USING (org_id = current_user_org_id() AND current_user_is_admin());

CREATE POLICY invitations_admin_delete ON organization_invitations
    FOR DELETE
    USING (org_id = current_user_org_id() AND current_user_is_admin());

-- Contacts RLS Policies
-- Users can only see contacts in their organization
CREATE POLICY contacts_tenant_isolation ON contacts
    FOR ALL
    USING (org_id = current_user_org_id());

-- Users can create contacts in their organization
CREATE POLICY contacts_user_create ON contacts
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id());

-- Users can update contacts they created or are assigned to, admins can update all
CREATE POLICY contacts_user_update ON contacts
    FOR UPDATE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id() OR 
            assigned_to = current_user_id()
        )
    );

-- Users can delete contacts they created, admins can delete all
CREATE POLICY contacts_user_delete ON contacts
    FOR DELETE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id()
        )
    );

-- Companies RLS Policies
-- Users can only see companies in their organization
CREATE POLICY companies_tenant_isolation ON companies
    FOR ALL
    USING (org_id = current_user_org_id());

-- Users can create companies in their organization
CREATE POLICY companies_user_create ON companies
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id());

-- Users can update companies they created or are assigned to, admins can update all
CREATE POLICY companies_user_update ON companies
    FOR UPDATE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id() OR 
            assigned_to = current_user_id()
        )
    );

-- Users can delete companies they created, admins can delete all
CREATE POLICY companies_user_delete ON companies
    FOR DELETE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id()
        )
    );

-- Deals RLS Policies
-- Users can only see deals in their organization
CREATE POLICY deals_tenant_isolation ON deals
    FOR ALL
    USING (org_id = current_user_org_id());

-- Users can create deals in their organization
CREATE POLICY deals_user_create ON deals
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id());

-- Users can update deals they created or own, admins can update all
CREATE POLICY deals_user_update ON deals
    FOR UPDATE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id() OR 
            owner_id = current_user_id()
        )
    );

-- Users can delete deals they created, admins can delete all
CREATE POLICY deals_user_delete ON deals
    FOR DELETE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            created_by = current_user_id()
        )
    );

-- Activities RLS Policies
-- Users can only see activities in their organization
CREATE POLICY activities_tenant_isolation ON activities
    FOR ALL
    USING (org_id = current_user_org_id());

-- Users can create activities in their organization
CREATE POLICY activities_user_create ON activities
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id());

-- Users can update their own activities, admins can update all
CREATE POLICY activities_user_update ON activities
    FOR UPDATE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            user_id = current_user_id()
        )
    );

-- Users can delete their own activities, admins can delete all
CREATE POLICY activities_user_delete ON activities
    FOR DELETE
    USING (
        org_id = current_user_org_id() AND (
            current_user_is_admin() OR 
            user_id = current_user_id()
        )
    );

-- Organization Usage RLS Policies
-- Only admins can see usage data
CREATE POLICY usage_admin_only ON organization_usage
    FOR ALL
    USING (org_id = current_user_org_id() AND current_user_is_admin());

-- Only admins can modify usage data
CREATE POLICY usage_admin_modify ON organization_usage
    FOR INSERT
    WITH CHECK (org_id = current_user_org_id() AND current_user_is_admin());

-- Create function to set user context (called by application)
CREATE OR REPLACE FUNCTION set_user_context(
    p_user_id UUID,
    p_org_id UUID,
    p_role TEXT,
    p_environment TEXT DEFAULT 'production'
) RETURNS VOID AS $$
BEGIN
    -- Set session variables for RLS policies
    PERFORM set_config('app.current_user_id', p_user_id::TEXT, false);
    PERFORM set_config('app.current_org_id', p_org_id::TEXT, false);
    PERFORM set_config('app.current_user_role', p_role, false);
    PERFORM set_config('app.environment', p_environment, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clear user context
CREATE OR REPLACE FUNCTION clear_user_context() RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_id', '', false);
    PERFORM set_config('app.current_org_id', '', false);
    PERFORM set_config('app.current_user_role', '', false);
    PERFORM set_config('app.environment', '', false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate tenant isolation (for testing)
CREATE OR REPLACE FUNCTION validate_tenant_isolation(
    p_org_id UUID,
    p_table_name TEXT
) RETURNS TABLE(
    table_name TEXT,
    total_rows BIGINT,
    org_rows BIGINT,
    isolation_valid BOOLEAN
) AS $$
DECLARE
    sql_total TEXT;
    sql_org TEXT;
    total_count BIGINT;
    org_count BIGINT;
BEGIN
    -- Build dynamic SQL to count total rows
    sql_total := format('SELECT COUNT(*) FROM %I WHERE deleted_at IS NULL', p_table_name);
    
    -- Build dynamic SQL to count org-specific rows
    sql_org := format('SELECT COUNT(*) FROM %I WHERE org_id = $1 AND deleted_at IS NULL', p_table_name);
    
    -- Execute queries
    EXECUTE sql_total INTO total_count;
    EXECUTE sql_org INTO org_count USING p_org_id;
    
    -- Return results
    RETURN QUERY SELECT 
        p_table_name,
        total_count,
        org_count,
        (org_count <= total_count) AS isolation_valid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions to application role
-- Note: In production, create a specific application role with limited permissions
GRANT USAGE ON SCHEMA public TO PUBLIC;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO PUBLIC;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO PUBLIC;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO PUBLIC;
