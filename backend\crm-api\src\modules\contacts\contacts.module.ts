import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContactsController } from './contacts.controller';
import { ContactsService } from './contacts.service';
import { Contact } from './contact.entity';
import { User } from '../users/user.entity';
import { Company } from '../companies/company.entity';
import { TenantContextService } from '../../common/services/tenant-context.service';

@Module({
  imports: [TypeOrmModule.forFeature([Contact, User, Company])],
  controllers: [ContactsController],
  providers: [ContactsService, TenantContextService],
  exports: [ContactsService],
})
export class ContactsModule {}
