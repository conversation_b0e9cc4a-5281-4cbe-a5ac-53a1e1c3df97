// Authentication utility functions

// Parse JWT token payload
export const parseJwtPayload = (token: string): any | null => {
  try {
    const base64Url = token.split('.')[1];
    if (!base64Url) return null;
    
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('Failed to parse JWT payload:', error);
    return null;
  }
};

// Check if token is expired
export const isTokenExpired = (token: string): boolean => {
  const payload = parseJwtPayload(token);
  if (!payload || !payload.exp) return true;
  
  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
};

// Get token expiration time
export const getTokenExpirationTime = (token: string): Date | null => {
  const payload = parseJwtPayload(token);
  if (!payload || !payload.exp) return null;
  
  return new Date(payload.exp * 1000);
};

// Check if token expires soon (within specified minutes)
export const isTokenExpiringSoon = (token: string, minutesThreshold = 5): boolean => {
  const expirationTime = getTokenExpirationTime(token);
  if (!expirationTime) return true;
  
  const thresholdTime = new Date(Date.now() + minutesThreshold * 60 * 1000);
  return expirationTime <= thresholdTime;
};

// Generate random string for PKCE
export const generateRandomString = (length = 43): string => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  return result;
};

// Hash string for PKCE challenge
export const sha256 = async (plain: string): Promise<string> => {
  const encoder = new TextEncoder();
  const data = encoder.encode(plain);
  const hash = await crypto.subtle.digest('SHA-256', data);
  
  return btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(hash))))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
};

// Role hierarchy for permission checking
const ROLE_HIERARCHY: Record<string, number> = {
  viewer: 1,
  user: 2,
  admin: 3,
  super_admin: 4,
};

// Check if user has required role or higher
export const hasRequiredRole = (userRole: string, requiredRole: string): boolean => {
  const userLevel = ROLE_HIERARCHY[userRole] || 0;
  const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
};

// Permission mappings
export const PERMISSIONS = {
  // Contacts
  'contacts:read': ['viewer', 'user', 'admin'],
  'contacts:write': ['user', 'admin'],
  'contacts:delete': ['admin'],
  
  // Companies
  'companies:read': ['viewer', 'user', 'admin'],
  'companies:write': ['user', 'admin'],
  'companies:delete': ['admin'],
  
  // Deals
  'deals:read': ['viewer', 'user', 'admin'],
  'deals:write': ['user', 'admin'],
  'deals:delete': ['admin'],
  
  // Organizations
  'organizations:read': ['admin'],
  'organizations:write': ['admin'],
  
  // Users
  'users:read': ['admin'],
  'users:write': ['admin'],
  'users:delete': ['admin'],
} as const;

// Check if user has specific permission
export const hasPermission = (userRole: string, permission: keyof typeof PERMISSIONS): boolean => {
  const allowedRoles = PERMISSIONS[permission] || [];
  return allowedRoles.includes(userRole as any);
};
