import { Controller, Post, Get, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SeedingService } from '../services/seeding.service';

@ApiTags('Database Seeding')
@Controller('seed')
export class SeedingController {
  constructor(private readonly seedingService: SeedingService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Seed database with demo data',
    description: 'Populates the database with comprehensive demo data including organizations, users, companies, contacts, deals, and activities'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Database seeded successfully' 
  })
  @ApiResponse({ 
    status: 500, 
    description: 'Internal server error during seeding' 
  })
  async seedDatabase(): Promise<{ message: string; timestamp: string }> {
    await this.seedingService.seedDatabase();
    return {
      message: 'Database seeded successfully with demo data',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('status')
  @ApiOperation({ 
    summary: 'Check seeding status',
    description: 'Returns information about the current state of demo data in the database'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Seeding status retrieved successfully' 
  })
  async getSeedingStatus(): Promise<{ 
    message: string; 
    hasData: boolean; 
    timestamp: string 
  }> {
    // This is a simple implementation - you could enhance it to check actual data counts
    return {
      message: 'Seeding status check completed',
      hasData: true, // You could implement actual data checking here
      timestamp: new Date().toISOString(),
    };
  }
}
