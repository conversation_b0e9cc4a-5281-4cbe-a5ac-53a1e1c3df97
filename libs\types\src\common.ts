import { z } from 'zod';

// Common utility types
export type ID = string;
export type Timestamp = string;

// Status types
export const StatusSchema = z.enum(['active', 'inactive', 'pending', 'archived']);
export type Status = z.infer<typeof StatusSchema>;

// Priority types
export const PrioritySchema = z.enum(['low', 'medium', 'high', 'urgent']);
export type Priority = z.infer<typeof PrioritySchema>;

// Audit fields
export const AuditFieldsSchema = z.object({
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string().optional(),
  updatedBy: z.string().optional(),
});

export type AuditFields = z.infer<typeof AuditFieldsSchema>;

// Multi-tenant fields
export const TenantFieldsSchema = z.object({
  orgId: z.string(),
});

export type TenantFields = z.infer<typeof TenantFieldsSchema>;

// Base entity schema
export const BaseEntitySchema = z.object({
  id: z.string(),
}).merge(AuditFieldsSchema).merge(TenantFieldsSchema);

export type BaseEntity = z.infer<typeof BaseEntitySchema>;

// File upload types
export const FileUploadSchema = z.object({
  id: z.string(),
  filename: z.string(),
  originalName: z.string(),
  mimeType: z.string(),
  size: z.number(),
  url: z.string(),
  uploadedBy: z.string(),
  uploadedAt: z.string(),
});

export type FileUpload = z.infer<typeof FileUploadSchema>;

// Notification types
export const NotificationSchema = z.object({
  id: z.string(),
  type: z.enum(['info', 'success', 'warning', 'error']),
  title: z.string(),
  message: z.string(),
  read: z.boolean().default(false),
  userId: z.string(),
  createdAt: z.string(),
});

export type Notification = z.infer<typeof NotificationSchema>;
