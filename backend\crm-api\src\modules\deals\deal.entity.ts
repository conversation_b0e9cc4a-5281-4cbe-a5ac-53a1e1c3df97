import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Organization } from '../organizations/organization.entity';
import { User } from '../users/user.entity';
import { Contact } from '../contacts/contact.entity';
import { Company } from '../companies/company.entity';

@Entity('deals')
export class Deal {
  @ApiProperty({ description: 'Unique identifier for the deal' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Organization ID' })
  @Column({ name: 'org_id' })
  orgId: string;

  @ApiProperty({ description: 'Deal title' })
  @Column({ length: 255 })
  title: string;

  @ApiProperty({ description: 'Deal amount', required: false })
  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  amount?: number;

  @ApiProperty({ description: 'Deal currency', required: false })
  @Column({ length: 3, default: 'USD' })
  currency: string;

  @ApiProperty({ description: 'Deal stage', required: false })
  @Column({ length: 100, nullable: true })
  stage?: string;

  @ApiProperty({ description: 'Deal probability (0-100)', required: false })
  @Column({ type: 'int', nullable: true })
  probability?: number;

  @ApiProperty({ description: 'Expected close date', required: false })
  @Column({ name: 'expected_close_date', type: 'date', nullable: true })
  expectedCloseDate?: Date;

  @ApiProperty({ description: 'Deal description', required: false })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Deal source', required: false })
  @Column({ length: 100, nullable: true })
  source?: string;

  @ApiProperty({ description: 'Deal type', required: false })
  @Column({ length: 100, nullable: true })
  type?: string;

  @ApiProperty({ description: 'Deal priority', required: false })
  @Column({ length: 50, nullable: true })
  priority?: string;

  @ApiProperty({ description: 'Custom fields', required: false })
  @Column({
    type: 'text',
    default: '{}',
    name: 'custom_fields',
    transformer: {
      to: (value: any) => JSON.stringify(value || {}),
      from: (value: string) => {
        try {
          return JSON.parse(value || '{}');
        } catch {
          return {};
        }
      }
    }
  })
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Deal notes', required: false })
  @Column({ type: 'text', nullable: true })
  notes?: string;

  @ApiProperty({ description: 'Contact ID', required: false })
  @Column({ name: 'contact_id', nullable: true })
  contactId?: string;

  @ApiProperty({ description: 'Company ID', required: false })
  @Column({ name: 'company_id', nullable: true })
  companyId?: string;

  @ApiProperty({ description: 'Deal owner ID', required: false })
  @Column({ name: 'owner_id', nullable: true })
  ownerId?: string;

  @ApiProperty({ description: 'User who created the deal', required: false })
  @Column({ name: 'created_by', nullable: true })
  createdById?: string;

  @ApiProperty({ description: 'User who last updated the deal', required: false })
  @Column({ name: 'updated_by', nullable: true })
  updatedById?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ApiProperty({ description: 'Soft delete timestamp', required: false })
  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt?: Date;

  // Relations
  @ManyToOne(() => Organization, (organization) => organization.deals)
  @JoinColumn({ name: 'org_id' })
  organization: Organization;

  @ManyToOne(() => Contact)
  @JoinColumn({ name: 'contact_id' })
  contact?: Contact;

  @ManyToOne(() => Company)
  @JoinColumn({ name: 'company_id' })
  company?: Company;

  @ManyToOne(() => User, (user) => user.ownedDeals)
  @JoinColumn({ name: 'owner_id' })
  owner?: User;

  @ManyToOne(() => User, (user) => user.createdDeals)
  @JoinColumn({ name: 'created_by' })
  createdBy?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updatedBy?: User;
}
