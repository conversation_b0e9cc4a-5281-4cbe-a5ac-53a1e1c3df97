import { IsS<PERSON>, <PERSON>O<PERSON>al, IsUUID, MaxLength, IsArray, IsObject, IsUrl, IsInt, Min, IsDecimal } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

export class CreateCompanyDto {
  @ApiProperty({ description: 'Company name', example: 'Acme Corporation' })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({ description: 'Company domain', example: 'acme.com', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  domain?: string;

  @ApiProperty({ description: 'Industry', example: 'Technology', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  industry?: string;

  @ApiProperty({ description: 'Company size', example: 'Enterprise', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  size?: string;

  @ApiProperty({ description: 'Company description', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Company website', example: 'https://acme.com', required: false })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  website?: string;

  @ApiProperty({ description: 'Company phone number', example: '******-123-4567', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  phone?: string;

  @ApiProperty({ 
    description: 'Company address', 
    example: { street: '123 Main St', city: 'New York', state: 'NY', zip: '10001', country: 'USA' },
    required: false 
  })
  @IsOptional()
  @IsObject()
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };

  @ApiProperty({ description: 'Company tags', example: ['prospect', 'enterprise'], required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({ description: 'Custom fields as key-value pairs', required: false })
  @IsOptional()
  @IsObject()
  customFields?: Record<string, any>;

  @ApiProperty({ description: 'Annual revenue in USD', example: 1000000, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsDecimal({ decimal_digits: '0,2' })
  @Transform(({ value }) => parseFloat(value))
  annualRevenue?: number;

  @ApiProperty({ description: 'Number of employees', example: 500, required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(0)
  employeeCount?: number;

  @ApiProperty({ description: 'User ID to assign company to', required: false })
  @IsOptional()
  @IsUUID()
  assignedToId?: string;

  @ApiProperty({ description: 'Parent company ID for company hierarchy', required: false })
  @IsOptional()
  @IsUUID()
  parentCompanyId?: string;
}
