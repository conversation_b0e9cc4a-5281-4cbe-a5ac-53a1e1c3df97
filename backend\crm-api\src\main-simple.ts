import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { CustomLoggerService } from './common/logger/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  // Get configuration service
  const configService = app.get(ConfigService);
  const customLogger = app.get(CustomLoggerService);

  // Use custom logger
  app.useLogger(customLogger);

  // Enable CORS
  app.enableCors({
    origin: true, // Allow all origins for testing
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Org-Id', 'X-Request-Id'],
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // API prefix
  app.setGlobalPrefix('api');

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('OneCRM API')
    .setDescription('Enterprise-grade Multi-tenant CRM API')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('health', 'Health check endpoints')
    .addTag('seed', 'Database seeding endpoints')
    .addTag('organizations', 'Organization management')
    .addTag('contacts', 'Contact management')
    .addTag('companies', 'Company management')
    .addTag('deals', 'Deal management')
    .addTag('users', 'User management')
    .addTag('activities', 'Activity management')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
  });

  const port = configService.get('PORT', 3002);
  const environment = configService.get('NODE_ENV', 'development');

  await app.listen(port, '0.0.0.0');

  // Log startup information
  customLogger.log(`🚀 OneCRM API is running on: http://localhost:${port}`, 'Bootstrap');
  customLogger.log(`📚 API Documentation: http://localhost:${port}/api/docs`, 'Bootstrap');
  customLogger.log(`🌍 Environment: ${environment}`, 'Bootstrap');
  customLogger.log(`🔧 Hot Reload: ${environment === 'development' ? 'Enabled' : 'Disabled'}`, 'Bootstrap');

  if (environment === 'development') {
    customLogger.log(`🐛 Debug Mode: Enabled`, 'Bootstrap');
    customLogger.log(`📊 Detailed Logging: Enabled`, 'Bootstrap');
  }
}

bootstrap().catch((error) => {
  console.error('Failed to start the application:', error);
  process.exit(1);
});
