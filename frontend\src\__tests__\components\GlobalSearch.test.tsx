import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import { GlobalSearch } from '../../components/search/GlobalSearch';

// Mock next/navigation
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Mock SWR
jest.mock('swr');

// Mock useDebounce hook
jest.mock('../../hooks/useDebounce', () => ({
  useDebounce: (value: string) => value, // Return immediately for testing
}));

const theme = createTheme();

const mockSearchResults = {
  results: [
    {
      id: '1',
      type: 'contact',
      title: '<PERSON>',
      subtitle: 'Software Engineer',
      description: '<EMAIL>',
      metadata: {},
    },
    {
      id: '2',
      type: 'company',
      title: 'Tech Corp',
      subtitle: 'Technology',
      description: 'tech-corp.com',
      metadata: {},
    },
    {
      id: '3',
      type: 'deal',
      title: 'Enterprise Software Deal',
      subtitle: 'Qualification',
      description: 'Large enterprise deal',
      metadata: { amount: 50000 },
    },
  ],
  total: 3,
};

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      <SWRConfig value={{ provider: () => new Map() }}>
        {component}
      </SWRConfig>
    </ThemeProvider>
  );
};

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('GlobalSearch', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    // Default SWR mock
    require('swr').default.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });
  });

  it('renders search input', () => {
    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    expect(searchInput).toBeInTheDocument();
  });

  it('shows search results when typing', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    
    await user.type(searchInput, 'john');

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Tech Corp')).toBeInTheDocument();
      expect(screen.getByText('Enterprise Software Deal')).toBeInTheDocument();
    });
  });

  it('groups results by type', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByText('Contacts (1)')).toBeInTheDocument();
      expect(screen.getByText('Companies (1)')).toBeInTheDocument();
      expect(screen.getByText('Deals (1)')).toBeInTheDocument();
    });
  });

  it('shows loading state', async () => {
    require('swr').default.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  it('shows no results message', async () => {
    require('swr').default.mockReturnValue({
      data: { results: [], total: 0 },
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'nonexistent');

    await waitFor(() => {
      expect(screen.getByText('No results found for "nonexistent"')).toBeInTheDocument();
    });
  });

  it('navigates to correct page when result is clicked', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'john');

    await waitFor(() => {
      const contactResult = screen.getByText('John Doe');
      fireEvent.click(contactResult);
    });

    expect(mockPush).toHaveBeenCalledWith('/contacts?id=1');
  });

  it('saves and displays recent searches', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(['previous search']));

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    
    // Focus input to show recent searches
    await user.click(searchInput);

    await waitFor(() => {
      expect(screen.getByText('Recent Searches')).toBeInTheDocument();
      expect(screen.getByText('previous search')).toBeInTheDocument();
    });
  });

  it('clears recent searches', async () => {
    localStorageMock.getItem.mockReturnValue(JSON.stringify(['search1', 'search2']));

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.click(searchInput);

    await waitFor(() => {
      const clearButton = screen.getByLabelText('clear recent searches');
      fireEvent.click(clearButton);
    });

    expect(localStorageMock.removeItem).toHaveBeenCalledWith('onecrm-recent-searches');
  });

  it('handles keyboard navigation', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Test arrow down navigation
    fireEvent.keyDown(searchInput, { key: 'ArrowDown' });
    
    // First result should be selected (highlighted)
    const firstResult = screen.getByText('John Doe').closest('[role="button"]');
    expect(firstResult).toHaveClass('Mui-selected');

    // Test arrow up navigation
    fireEvent.keyDown(searchInput, { key: 'ArrowUp' });
    
    // Should wrap to last result
    const lastResult = screen.getByText('Enterprise Software Deal').closest('[role="button"]');
    expect(lastResult).toHaveClass('Mui-selected');
  });

  it('handles enter key to select result', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Navigate to first result and press enter
    fireEvent.keyDown(searchInput, { key: 'ArrowDown' });
    fireEvent.keyDown(searchInput, { key: 'Enter' });

    expect(mockPush).toHaveBeenCalledWith('/contacts?id=1');
  });

  it('handles escape key to close results', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Press escape to close
    fireEvent.keyDown(searchInput, { key: 'Escape' });

    await waitFor(() => {
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    });
  });

  it('clears search input', async () => {
    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test query');

    // Clear button should appear
    const clearButton = screen.getByLabelText('clear search');
    expect(clearButton).toBeInTheDocument();

    fireEvent.click(clearButton);

    expect(searchInput).toHaveValue('');
  });

  it('closes results when clicking away', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(
      <div>
        <GlobalSearch />
        <div data-testid="outside">Outside element</div>
      </div>
    );

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'test');

    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    // Click outside
    const outsideElement = screen.getByTestId('outside');
    fireEvent.click(outsideElement);

    await waitFor(() => {
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    });
  });

  it('displays currency formatting for deal amounts', async () => {
    require('swr').default.mockReturnValue({
      data: mockSearchResults,
      isLoading: false,
      error: null,
      mutate: jest.fn(),
    });

    renderWithProviders(<GlobalSearch />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    await user.type(searchInput, 'deal');

    await waitFor(() => {
      expect(screen.getByText('$50,000')).toBeInTheDocument();
    });
  });

  it('handles custom placeholder text', () => {
    renderWithProviders(<GlobalSearch placeholder="Custom search placeholder" />);

    const searchInput = screen.getByPlaceholderText('Custom search placeholder');
    expect(searchInput).toBeInTheDocument();
  });

  it('handles different sizes', () => {
    renderWithProviders(<GlobalSearch size="small" />);

    const searchInput = screen.getByPlaceholderText('Search contacts, companies, deals...');
    expect(searchInput).toHaveClass('MuiInputBase-sizeSmall');
  });

  it('handles full width prop', () => {
    renderWithProviders(<GlobalSearch fullWidth />);

    const searchContainer = screen.getByPlaceholderText('Search contacts, companies, deals...').closest('.MuiTextField-root');
    expect(searchContainer).toHaveClass('MuiTextField-fullWidth');
  });
});
