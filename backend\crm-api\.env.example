# OneCRM API Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Configuration
NODE_ENV=development
PORT=3002
API_PREFIX=api

# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_USERNAME=onecrm
DB_PASSWORD=onecrm_dev_password
DB_NAME=onecrm
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_ACQUIRE_TIMEOUT=60000
DB_IDLE_TIMEOUT=10000
DB_CONNECT_TIMEOUT=10000
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=3000
DB_DISABLE_SYNC=false
DB_TIMEZONE=UTC

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600

# Authentication & Authorization (Keycloak)
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-api
KE<PERSON><PERSON>OAK_CLIENT_SECRET=your-client-secret
KEYCLOAK_ADMIN_USERNAME=admin
KEYCLOAK_ADMIN_PASSWORD=admin

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv
UPLOAD_DEST=./uploads

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs
LOG_MAX_FILES=30
LOG_MAX_SIZE=20m

# Monitoring & Health Checks
HEALTH_CHECK_TIMEOUT=5000
METRICS_ENABLED=true

# Multi-tenancy Configuration
TENANT_ISOLATION_ENABLED=true
DEFAULT_TENANT_PLAN=starter
MAX_TENANTS_PER_INSTANCE=1000

# Feature Flags
FEATURE_ADVANCED_ANALYTICS=true
FEATURE_CUSTOM_FIELDS=true
FEATURE_WEBHOOKS=true
FEATURE_API_RATE_LIMITING=true
FEATURE_AUDIT_LOGS=true

# External Integrations
WEBHOOK_SECRET=your-webhook-secret
INTEGRATION_TIMEOUT=30000

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this
CSRF_ENABLED=true

# Development Configuration
DEV_ENABLE_PLAYGROUND=true
DEV_ENABLE_INTROSPECTION=true
DEV_ENABLE_DEBUG=true
DEV_MOCK_AUTH=false

# Production Configuration
PROD_ENABLE_COMPRESSION=true
PROD_ENABLE_HELMET=true
PROD_TRUST_PROXY=true
PROD_CLUSTER_MODE=false

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=onecrm-backups
BACKUP_S3_REGION=us-east-1

# Cache Configuration
CACHE_TTL=300
CACHE_MAX_ITEMS=1000
CACHE_ENABLED=true

# API Documentation
SWAGGER_TITLE=OneCRM API
SWAGGER_DESCRIPTION=Enterprise-grade Multi-tenant CRM API
SWAGGER_VERSION=1.0.0
SWAGGER_CONTACT_NAME=OneCRM Support
SWAGGER_CONTACT_EMAIL=<EMAIL>

# Performance Configuration
REQUEST_TIMEOUT=30000
BODY_LIMIT=10mb
PARAMETER_LIMIT=1000

# Internationalization
DEFAULT_LOCALE=en
SUPPORTED_LOCALES=en,es,fr,de,it,pt,ja,zh

# Analytics & Tracking
ANALYTICS_ENABLED=true
TRACKING_ENABLED=true
USAGE_METRICS_ENABLED=true

# Notification Configuration
NOTIFICATION_QUEUE_ENABLED=true
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_DELAY=5000

# Audit Configuration
AUDIT_ENABLED=true
AUDIT_RETENTION_DAYS=365
AUDIT_INCLUDE_REQUESTS=true
AUDIT_INCLUDE_RESPONSES=false

# Error Handling
ERROR_STACK_TRACE_ENABLED=true
ERROR_REPORTING_ENABLED=true
ERROR_NOTIFICATION_ENABLED=true
