'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Box,
  Chip,
  Grid,
  LinearProgress,
  IconButton,
  Menu,
  MenuItem,
  Skeleton,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  TrendingUp as TrendingUpIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface Deal {
  id: string;
  title: string;
  amount: number;
  probability: number;
  owner?: {
    firstName: string;
    lastName: string;
  };
  contact?: {
    firstName: string;
    lastName: string;
  };
  company?: {
    name: string;
  };
}

interface PipelineStage {
  stage: string;
  count: number;
  totalValue: number;
  averageValue: number;
  deals: Deal[];
}

const stageColors: Record<string, string> = {
  lead: '#94a3b8',
  qualification: '#3b82f6',
  proposal: '#f59e0b',
  negotiation: '#ef4444',
  'closed-won': '#22c55e',
  'closed-lost': '#6b7280',
};

const stageLabels: Record<string, string> = {
  lead: 'Lead',
  qualification: 'Qualification',
  proposal: 'Proposal',
  negotiation: 'Negotiation',
  'closed-won': 'Closed Won',
  'closed-lost': 'Closed Lost',
};

const DealCard: React.FC<{ deal: Deal }> = ({ deal }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card
      variant="outlined"
      sx={{
        mb: 1,
        cursor: 'pointer',
        '&:hover': {
          boxShadow: 2,
        },
      }}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <Typography variant="subtitle2" fontWeight={600} gutterBottom>
              {deal.title}
            </Typography>
            
            <Typography variant="h6" color="primary" gutterBottom>
              {formatCurrency(deal.amount)}
            </Typography>

            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <LinearProgress
                variant="determinate"
                value={deal.probability}
                sx={{ flex: 1, height: 6, borderRadius: 3 }}
              />
              <Typography variant="caption" color="text.secondary">
                {deal.probability}%
              </Typography>
            </Box>

            <Box display="flex" alignItems="center" gap={1}>
              {deal.owner && (
                <Tooltip title={`Owner: ${deal.owner.firstName} ${deal.owner.lastName}`}>
                  <Avatar sx={{ width: 20, height: 20, fontSize: '0.75rem' }}>
                    {deal.owner.firstName[0]}{deal.owner.lastName[0]}
                  </Avatar>
                </Tooltip>
              )}
              
              {deal.contact && (
                <Typography variant="caption" color="text.secondary">
                  {deal.contact.firstName} {deal.contact.lastName}
                </Typography>
              )}
              
              {deal.company && (
                <Chip
                  label={deal.company.name}
                  size="small"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem', height: 20 }}
                />
              )}
            </Box>
          </Box>

          <IconButton size="small" onClick={handleMenuOpen}>
            <MoreVertIcon fontSize="small" />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>View Details</MenuItem>
            <MenuItem onClick={handleMenuClose}>Edit Deal</MenuItem>
            <MenuItem onClick={handleMenuClose}>Move Stage</MenuItem>
          </Menu>
        </Box>
      </CardContent>
    </Card>
  );
};

const PipelineStageColumn: React.FC<{ stage: PipelineStage; isLoading: boolean }> = ({
  stage,
  isLoading,
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader
          title={<Skeleton variant="text" width={120} />}
          subheader={<Skeleton variant="text" width={80} />}
        />
        <CardContent>
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} variant="rectangular" height={120} sx={{ mb: 1 }} />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ height: 'fit-content', minHeight: 400 }}>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <Box
              sx={{
                width: 12,
                height: 12,
                borderRadius: '50%',
                bgcolor: stageColors[stage.stage] || '#94a3b8',
              }}
            />
            <Typography variant="h6" fontWeight={600}>
              {stageLabels[stage.stage] || stage.stage}
            </Typography>
            <Chip
              label={stage.count}
              size="small"
              sx={{ ml: 'auto' }}
            />
          </Box>
        }
        subheader={
          <Typography variant="body2" color="text.secondary">
            {formatCurrency(stage.totalValue)} total
          </Typography>
        }
        sx={{ pb: 1 }}
      />
      <CardContent sx={{ pt: 0, maxHeight: 500, overflow: 'auto' }}>
        {stage.deals.length === 0 ? (
          <Typography variant="body2" color="text.secondary" textAlign="center" py={4}>
            No deals in this stage
          </Typography>
        ) : (
          stage.deals.map((deal) => (
            <DealCard key={deal.id} deal={deal} />
          ))
        )}
      </CardContent>
    </Card>
  );
};

export const SalesPipeline: React.FC = () => {
  const { data: pipelineData, isLoading } = useSWR('/api/deals/pipeline');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <TrendingUpIcon color="primary" />
            <Typography variant="h6" fontWeight={600}>
              Sales Pipeline
            </Typography>
          </Box>
        }
        subheader={
          !isLoading && pipelineData ? (
            <Typography variant="body2" color="text.secondary">
              {formatCurrency(pipelineData.totalValue)} total pipeline • {formatCurrency(pipelineData.weightedValue)} weighted
            </Typography>
          ) : (
            <Skeleton variant="text" width={200} />
          )
        }
      />
      <CardContent>
        <Grid container spacing={2}>
          {isLoading ? (
            // Loading state
            Array.from({ length: 6 }).map((_, index) => (
              <Grid item xs={12} sm={6} md={4} lg={2} key={index}>
                <PipelineStageColumn
                  stage={{} as PipelineStage}
                  isLoading={true}
                />
              </Grid>
            ))
          ) : (
            // Loaded state
            pipelineData?.pipeline?.map((stage: PipelineStage) => (
              <Grid item xs={12} sm={6} md={4} lg={2} key={stage.stage}>
                <PipelineStageColumn stage={stage} isLoading={false} />
              </Grid>
            ))
          )}
        </Grid>
      </CardContent>
    </Card>
  );
};
