# OneCRM UX Improvement Report & Design System
## Complete Frontend Revamp with shadcn/ui

### Executive Summary

This comprehensive report analyzes the current OneCRM frontend and provides detailed recommendations for a complete UX overhaul using shadcn/ui components. Based on analysis of modern CRM trends (2025) and best practices from leading platforms like Salesforce, HubSpot, and Pipedrive, this document outlines a clean, minimal, and highly functional design system.

## Current State Analysis

### Existing Architecture
- **Framework**: Next.js 14 with App Router
- **UI Library**: Material-UI (MUI) v5
- **State Management**: SWR + Context
- **Components**: Basic table, kanban, dashboard components
- **Layout**: Traditional sidebar + main content

### Current Pain Points
1. **Visual Hierarchy**: Inconsistent spacing and typography
2. **Information Density**: Overwhelming data presentation
3. **Navigation**: Complex sidebar navigation on mobile
4. **Filtering**: Basic search without advanced filters
5. **Responsiveness**: Limited mobile optimization
6. **Performance**: Heavy MUI bundle size
7. **Accessibility**: Limited keyboard navigation
8. **User Flow**: Disconnected workflows between modules

## Modern CRM UX Trends (2025)

### Key Design Principles
1. **Minimal Clean Interface**: Reduced visual noise, focus on content
2. **Contextual Actions**: Actions appear when needed
3. **Progressive Disclosure**: Show relevant information gradually
4. **Unified Search**: Global search with intelligent filtering
5. **Collaborative Features**: Real-time updates and team visibility
6. **Mobile-First**: Touch-optimized interactions
7. **Data Visualization**: Inline charts and progress indicators
8. **Customizable Workspaces**: User-defined layouts and views

### Industry Benchmarks
- **Salesforce Lightning**: Card-based layouts, contextual sidebars
- **HubSpot**: Clean tables with inline editing, smart filters
- **Pipedrive**: Visual pipeline with drag-drop, minimal chrome
- **Notion**: Block-based content, seamless navigation
- **Linear**: Command palette, keyboard shortcuts

## Proposed Design System with shadcn/ui

### Core Components Architecture

#### 1. Layout System
```
┌─────────────────────────────────────────┐
│ Header (Global Search + User Menu)     │
├─────────────────────────────────────────┤
│ ┌─────┐ ┌─────────────────────────────┐ │
│ │ Nav │ │ Main Content Area           │ │
│ │     │ │ ┌─────────────────────────┐ │ │
│ │     │ │ │ Page Header + Actions   │ │ │
│ │     │ │ ├─────────────────────────┤ │ │
│ │     │ │ │ Filters + Views         │ │ │
│ │     │ │ ├─────────────────────────┤ │ │
│ │     │ │ │ Content (Table/Kanban)  │ │ │
│ │     │ │ └─────────────────────────┘ │ │
│ └─────┘ └─────────────────────────────┘ │
└─────────────────────────────────────────┘
```

#### 2. Navigation Components
- **Collapsible Sidebar**: Auto-collapse on mobile
- **Breadcrumb Navigation**: Context-aware path
- **Command Palette**: Cmd+K global search and actions
- **Quick Actions**: Floating action button on mobile

#### 3. Data Display Components
- **Enhanced Data Table**: Sortable, filterable, with inline actions
- **Kanban Board**: Drag-drop with swimlanes
- **Card Views**: Compact information display
- **List Views**: Dense information with quick actions

#### 4. Form Components
- **Inline Editing**: Click-to-edit fields
- **Modal Forms**: Overlay forms for quick actions
- **Slide-over Panels**: Side panels for detailed editing
- **Multi-step Wizards**: Guided data entry

#### 5. Filter & Search System
- **Global Search Bar**: Intelligent search across all entities
- **Advanced Filters**: Faceted search with saved filters
- **Quick Filters**: One-click common filters
- **Search Suggestions**: Auto-complete with recent searches

## Detailed Component Specifications

### 1. Dashboard Layout
```typescript
// Modern dashboard with cards and widgets
<DashboardGrid>
  <MetricsCards />
  <RevenueChart />
  <PipelineOverview />
  <RecentActivity />
  <UpcomingTasks />
</DashboardGrid>
```

**Key Features**:
- Responsive grid system
- Drag-drop widget arrangement
- Real-time data updates
- Customizable widget sizes
- Export capabilities

### 2. Enhanced Data Table
```typescript
// Advanced table with shadcn/ui components
<DataTable
  columns={columns}
  data={data}
  filters={<AdvancedFilters />}
  toolbar={<TableToolbar />}
  pagination={<SmartPagination />}
  selection="multiple"
  sorting="multi-column"
  grouping="enabled"
  export="csv,excel,pdf"
/>
```

**Features**:
- Column resizing and reordering
- Inline editing capabilities
- Bulk actions toolbar
- Saved views and filters
- Virtual scrolling for large datasets
- Keyboard navigation

### 3. Modern Kanban Board
```typescript
// Drag-drop kanban with swimlanes
<KanbanBoard
  stages={dealStages}
  swimlanes={["priority", "owner"]}
  cards={<DealCard />}
  onDragEnd={handleDragEnd}
  filters={<KanbanFilters />}
  groupBy="assignee"
/>
```

**Features**:
- Horizontal and vertical swimlanes
- Card templates for different entities
- Bulk move operations
- Stage-specific actions
- Real-time collaboration indicators

### 4. Smart Search & Filters
```typescript
// Global search with intelligent filtering
<SearchSystem>
  <GlobalSearchBar
    placeholder="Search contacts, companies, deals..."
    suggestions={<SearchSuggestions />}
    shortcuts={<KeyboardShortcuts />}
  />
  <AdvancedFilters
    fields={filterableFields}
    operators={["equals", "contains", "greater_than"]}
    savedFilters={<SavedFilters />}
    quickFilters={<QuickFilters />}
  />
</SearchSystem>
```

**Features**:
- Fuzzy search across all entities
- Filter by relationships (e.g., "deals from Acme Corp")
- Date range pickers
- Multi-select dropdowns
- Boolean logic (AND/OR)
- Saved filter sets

## Mobile-First Design Patterns

### 1. Progressive Navigation
- **Bottom Tab Bar**: Primary navigation
- **Swipe Gestures**: Navigate between views
- **Pull-to-Refresh**: Update data
- **Infinite Scroll**: Load more content

### 2. Touch-Optimized Interactions
- **Large Touch Targets**: Minimum 44px
- **Swipe Actions**: Quick actions on list items
- **Long Press Menus**: Context actions
- **Gesture Navigation**: Swipe back/forward

### 3. Responsive Layouts
- **Stacked Cards**: Mobile-friendly information display
- **Collapsible Sections**: Expandable content areas
- **Floating Action Button**: Primary actions
- **Sheet Modals**: Bottom sheet for forms

## Accessibility & Performance

### Accessibility Features
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: ARIA labels and descriptions
- **High Contrast Mode**: Dark/light theme support
- **Focus Management**: Logical tab order
- **Voice Commands**: Integration with browser speech API

### Performance Optimizations
- **Code Splitting**: Route-based lazy loading
- **Virtual Scrolling**: Large dataset handling
- **Image Optimization**: Next.js Image component
- **Caching Strategy**: SWR with background updates
- **Bundle Analysis**: Tree-shaking unused code

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- [ ] Setup shadcn/ui design system
- [ ] Create base layout components
- [ ] Implement navigation system
- [ ] Setup theming and dark mode

### Phase 2: Core Components (Weeks 3-4)
- [ ] Enhanced data table component
- [ ] Modern kanban board
- [ ] Search and filter system
- [ ] Form components with validation

### Phase 3: Advanced Features (Weeks 5-6)
- [ ] Dashboard with widgets
- [ ] Mobile responsive layouts
- [ ] Accessibility improvements
- [ ] Performance optimizations

### Phase 4: Polish & Testing (Weeks 7-8)
- [ ] User testing and feedback
- [ ] Bug fixes and refinements
- [ ] Documentation and training
- [ ] Production deployment

## Success Metrics

### User Experience Metrics
- **Task Completion Time**: 40% reduction
- **User Satisfaction Score**: Target 4.5/5
- **Mobile Usage**: 60% increase
- **Feature Adoption**: 80% of new features used

### Technical Metrics
- **Page Load Time**: <2 seconds
- **Bundle Size**: 50% reduction
- **Accessibility Score**: 95+ Lighthouse
- **Performance Score**: 90+ Lighthouse

## Next Steps

1. **Stakeholder Review**: Present design concepts
2. **Technical Planning**: Detailed implementation plan
3. **Design System Setup**: Initialize shadcn/ui components
4. **Prototype Development**: Build key user flows
5. **User Testing**: Validate design decisions

This comprehensive UX overhaul will transform OneCRM into a modern, efficient, and delightful CRM platform that rivals industry leaders while maintaining the flexibility and power users expect.

---

## Detailed Component Library Specifications

### Core UI Components with shadcn/ui

#### 1. Enhanced Button System
```typescript
// Primary action buttons with consistent styling
<Button variant="default" size="sm" className="gap-2">
  <Plus className="h-4 w-4" />
  Add Contact
</Button>

// Secondary actions
<Button variant="outline" size="sm">
  Export
</Button>

// Destructive actions
<Button variant="destructive" size="sm">
  Delete
</Button>
```

#### 2. Advanced Form Components
```typescript
// Form with validation and auto-save
<Form {...form}>
  <FormField
    control={form.control}
    name="firstName"
    render={({ field }) => (
      <FormItem>
        <FormLabel>First Name</FormLabel>
        <FormControl>
          <Input placeholder="Enter first name" {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
</Form>
```

#### 3. Smart Data Display Cards
```typescript
// Contact card with quick actions
<Card className="hover:shadow-md transition-shadow">
  <CardHeader className="pb-3">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={contact.avatar} />
          <AvatarFallback>{contact.initials}</AvatarFallback>
        </Avatar>
        <div>
          <CardTitle className="text-base">{contact.name}</CardTitle>
          <CardDescription>{contact.title}</CardDescription>
        </div>
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem>Edit</DropdownMenuItem>
          <DropdownMenuItem>Delete</DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </CardHeader>
</Card>
```

#### 4. Advanced Filter System
```typescript
// Multi-faceted filter interface
<div className="flex flex-wrap gap-2 mb-4">
  <Popover>
    <PopoverTrigger asChild>
      <Button variant="outline" size="sm" className="gap-2">
        <Filter className="h-4 w-4" />
        Status
        {statusFilter && <Badge variant="secondary">{statusFilter}</Badge>}
      </Button>
    </PopoverTrigger>
    <PopoverContent className="w-80">
      <div className="space-y-2">
        <Label>Contact Status</Label>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger>
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="lead">Lead</SelectItem>
            <SelectItem value="qualified">Qualified</SelectItem>
            <SelectItem value="customer">Customer</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </PopoverContent>
  </Popover>
</div>
```

### Page-Specific Layouts

#### 1. Dashboard Layout
```typescript
// Modern dashboard with customizable widgets
<div className="space-y-6">
  {/* Header */}
  <div className="flex items-center justify-between">
    <div>
      <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
      <p className="text-muted-foreground">
        Welcome back, {user.name}
      </p>
    </div>
    <div className="flex items-center gap-2">
      <Button variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
      <Button size="sm">
        <Plus className="h-4 w-4 mr-2" />
        Quick Add
      </Button>
    </div>
  </div>

  {/* Metrics Grid */}
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <MetricCard
      title="Total Contacts"
      value="1,247"
      change="+12%"
      trend="up"
    />
    <MetricCard
      title="Active Deals"
      value="156"
      change="+8%"
      trend="up"
    />
    <MetricCard
      title="Revenue"
      value="$2.4M"
      change="+24%"
      trend="up"
    />
    <MetricCard
      title="Conversion Rate"
      value="24.8%"
      change="-2%"
      trend="down"
    />
  </div>

  {/* Content Grid */}
  <div className="grid gap-6 lg:grid-cols-2">
    <Card>
      <CardHeader>
        <CardTitle>Sales Pipeline</CardTitle>
      </CardHeader>
      <CardContent>
        <PipelineChart />
      </CardContent>
    </Card>

    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <ActivityFeed />
      </CardContent>
    </Card>
  </div>
</div>
```

#### 2. Contacts List Layout
```typescript
// Contacts page with advanced filtering and views
<div className="space-y-6">
  {/* Page Header */}
  <div className="flex items-center justify-between">
    <div>
      <h1 className="text-3xl font-bold tracking-tight">Contacts</h1>
      <p className="text-muted-foreground">
        Manage your contacts and leads
      </p>
    </div>
    <Button>
      <Plus className="h-4 w-4 mr-2" />
      Add Contact
    </Button>
  </div>

  {/* Search and Filters */}
  <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
    <div className="flex flex-1 items-center gap-2">
      <div className="relative flex-1 max-w-sm">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search contacts..."
          className="pl-10"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      <AdvancedFilters />
    </div>

    <div className="flex items-center gap-2">
      <ViewToggle />
      <Button variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
    </div>
  </div>

  {/* Content Area */}
  <div className="rounded-md border">
    {viewMode === 'table' ? (
      <ContactsTable />
    ) : (
      <ContactsGrid />
    )}
  </div>
</div>
```

#### 3. Deals Kanban Layout
```typescript
// Kanban board with drag-drop functionality
<div className="space-y-6">
  {/* Header with Pipeline Controls */}
  <div className="flex items-center justify-between">
    <div>
      <h1 className="text-3xl font-bold tracking-tight">Sales Pipeline</h1>
      <p className="text-muted-foreground">
        Track deals through your sales process
      </p>
    </div>
    <div className="flex items-center gap-2">
      <Select value={pipelineView} onValueChange={setPipelineView}>
        <SelectTrigger className="w-40">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="standard">Standard View</SelectItem>
          <SelectItem value="forecast">Forecast View</SelectItem>
          <SelectItem value="activity">Activity View</SelectItem>
        </SelectContent>
      </Select>
      <Button>
        <Plus className="h-4 w-4 mr-2" />
        Add Deal
      </Button>
    </div>
  </div>

  {/* Pipeline Stats */}
  <div className="grid gap-4 md:grid-cols-6">
    {pipelineStages.map((stage) => (
      <Card key={stage.id}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium">{stage.name}</p>
              <p className="text-2xl font-bold">{stage.count}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Value</p>
              <p className="text-sm font-medium">{stage.value}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>

  {/* Kanban Board */}
  <div className="overflow-x-auto">
    <div className="flex gap-4 pb-4" style={{ minWidth: 'max-content' }}>
      {pipelineStages.map((stage) => (
        <KanbanColumn
          key={stage.id}
          stage={stage}
          deals={getDealsForStage(stage.id)}
          onDragEnd={handleDragEnd}
        />
      ))}
    </div>
  </div>
</div>
```

### Mobile-Optimized Components

#### 1. Mobile Navigation Pattern
```typescript
// Bottom navigation with contextual actions
<div className="fixed bottom-0 left-0 right-0 bg-background border-t md:hidden">
  <div className="grid grid-cols-5 gap-1 p-2">
    {navigationItems.map((item) => (
      <Button
        key={item.path}
        variant={pathname === item.path ? "default" : "ghost"}
        size="sm"
        className="flex flex-col gap-1 h-auto py-2"
        onClick={() => router.push(item.path)}
      >
        <item.icon className="h-4 w-4" />
        <span className="text-xs">{item.label}</span>
      </Button>
    ))}
  </div>
</div>

// Floating Action Button for quick actions
<div className="fixed bottom-20 right-4 md:hidden">
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button size="lg" className="rounded-full h-14 w-14">
        <Plus className="h-6 w-6" />
      </Button>
    </DropdownMenuTrigger>
    <DropdownMenuContent align="end" className="w-48">
      <DropdownMenuItem>
        <UserPlus className="h-4 w-4 mr-2" />
        Add Contact
      </DropdownMenuItem>
      <DropdownMenuItem>
        <Building className="h-4 w-4 mr-2" />
        Add Company
      </DropdownMenuItem>
      <DropdownMenuItem>
        <TrendingUp className="h-4 w-4 mr-2" />
        Add Deal
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</div>
```

#### 2. Mobile-First Data Display
```typescript
// Responsive card layout for mobile
<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
  {contacts.map((contact) => (
    <Card key={contact.id} className="cursor-pointer hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={contact.avatar} />
              <AvatarFallback>{contact.initials}</AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="font-medium truncate">{contact.name}</h3>
              <p className="text-sm text-muted-foreground truncate">
                {contact.title}
              </p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem>Call</DropdownMenuItem>
              <DropdownMenuItem>Email</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Building className="h-4 w-4 text-muted-foreground" />
            <span className="truncate">{contact.company}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-4 w-4 text-muted-foreground" />
            <span className="truncate">{contact.email}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Phone className="h-4 w-4 text-muted-foreground" />
            <span>{contact.phone}</span>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <Badge variant={getStatusVariant(contact.status)}>
            {contact.status}
          </Badge>
          <span className="text-xs text-muted-foreground">
            {formatDate(contact.lastContact)}
          </span>
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

#### 3. Touch-Optimized Interactions
```typescript
// Swipe actions for list items
<div className="space-y-2">
  {items.map((item) => (
    <SwipeableListItem
      key={item.id}
      leftActions={[
        {
          icon: <Phone className="h-4 w-4" />,
          label: "Call",
          action: () => handleCall(item),
          color: "green"
        }
      ]}
      rightActions={[
        {
          icon: <Edit className="h-4 w-4" />,
          label: "Edit",
          action: () => handleEdit(item),
          color: "blue"
        },
        {
          icon: <Trash className="h-4 w-4" />,
          label: "Delete",
          action: () => handleDelete(item),
          color: "red"
        }
      ]}
    >
      <div className="flex items-center gap-3 p-4">
        <Avatar className="h-10 w-10">
          <AvatarFallback>{item.initials}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <h3 className="font-medium truncate">{item.name}</h3>
          <p className="text-sm text-muted-foreground truncate">
            {item.subtitle}
          </p>
        </div>
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
      </div>
    </SwipeableListItem>
  ))}
</div>
```

### Advanced Search & Filter Components

#### 1. Global Search with Command Palette
```typescript
// Command palette for global search and actions
<CommandDialog open={open} onOpenChange={setOpen}>
  <CommandInput placeholder="Search contacts, companies, deals..." />
  <CommandList>
    <CommandEmpty>No results found.</CommandEmpty>

    <CommandGroup heading="Quick Actions">
      <CommandItem onSelect={() => handleQuickAction('add-contact')}>
        <UserPlus className="mr-2 h-4 w-4" />
        Add Contact
      </CommandItem>
      <CommandItem onSelect={() => handleQuickAction('add-deal')}>
        <TrendingUp className="mr-2 h-4 w-4" />
        Add Deal
      </CommandItem>
    </CommandGroup>

    <CommandGroup heading="Contacts">
      {searchResults.contacts.map((contact) => (
        <CommandItem
          key={contact.id}
          onSelect={() => navigateToContact(contact.id)}
        >
          <User className="mr-2 h-4 w-4" />
          {contact.name} - {contact.company}
        </CommandItem>
      ))}
    </CommandGroup>

    <CommandGroup heading="Companies">
      {searchResults.companies.map((company) => (
        <CommandItem
          key={company.id}
          onSelect={() => navigateToCompany(company.id)}
        >
          <Building className="mr-2 h-4 w-4" />
          {company.name}
        </CommandItem>
      ))}
    </CommandGroup>
  </CommandList>
</CommandDialog>
```

#### 2. Advanced Filter Builder
```typescript
// Dynamic filter builder with multiple conditions
<Card>
  <CardHeader>
    <CardTitle className="text-base">Advanced Filters</CardTitle>
  </CardHeader>
  <CardContent className="space-y-4">
    {filters.map((filter, index) => (
      <div key={index} className="flex items-center gap-2">
        {index > 0 && (
          <Select
            value={filter.operator}
            onValueChange={(value) => updateFilterOperator(index, value)}
          >
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="and">AND</SelectItem>
              <SelectItem value="or">OR</SelectItem>
            </SelectContent>
          </Select>
        )}

        <Select
          value={filter.field}
          onValueChange={(value) => updateFilterField(index, value)}
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Field" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="company">Company</SelectItem>
            <SelectItem value="status">Status</SelectItem>
          </SelectContent>
        </Select>

        <Select
          value={filter.condition}
          onValueChange={(value) => updateFilterCondition(index, value)}
        >
          <SelectTrigger className="w-32">
            <SelectValue placeholder="Condition" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="equals">Equals</SelectItem>
            <SelectItem value="contains">Contains</SelectItem>
            <SelectItem value="starts_with">Starts with</SelectItem>
            <SelectItem value="not_equals">Not equals</SelectItem>
          </SelectContent>
        </Select>

        <Input
          placeholder="Value"
          value={filter.value}
          onChange={(e) => updateFilterValue(index, e.target.value)}
          className="flex-1"
        />

        <Button
          variant="ghost"
          size="sm"
          onClick={() => removeFilter(index)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    ))}

    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={addFilter}
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Filter
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={clearFilters}
      >
        Clear All
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={saveFilterSet}
      >
        <Save className="h-4 w-4 mr-2" />
        Save Filter Set
      </Button>
    </div>
  </CardContent>
</Card>
```

### Data Visualization Components

#### 1. Interactive Charts with Recharts
```typescript
// Revenue trend chart with interactive tooltips
<Card>
  <CardHeader>
    <CardTitle>Revenue Trend</CardTitle>
    <CardDescription>Monthly revenue over the last 12 months</CardDescription>
  </CardHeader>
  <CardContent>
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={revenueData}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip
          content={({ active, payload, label }) => {
            if (active && payload && payload.length) {
              return (
                <div className="rounded-lg border bg-background p-2 shadow-sm">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">
                        Month
                      </span>
                      <span className="font-bold text-muted-foreground">
                        {label}
                      </span>
                    </div>
                    <div className="flex flex-col">
                      <span className="text-[0.70rem] uppercase text-muted-foreground">
                        Revenue
                      </span>
                      <span className="font-bold">
                        ${payload[0].value?.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              )
            }
            return null
          }}
        />
        <Line
          type="monotone"
          dataKey="revenue"
          stroke="hsl(var(--primary))"
          strokeWidth={2}
          dot={{ fill: "hsl(var(--primary))" }}
        />
      </LineChart>
    </ResponsiveContainer>
  </CardContent>
</Card>
```

#### 2. Pipeline Funnel Visualization
```typescript
// Sales funnel with conversion rates
<Card>
  <CardHeader>
    <CardTitle>Sales Funnel</CardTitle>
    <CardDescription>Conversion rates through pipeline stages</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      {funnelData.map((stage, index) => (
        <div key={stage.name} className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">{stage.name}</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {stage.count} deals
              </span>
              <span className="text-sm font-medium">
                {stage.conversionRate}%
              </span>
            </div>
          </div>
          <div className="relative">
            <div className="h-8 bg-muted rounded-md overflow-hidden">
              <div
                className="h-full bg-primary transition-all duration-500"
                style={{ width: `${stage.percentage}%` }}
              />
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-sm font-medium text-primary-foreground">
                ${stage.value.toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

#### 3. Activity Heatmap
```typescript
// Activity heatmap showing user engagement
<Card>
  <CardHeader>
    <CardTitle>Activity Heatmap</CardTitle>
    <CardDescription>User activity over the last 30 days</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="grid grid-cols-7 gap-1">
      {Array.from({ length: 30 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (29 - i));
        const activity = getActivityForDate(date);

        return (
          <Tooltip key={i}>
            <TooltipTrigger asChild>
              <div
                className={cn(
                  "aspect-square rounded-sm cursor-pointer transition-colors",
                  getActivityColor(activity.level)
                )}
              />
            </TooltipTrigger>
            <TooltipContent>
              <p>{formatDate(date)}</p>
              <p>{activity.count} activities</p>
            </TooltipContent>
          </Tooltip>
        );
      })}
    </div>
    <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
      <span>Less</span>
      <div className="flex items-center gap-1">
        <div className="w-3 h-3 rounded-sm bg-muted" />
        <div className="w-3 h-3 rounded-sm bg-primary/20" />
        <div className="w-3 h-3 rounded-sm bg-primary/40" />
        <div className="w-3 h-3 rounded-sm bg-primary/60" />
        <div className="w-3 h-3 rounded-sm bg-primary" />
      </div>
      <span>More</span>
    </div>
  </CardContent>
</Card>
```

### Form Components & Workflows

#### 1. Multi-Step Form Wizard
```typescript
// Contact creation wizard with validation
<Card className="w-full max-w-2xl mx-auto">
  <CardHeader>
    <CardTitle>Add New Contact</CardTitle>
    <CardDescription>
      Step {currentStep} of {totalSteps}
    </CardDescription>
    <Progress value={(currentStep / totalSteps) * 100} className="w-full" />
  </CardHeader>

  <CardContent>
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {currentStep === 1 && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company</FormLabel>
                  <FormControl>
                    <Combobox
                      options={companies}
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Select or create company..."
                      searchPlaceholder="Search companies..."
                      emptyText="No companies found"
                      createText="Create new company"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Software Engineer" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <div className="flex items-center justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={previousStep}
            disabled={currentStep === 1}
          >
            Previous
          </Button>

          {currentStep === totalSteps ? (
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Contact
            </Button>
          ) : (
            <Button type="button" onClick={nextStep}>
              Next
            </Button>
          )}
        </div>
      </form>
    </Form>
  </CardContent>
</Card>
```

#### 2. Inline Editing Components
```typescript
// Inline editable fields with auto-save
<div className="space-y-4">
  <div className="grid grid-cols-2 gap-4">
    <InlineEditField
      label="First Name"
      value={contact.firstName}
      onSave={(value) => updateContact({ firstName: value })}
      validation={(value) => value.length > 0 ? null : "First name is required"}
    />

    <InlineEditField
      label="Last Name"
      value={contact.lastName}
      onSave={(value) => updateContact({ lastName: value })}
      validation={(value) => value.length > 0 ? null : "Last name is required"}
    />
  </div>

  <InlineEditField
    label="Email"
    value={contact.email}
    type="email"
    onSave={(value) => updateContact({ email: value })}
    validation={(value) => {
      if (!value) return "Email is required";
      if (!/\S+@\S+\.\S+/.test(value)) return "Invalid email format";
      return null;
    }}
  />

  <InlineSelectField
    label="Status"
    value={contact.status}
    options={statusOptions}
    onSave={(value) => updateContact({ status: value })}
  />
</div>
```

### Performance & Accessibility Features

#### 1. Virtual Scrolling for Large Datasets
```typescript
// Virtual table for handling thousands of records
<VirtualizedTable
  data={contacts}
  columns={columns}
  height={600}
  rowHeight={60}
  overscan={5}
  onRowClick={handleRowClick}
  loading={isLoading}
  loadMore={loadMoreContacts}
  hasNextPage={hasNextPage}
/>
```

#### 2. Keyboard Navigation Support
```typescript
// Enhanced keyboard navigation for accessibility
<div
  role="grid"
  tabIndex={0}
  onKeyDown={handleKeyDown}
  className="focus:outline-none focus:ring-2 focus:ring-primary"
>
  {items.map((item, index) => (
    <div
      key={item.id}
      role="gridcell"
      tabIndex={focusedIndex === index ? 0 : -1}
      className={cn(
        "p-4 border-b cursor-pointer transition-colors",
        focusedIndex === index && "bg-muted",
        "focus:outline-none focus:bg-accent"
      )}
      onClick={() => handleItemClick(item)}
      onKeyDown={(e) => handleItemKeyDown(e, item)}
    >
      <ItemContent item={item} />
    </div>
  ))}
</div>
```

This comprehensive design system provides a modern, accessible, and performant foundation for the OneCRM frontend revamp using shadcn/ui components.
