import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TenantGuard } from '../tenant/guards/tenant.guard';
import { CustomerPortalService, CreateChangeRequestDto } from './customer-portal.service';
import { BillingPortalService } from './billing-portal.service';
import { PortalSessionType } from './entities/portal-session.entity';
import { ChangeRequestType } from './entities/account-change-request.entity';

class CreatePortalSessionDto {
  type: PortalSessionType;
  returnUrl?: string;
  expirationMinutes?: number;
}

class CreateChangeRequestBodyDto {
  type: ChangeRequestType;
  title: string;
  description: string;
  requestData: Record<string, any>;
  targetImplementationDate?: Date;
  businessJustification?: string;
}

class UpdateBillingAddressDto {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

@ApiTags('Customer Portal')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('portal')
export class CustomerPortalController {
  constructor(
    private readonly customerPortalService: CustomerPortalService,
    private readonly billingPortalService: BillingPortalService,
  ) {}

  @Get('dashboard')
  @ApiOperation({ summary: 'Get customer portal dashboard' })
  @ApiResponse({ status: 200, description: 'Dashboard data retrieved successfully' })
  async getDashboard(@Request() req) {
    return this.customerPortalService.getCustomerPortalDashboard(req.user.organizationId);
  }

  @Post('session')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a portal session' })
  @ApiResponse({ status: 201, description: 'Portal session created successfully' })
  async createPortalSession(@Request() req, @Body() dto: CreatePortalSessionDto) {
    return this.customerPortalService.createPortalSession({
      organizationId: req.user.organizationId,
      userId: req.user.id,
      userEmail: req.user.email,
      type: dto.type,
      returnUrl: dto.returnUrl,
      expirationMinutes: dto.expirationMinutes,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
    });
  }

  @Get('session/:sessionToken/validate')
  @ApiOperation({ summary: 'Validate a portal session' })
  @ApiResponse({ status: 200, description: 'Session is valid' })
  @ApiResponse({ status: 403, description: 'Session is invalid or expired' })
  async validateSession(@Param('sessionToken') sessionToken: string) {
    return this.customerPortalService.validatePortalSession(sessionToken);
  }

  @Delete('session/:sessionToken')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Revoke a portal session' })
  @ApiResponse({ status: 204, description: 'Session revoked successfully' })
  async revokeSession(@Param('sessionToken') sessionToken: string) {
    await this.customerPortalService.revokePortalSession(sessionToken);
  }

  @Get('billing')
  @ApiOperation({ summary: 'Get billing information' })
  @ApiResponse({ status: 200, description: 'Billing information retrieved successfully' })
  async getBillingInfo(@Request() req) {
    return this.billingPortalService.getBillingInfo(req.user.organizationId);
  }

  @Get('billing/invoices')
  @ApiOperation({ summary: 'Get billing invoices' })
  @ApiResponse({ status: 200, description: 'Invoices retrieved successfully' })
  async getInvoices(@Request() req, @Query('limit') limit = 10) {
    return this.billingPortalService.getInvoices(req.user.organizationId, limit);
  }

  @Get('billing/payment-methods')
  @ApiOperation({ summary: 'Get payment methods' })
  @ApiResponse({ status: 200, description: 'Payment methods retrieved successfully' })
  async getPaymentMethods(@Request() req) {
    return this.billingPortalService.getPaymentMethods(req.user.organizationId);
  }

  @Post('billing/payment-methods')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Add a payment method' })
  @ApiResponse({ status: 201, description: 'Payment method added successfully' })
  async addPaymentMethod(@Request() req, @Body() body: { paymentMethodId: string }) {
    return this.billingPortalService.addPaymentMethod(
      req.user.organizationId,
      body.paymentMethodId,
    );
  }

  @Put('billing/payment-methods/:paymentMethodId/default')
  @ApiOperation({ summary: 'Set default payment method' })
  @ApiResponse({ status: 200, description: 'Default payment method updated successfully' })
  async setDefaultPaymentMethod(
    @Request() req,
    @Param('paymentMethodId') paymentMethodId: string,
  ) {
    return this.billingPortalService.setDefaultPaymentMethod(
      req.user.organizationId,
      paymentMethodId,
    );
  }

  @Delete('billing/payment-methods/:paymentMethodId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remove a payment method' })
  @ApiResponse({ status: 204, description: 'Payment method removed successfully' })
  async removePaymentMethod(
    @Request() req,
    @Param('paymentMethodId') paymentMethodId: string,
  ) {
    await this.billingPortalService.removePaymentMethod(
      req.user.organizationId,
      paymentMethodId,
    );
  }

  @Put('billing/address')
  @ApiOperation({ summary: 'Update billing address' })
  @ApiResponse({ status: 200, description: 'Billing address updated successfully' })
  async updateBillingAddress(@Request() req, @Body() dto: UpdateBillingAddressDto) {
    return this.billingPortalService.updateBillingAddress(req.user.organizationId, dto);
  }

  @Post('billing/portal-session')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create Stripe billing portal session' })
  @ApiResponse({ status: 201, description: 'Billing portal session created successfully' })
  async createBillingPortalSession(@Request() req, @Body() body: { returnUrl: string }) {
    return this.billingPortalService.createBillingPortalSession(
      req.user.organizationId,
      body.returnUrl,
    );
  }

  @Get('subscription')
  @ApiOperation({ summary: 'Get subscription information' })
  @ApiResponse({ status: 200, description: 'Subscription information retrieved successfully' })
  async getSubscription(@Request() req) {
    return this.billingPortalService.getSubscriptionInfo(req.user.organizationId);
  }

  @Get('subscription/plans')
  @ApiOperation({ summary: 'Get available subscription plans' })
  @ApiResponse({ status: 200, description: 'Available plans retrieved successfully' })
  async getAvailablePlans() {
    return this.billingPortalService.getAvailablePlans();
  }

  @Post('subscription/change-plan')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Request subscription plan change' })
  @ApiResponse({ status: 201, description: 'Plan change request created successfully' })
  async requestPlanChange(@Request() req, @Body() body: { planId: string; reason?: string }) {
    const changeRequestDto: CreateChangeRequestDto = {
      organizationId: req.user.organizationId,
      requestedByUserId: req.user.id,
      requestedByEmail: req.user.email,
      type: ChangeRequestType.PLAN_UPGRADE,
      title: 'Subscription Plan Change Request',
      description: `Request to change subscription plan to ${body.planId}`,
      requestData: {
        newPlanId: body.planId,
        reason: body.reason,
      },
    };

    return this.customerPortalService.createChangeRequest(changeRequestDto);
  }

  @Post('subscription/cancel')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Request subscription cancellation' })
  @ApiResponse({ status: 201, description: 'Cancellation request created successfully' })
  async requestCancellation(@Request() req, @Body() body: { reason: string; feedback?: string }) {
    const changeRequestDto: CreateChangeRequestDto = {
      organizationId: req.user.organizationId,
      requestedByUserId: req.user.id,
      requestedByEmail: req.user.email,
      type: ChangeRequestType.ACCOUNT_CANCELLATION,
      title: 'Account Cancellation Request',
      description: `Request to cancel subscription: ${body.reason}`,
      requestData: {
        cancellationReason: body.reason,
        feedback: body.feedback,
      },
    };

    return this.customerPortalService.createChangeRequest(changeRequestDto);
  }

  @Get('change-requests')
  @ApiOperation({ summary: 'Get change requests for organization' })
  @ApiResponse({ status: 200, description: 'Change requests retrieved successfully' })
  async getChangeRequests(@Request() req) {
    return this.customerPortalService.getChangeRequests(req.user.organizationId);
  }

  @Post('change-requests')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a change request' })
  @ApiResponse({ status: 201, description: 'Change request created successfully' })
  async createChangeRequest(@Request() req, @Body() dto: CreateChangeRequestBodyDto) {
    const changeRequestDto: CreateChangeRequestDto = {
      organizationId: req.user.organizationId,
      requestedByUserId: req.user.id,
      requestedByEmail: req.user.email,
      type: dto.type,
      title: dto.title,
      description: dto.description,
      requestData: dto.requestData,
      targetImplementationDate: dto.targetImplementationDate,
      businessJustification: dto.businessJustification,
    };

    return this.customerPortalService.createChangeRequest(changeRequestDto);
  }

  @Delete('change-requests/:requestId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Cancel a change request' })
  @ApiResponse({ status: 204, description: 'Change request cancelled successfully' })
  async cancelChangeRequest(@Request() req, @Param('requestId') requestId: string) {
    await this.customerPortalService.cancelChangeRequest(requestId, req.user.organizationId);
  }

  @Get('usage')
  @ApiOperation({ summary: 'Get usage statistics' })
  @ApiResponse({ status: 200, description: 'Usage statistics retrieved successfully' })
  async getUsageStats(@Request() req, @Query('period') period?: string) {
    return this.billingPortalService.getUsageStats(req.user.organizationId, period);
  }

  @Get('features')
  @ApiOperation({ summary: 'Get feature information' })
  @ApiResponse({ status: 200, description: 'Feature information retrieved successfully' })
  async getFeatures(@Request() req) {
    return this.billingPortalService.getFeatureInfo(req.user.organizationId);
  }

  @Post('features/:featureKey/trial')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Start feature trial' })
  @ApiResponse({ status: 201, description: 'Feature trial started successfully' })
  async startFeatureTrial(@Request() req, @Param('featureKey') featureKey: string) {
    return this.billingPortalService.startFeatureTrial(
      req.user.organizationId,
      featureKey,
      req.user.id,
    );
  }
}
