#!/bin/bash

# OneCRM Development Startup Script
# This script starts all services in the correct order for development

set -e

echo "🚀 Starting OneCRM Development Environment..."

# Configuration
SERVICES_TO_START="${SERVICES_TO_START:-all}"
SKIP_TESTS="${SKIP_TESTS:-false}"
WAIT_FOR_SERVICES="${WAIT_FOR_SERVICES:-true}"

# Function to check if a service is running
check_service() {
    local service_name=$1
    local url=$2
    local max_attempts=${3:-30}
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "$url" > /dev/null 2>&1; then
            echo "✅ $service_name is ready"
            return 0
        fi
        
        echo "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 5
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service_name failed to start within expected time"
    return 1
}

# Function to start infrastructure services
start_infrastructure() {
    echo "🐳 Starting infrastructure services..."
    
    # Start PostgreSQL, Redis, Keycloak, and Kong
    docker-compose -f docker-compose.dev.yml up -d postgres redis keycloak kong
    
    if [ "$WAIT_FOR_SERVICES" = "true" ]; then
        # Wait for services to be ready
        check_service "PostgreSQL" "http://localhost:5432" 10 || echo "⚠️  PostgreSQL may not be ready"
        check_service "Redis" "http://localhost:6379" 10 || echo "⚠️  Redis may not be ready"
        check_service "Keycloak" "http://localhost:8080/realms/master" 60
        check_service "Kong Gateway" "http://localhost:8001/status" 30
    fi
    
    echo "✅ Infrastructure services started"
}

# Function to setup Keycloak realm
setup_keycloak() {
    echo "🔐 Setting up Keycloak realm..."
    
    if [ -f "scripts/setup-keycloak.sh" ]; then
        ./scripts/setup-keycloak.sh
    else
        echo "⚠️  Keycloak setup script not found, skipping..."
    fi
}

# Function to configure Kong Gateway
setup_kong() {
    echo "🦍 Setting up Kong Gateway..."
    
    if [ -f "scripts/setup-kong.sh" ]; then
        ./scripts/setup-kong.sh
    else
        echo "⚠️  Kong setup script not found, skipping..."
    fi
}

# Function to start backend services
start_backend() {
    echo "🔧 Starting backend services..."
    
    # Install backend dependencies if needed
    if [ ! -d "backend/crm-api/node_modules" ]; then
        echo "📦 Installing backend dependencies..."
        cd backend/crm-api && npm install && cd ../..
    fi
    
    # Start the main CRM API
    echo "🚀 Starting CRM API..."
    cd backend/crm-api
    npm run dev &
    BACKEND_PID=$!
    cd ../..
    
    if [ "$WAIT_FOR_SERVICES" = "true" ]; then
        check_service "CRM API" "http://localhost:3001/api/health" 30
    fi
    
    echo "✅ Backend services started (PID: $BACKEND_PID)"
}

# Function to start frontend
start_frontend() {
    echo "🌐 Starting frontend..."
    
    # Install frontend dependencies if needed
    if [ ! -d "frontend/node_modules" ]; then
        echo "📦 Installing frontend dependencies..."
        cd frontend && npm install && cd ..
    fi
    
    # Start the Next.js frontend
    echo "🚀 Starting Next.js frontend..."
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    if [ "$WAIT_FOR_SERVICES" = "true" ]; then
        check_service "Frontend" "http://localhost:3000" 30
    fi
    
    echo "✅ Frontend started (PID: $FRONTEND_PID)"
}

# Function to run tests
run_tests() {
    if [ "$SKIP_TESTS" = "true" ]; then
        echo "⏭️  Skipping tests..."
        return 0
    fi
    
    echo "🧪 Running integration tests..."
    
    # Test SSO integration
    if [ -f "scripts/test-sso-integration.sh" ]; then
        ./scripts/test-sso-integration.sh
    fi
    
    # Test API Gateway
    if [ -f "scripts/test-api-gateway.sh" ]; then
        ./scripts/test-api-gateway.sh
    fi
    
    echo "✅ Tests completed"
}

# Function to display service information
display_service_info() {
    echo ""
    echo "🎉 OneCRM Development Environment Started!"
    echo ""
    echo "📋 Service URLs:"
    echo "- Frontend: http://localhost:3000"
    echo "- CRM API: http://localhost:3001"
    echo "- API Gateway: http://localhost:8000"
    echo "- Kong Admin: http://localhost:8001"
    echo "- Kong Manager: http://localhost:8002"
    echo "- Keycloak: http://localhost:8080"
    echo "- API Documentation: http://localhost:3001/api/docs"
    echo ""
    echo "🔑 Default Credentials:"
    echo "- Keycloak Admin: admin/admin"
    echo "- Test User: <EMAIL>/test123"
    echo "- Admin User: <EMAIL>/admin123"
    echo ""
    echo "🛠️  Development Commands:"
    echo "- Stop all: docker-compose -f docker-compose.dev.yml down"
    echo "- View logs: docker-compose -f docker-compose.dev.yml logs -f"
    echo "- Restart Kong: ./scripts/setup-kong.sh"
    echo "- Test SSO: ./scripts/test-sso-integration.sh"
    echo "- Test API: ./scripts/test-api-gateway.sh"
    echo ""
    echo "📊 Process IDs:"
    if [ -n "${BACKEND_PID:-}" ]; then
        echo "- Backend: $BACKEND_PID"
    fi
    if [ -n "${FRONTEND_PID:-}" ]; then
        echo "- Frontend: $FRONTEND_PID"
    fi
    echo ""
}

# Function to handle cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    
    # Kill background processes
    if [ -n "${BACKEND_PID:-}" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        echo "✅ Backend stopped"
    fi
    
    if [ -n "${FRONTEND_PID:-}" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo "✅ Frontend stopped"
    fi
    
    # Stop Docker services
    docker-compose -f docker-compose.dev.yml down
    echo "✅ Infrastructure services stopped"
    
    echo "👋 OneCRM development environment stopped"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Function to show help
show_help() {
    echo "OneCRM Development Startup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --services=SERVICE    Start specific services (all|infra|backend|frontend)"
    echo "  --skip-tests         Skip integration tests"
    echo "  --no-wait           Don't wait for services to be ready"
    echo "  --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # Start all services"
    echo "  $0 --services=infra          # Start only infrastructure"
    echo "  $0 --skip-tests              # Start all but skip tests"
    echo "  $0 --services=backend --no-wait  # Start backend without waiting"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --services=*)
            SERVICES_TO_START="${1#*=}"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        --no-wait)
            WAIT_FOR_SERVICES="false"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo "🔧 Configuration:"
    echo "- Services: $SERVICES_TO_START"
    echo "- Skip tests: $SKIP_TESTS"
    echo "- Wait for services: $WAIT_FOR_SERVICES"
    echo ""
    
    case $SERVICES_TO_START in
        "all")
            start_infrastructure
            setup_keycloak
            setup_kong
            start_backend
            start_frontend
            run_tests
            ;;
        "infra")
            start_infrastructure
            setup_keycloak
            setup_kong
            ;;
        "backend")
            start_backend
            ;;
        "frontend")
            start_frontend
            ;;
        *)
            echo "❌ Unknown service configuration: $SERVICES_TO_START"
            show_help
            exit 1
            ;;
    esac
    
    display_service_info
    
    # Keep the script running
    if [ "$SERVICES_TO_START" = "all" ] || [ "$SERVICES_TO_START" = "backend" ] || [ "$SERVICES_TO_START" = "frontend" ]; then
        echo "🔄 Services are running. Press Ctrl+C to stop..."
        wait
    fi
}

# Run main function
main "$@"
