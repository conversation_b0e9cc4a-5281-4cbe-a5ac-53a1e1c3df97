# OneCRM Secrets Template
# Copy this file to secrets-production.yaml and secrets-staging.yaml
# Replace all placeholder values with actual secrets

apiVersion: v1
kind: Secret
metadata:
  name: onecrm-secrets
  namespace: onecrm
  labels:
    app: onecrm
    environment: production
type: Opaque
stringData:
  # Database Configuration
  database-url: "***************************************************************************/onecrm"
  postgres-user: "onecrm_user"
  postgres-password: "REPLACE_WITH_STRONG_PASSWORD"
  postgres-db: "onecrm"
  
  # Redis Configuration
  redis-url: "redis://:REPLACE_WITH_REDIS_PASSWORD@redis-service:6379"
  redis-password: "REPLACE_WITH_REDIS_PASSWORD"
  
  # JWT Configuration
  jwt-secret: "REPLACE_WITH_JWT_SECRET_256_BITS"
  jwt-refresh-secret: "REPLACE_WITH_JWT_REFRESH_SECRET_256_BITS"
  
  # Keycloak Configuration
  keycloak-admin: "admin"
  keycloak-admin-password: "REPLACE_WITH_KEYCLOAK_ADMIN_PASSWORD"
  keycloak-secret: "REPLACE_WITH_KEYCLOAK_CLIENT_SECRET"
  keycloak-db: "keycloak"
  
  # Email Configuration (SMTP)
  smtp-host: "smtp.gmail.com"
  smtp-port: "587"
  smtp-user: "REPLACE_WITH_SMTP_USER"
  smtp-password: "REPLACE_WITH_SMTP_PASSWORD"
  smtp-from: "<EMAIL>"
  
  # File Storage (AWS S3 or compatible)
  s3-access-key: "REPLACE_WITH_S3_ACCESS_KEY"
  s3-secret-key: "REPLACE_WITH_S3_SECRET_KEY"
  s3-bucket: "onecrm-files"
  s3-region: "us-east-1"
  s3-endpoint: "https://s3.amazonaws.com"
  
  # Monitoring & Observability
  grafana-admin-user: "admin"
  grafana-admin-password: "REPLACE_WITH_GRAFANA_PASSWORD"
  
  # External API Keys
  google-maps-api-key: "REPLACE_WITH_GOOGLE_MAPS_API_KEY"
  sendgrid-api-key: "REPLACE_WITH_SENDGRID_API_KEY"
  stripe-secret-key: "REPLACE_WITH_STRIPE_SECRET_KEY"
  stripe-webhook-secret: "REPLACE_WITH_STRIPE_WEBHOOK_SECRET"
  
  # Encryption Keys
  encryption-key: "REPLACE_WITH_ENCRYPTION_KEY_256_BITS"
  
  # SSL/TLS Certificates (if not using cert-manager)
  tls-cert: |
    -----BEGIN CERTIFICATE-----
    REPLACE_WITH_TLS_CERTIFICATE
    -----END CERTIFICATE-----
  tls-key: |
    -----BEGIN PRIVATE KEY-----
    REPLACE_WITH_TLS_PRIVATE_KEY
    -----END PRIVATE KEY-----

---
# Additional secrets for staging environment
apiVersion: v1
kind: Secret
metadata:
  name: onecrm-secrets
  namespace: onecrm-staging
  labels:
    app: onecrm
    environment: staging
type: Opaque
stringData:
  # Database Configuration
  database-url: "*******************************************************************************/onecrm_staging"
  postgres-user: "onecrm_staging"
  postgres-password: "REPLACE_WITH_STAGING_PASSWORD"
  postgres-db: "onecrm_staging"
  
  # Redis Configuration
  redis-url: "redis://:REPLACE_WITH_STAGING_REDIS_PASSWORD@redis-service:6379"
  redis-password: "REPLACE_WITH_STAGING_REDIS_PASSWORD"
  
  # JWT Configuration
  jwt-secret: "REPLACE_WITH_STAGING_JWT_SECRET"
  jwt-refresh-secret: "REPLACE_WITH_STAGING_JWT_REFRESH_SECRET"
  
  # Keycloak Configuration
  keycloak-admin: "admin"
  keycloak-admin-password: "REPLACE_WITH_STAGING_KEYCLOAK_PASSWORD"
  keycloak-secret: "REPLACE_WITH_STAGING_KEYCLOAK_SECRET"
  keycloak-db: "keycloak_staging"
  
  # Email Configuration (Test SMTP)
  smtp-host: "smtp.mailtrap.io"
  smtp-port: "587"
  smtp-user: "REPLACE_WITH_MAILTRAP_USER"
  smtp-password: "REPLACE_WITH_MAILTRAP_PASSWORD"
  smtp-from: "<EMAIL>"
  
  # File Storage (Staging bucket)
  s3-access-key: "REPLACE_WITH_STAGING_S3_ACCESS_KEY"
  s3-secret-key: "REPLACE_WITH_STAGING_S3_SECRET_KEY"
  s3-bucket: "onecrm-files-staging"
  s3-region: "us-east-1"
  s3-endpoint: "https://s3.amazonaws.com"
  
  # Monitoring & Observability
  grafana-admin-user: "admin"
  grafana-admin-password: "REPLACE_WITH_STAGING_GRAFANA_PASSWORD"
  
  # External API Keys (Test keys)
  google-maps-api-key: "REPLACE_WITH_TEST_GOOGLE_MAPS_API_KEY"
  sendgrid-api-key: "REPLACE_WITH_TEST_SENDGRID_API_KEY"
  stripe-secret-key: "REPLACE_WITH_TEST_STRIPE_SECRET_KEY"
  stripe-webhook-secret: "REPLACE_WITH_TEST_STRIPE_WEBHOOK_SECRET"
  
  # Encryption Keys
  encryption-key: "REPLACE_WITH_STAGING_ENCRYPTION_KEY"
