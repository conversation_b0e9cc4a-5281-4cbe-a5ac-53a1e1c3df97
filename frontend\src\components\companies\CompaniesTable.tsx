'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Avatar,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Skeleton,
  Checkbox,
  Tooltip,
  Link,
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Language as WebsiteIcon,
  Business as BusinessIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AccountTree as HierarchyIcon,
} from '@mui/icons-material';
import useSWR from 'swr';

interface Company {
  id: string;
  name: string;
  domain?: string;
  industry?: string;
  size?: string;
  website?: string;
  annualRevenue?: number;
  employeeCount?: number;
  tags?: string[];
  assignedTo?: {
    firstName: string;
    lastName: string;
  };
  parentCompany?: {
    name: string;
  };
  subsidiaries?: any[];
  createdAt: string;
}

interface CompaniesTableProps {
  searchQuery: string;
  filters: any;
  onEditCompany: (company: Company) => void;
}

const CompanyRow: React.FC<{
  company: Company;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: (company: Company) => void;
}> = ({ company, isSelected, onSelect, onEdit }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleMenuClose();
    onEdit(company);
  };

  const handleDelete = () => {
    handleMenuClose();
    console.log('Delete company:', company.id);
  };

  const handleViewHierarchy = () => {
    handleMenuClose();
    console.log('View hierarchy for:', company.id);
  };

  const getInitials = () => {
    return company.name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2);
  };

  const formatCurrency = (amount?: number) => {
    if (!amount) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num?: number) => {
    if (!num) return 'N/A';
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getSizeColor = (size?: string) => {
    switch (size?.toLowerCase()) {
      case 'enterprise':
        return 'error';
      case 'large':
        return 'warning';
      case 'medium':
        return 'info';
      case 'small':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <TableRow hover selected={isSelected}>
      <TableCell padding="checkbox">
        <Checkbox
          checked={isSelected}
          onChange={() => onSelect(company.id)}
        />
      </TableCell>
      
      <TableCell>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar sx={{ bgcolor: 'primary.main' }}>
            {getInitials()}
          </Avatar>
          <Box>
            <Typography variant="subtitle2" fontWeight={600}>
              {company.name}
            </Typography>
            {company.domain && (
              <Typography variant="caption" color="text.secondary">
                {company.domain}
              </Typography>
            )}
          </Box>
        </Box>
      </TableCell>
      
      <TableCell>
        <Box display="flex" alignItems="center" gap={1}>
          {company.website && (
            <Tooltip title={`Visit ${company.website}`}>
              <IconButton 
                size="small" 
                component={Link}
                href={company.website}
                target="_blank"
                rel="noopener noreferrer"
              >
                <WebsiteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          <Box>
            {company.industry && (
              <Typography variant="body2">{company.industry}</Typography>
            )}
            {company.website && (
              <Typography variant="caption" color="text.secondary">
                {company.website}
              </Typography>
            )}
          </Box>
        </Box>
      </TableCell>
      
      <TableCell>
        {company.size && (
          <Chip
            label={company.size}
            size="small"
            color={getSizeColor(company.size) as any}
            variant="outlined"
          />
        )}
      </TableCell>
      
      <TableCell>
        <Typography variant="body2">
          {formatCurrency(company.annualRevenue)}
        </Typography>
      </TableCell>
      
      <TableCell>
        <Typography variant="body2">
          {formatNumber(company.employeeCount)}
        </Typography>
      </TableCell>
      
      <TableCell>
        <Box display="flex" gap={0.5} flexWrap="wrap">
          {company.tags?.slice(0, 2).map((tag) => (
            <Chip
              key={tag}
              label={tag}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          ))}
          {company.tags && company.tags.length > 2 && (
            <Chip
              label={`+${company.tags.length - 2}`}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem', height: 20 }}
            />
          )}
        </Box>
      </TableCell>
      
      <TableCell>
        <Box>
          {company.parentCompany && (
            <Typography variant="caption" color="text.secondary">
              Parent: {company.parentCompany.name}
            </Typography>
          )}
          {company.subsidiaries && company.subsidiaries.length > 0 && (
            <Typography variant="caption" color="text.secondary" display="block">
              {company.subsidiaries.length} subsidiaries
            </Typography>
          )}
        </Box>
      </TableCell>
      
      <TableCell>
        {company.assignedTo && (
          <Tooltip title={`${company.assignedTo.firstName} ${company.assignedTo.lastName}`}>
            <Avatar sx={{ width: 32, height: 32, fontSize: '0.8rem' }}>
              {company.assignedTo.firstName[0]}{company.assignedTo.lastName[0]}
            </Avatar>
          </Tooltip>
        )}
      </TableCell>
      
      <TableCell>
        <IconButton onClick={handleMenuOpen}>
          <MoreVertIcon />
        </IconButton>
        
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={handleEdit}>
            <EditIcon sx={{ mr: 1 }} fontSize="small" />
            Edit
          </MenuItem>
          <MenuItem onClick={handleViewHierarchy}>
            <HierarchyIcon sx={{ mr: 1 }} fontSize="small" />
            View Hierarchy
          </MenuItem>
          <MenuItem onClick={handleDelete}>
            <DeleteIcon sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        </Menu>
      </TableCell>
    </TableRow>
  );
};

export const CompaniesTable: React.FC<CompaniesTableProps> = ({
  searchQuery,
  filters,
  onEditCompany,
}) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);

  // Build query parameters
  const queryParams = new URLSearchParams({
    page: (page + 1).toString(),
    limit: rowsPerPage.toString(),
    ...(searchQuery && { search: searchQuery }),
    ...(filters.industry && { industry: filters.industry }),
    ...(filters.size && { size: filters.size }),
    ...(filters.assignedToId && { assignedToId: filters.assignedToId }),
    ...(filters.tags.length > 0 && { tags: filters.tags.join(',') }),
    ...(filters.minRevenue && { minRevenue: filters.minRevenue }),
    ...(filters.maxRevenue && { maxRevenue: filters.maxRevenue }),
    ...(filters.minEmployees && { minEmployees: filters.minEmployees }),
    ...(filters.maxEmployees && { maxEmployees: filters.maxEmployees }),
  });

  const { data, isLoading } = useSWR(`/api/companies?${queryParams}`);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = data?.companies?.map((company: Company) => company.id) || [];
      setSelectedCompanies(newSelected);
    } else {
      setSelectedCompanies([]);
    }
  };

  const handleSelectCompany = (id: string) => {
    const selectedIndex = selectedCompanies.indexOf(id);
    let newSelected: string[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedCompanies, id);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedCompanies.slice(1));
    } else if (selectedIndex === selectedCompanies.length - 1) {
      newSelected = newSelected.concat(selectedCompanies.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selectedCompanies.slice(0, selectedIndex),
        selectedCompanies.slice(selectedIndex + 1),
      );
    }

    setSelectedCompanies(newSelected);
  };

  const isSelected = (id: string) => selectedCompanies.indexOf(id) !== -1;

  if (isLoading) {
    return (
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                {Array.from({ length: 10 }).map((_, index) => (
                  <TableCell key={index}>
                    <Skeleton variant="text" />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {Array.from({ length: 10 }).map((_, index) => (
                <TableRow key={index}>
                  {Array.from({ length: 10 }).map((_, cellIndex) => (
                    <TableCell key={cellIndex}>
                      <Skeleton variant="text" />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    );
  }

  return (
    <Paper>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedCompanies.length > 0 && selectedCompanies.length < (data?.companies?.length || 0)}
                  checked={data?.companies?.length > 0 && selectedCompanies.length === data?.companies?.length}
                  onChange={handleSelectAll}
                />
              </TableCell>
              <TableCell>Company</TableCell>
              <TableCell>Industry & Website</TableCell>
              <TableCell>Size</TableCell>
              <TableCell>Revenue</TableCell>
              <TableCell>Employees</TableCell>
              <TableCell>Tags</TableCell>
              <TableCell>Hierarchy</TableCell>
              <TableCell>Assigned To</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data?.companies?.map((company: Company) => (
              <CompanyRow
                key={company.id}
                company={company}
                isSelected={isSelected(company.id)}
                onSelect={handleSelectCompany}
                onEdit={onEditCompany}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      <TablePagination
        rowsPerPageOptions={[10, 25, 50, 100]}
        component="div"
        count={data?.total || 0}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};
