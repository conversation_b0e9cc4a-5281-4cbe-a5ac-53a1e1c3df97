# OneCRM Authentication Flow Documentation

## Overview
OneCRM uses Keycloak for authentication with OpenID Connect (OIDC) protocol. This document covers the complete authentication flow and configuration.

## Authentication Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend (Next.js)
    participant K as Keycloak
    participant B as Backend API
    participant DB as Database

    Note over U,DB: Initial Page Load
    U->>F: Access OneCRM App
    F->>F: Check localStorage for token
    alt No valid token
        F->>U: Redirect to login page
        U->>F: Click "Sign In"
        F->>K: Redirect to Keycloak login
        K->>U: Show login form
        U->>K: Enter credentials
        K->>K: Validate credentials
        K->>F: Redirect with auth code
        F->>K: Exchange code for tokens
        K->>F: Return access_token, refresh_token, id_token
        F->>F: Store tokens in localStorage
        F->>B: API call with Bearer token
        B->>K: Validate token (introspect)
        K->>B: Token validation response
        B->>DB: Query user data
        DB->>B: Return user data
        B->>F: Return user profile
        F->>U: Show dashboard
    else Valid token exists
        F->>B: API call with Bearer token
        B->>K: Validate token
        alt Token valid
            K->>B: Token valid response
            B->>F: Return data
            F->>U: Show dashboard
        else Token expired
            K->>B: Token invalid response
            B->>F: 401 Unauthorized
            F->>K: Refresh token request
            K->>F: New access token
            F->>B: Retry API call
            B->>F: Return data
            F->>U: Show dashboard
        end
    end

    Note over U,DB: Logout Flow
    U->>F: Click logout
    F->>K: Logout request
    K->>K: Invalidate session
    K->>F: Logout confirmation
    F->>F: Clear localStorage
    F->>U: Redirect to login page
```

## Token Management Flow

```mermaid
flowchart TD
    A[App Start] --> B{Token in localStorage?}
    B -->|No| C[Redirect to Keycloak Login]
    B -->|Yes| D{Token Valid?}
    D -->|Yes| E[Use Token for API Calls]
    D -->|No| F{Refresh Token Valid?}
    F -->|Yes| G[Refresh Access Token]
    F -->|No| C
    G --> H{Refresh Successful?}
    H -->|Yes| E
    H -->|No| C
    C --> I[User Login]
    I --> J[Keycloak Auth]
    J --> K[Receive Tokens]
    K --> L[Store in localStorage]
    L --> E
    E --> M{API Response}
    M -->|200 OK| N[Continue]
    M -->|401 Unauthorized| F
    N --> O[User Interaction]
    O --> E
```

## Keycloak Integration Architecture

```mermaid
graph TB
    subgraph "Frontend (Next.js)"
        A[Login Page] --> B[Auth Service]
        B --> C[Token Storage]
        C --> D[API Client]
        D --> E[Protected Routes]
    end

    subgraph "Keycloak Server"
        F[Authentication] --> G[Token Endpoint]
        G --> H[User Management]
        H --> I[Role Management]
        I --> J[Token Introspection]
    end

    subgraph "Backend API"
        K[Auth Middleware] --> L[Token Validation]
        L --> M[User Context]
        M --> N[RBAC Enforcement]
        N --> O[Database Access]
    end

    B --> F
    G --> C
    D --> K
    J --> L

    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style K fill:#e8f5e8
```

## Implementation Components

### 1. Frontend Authentication Service
```typescript
// Location: src/lib/auth.ts
interface AuthTokens {
  access_token: string;
  refresh_token: string;
  id_token: string;
  expires_in: number;
}

class AuthService {
  login(): Promise<void>
  logout(): Promise<void>
  refreshToken(): Promise<string>
  getAccessToken(): string | null
  isAuthenticated(): boolean
}
```

### 2. API Interceptor
```typescript
// Location: src/lib/api.ts
// Automatically adds Bearer token to requests
// Handles 401 responses with token refresh
// Redirects to login on refresh failure
```

### 3. Protected Route Component
```typescript
// Location: src/components/auth/ProtectedRoute.tsx
// Wraps pages requiring authentication
// Handles loading states during auth check
// Redirects unauthenticated users
```

### 4. Auth Context Provider
```typescript
// Location: src/components/providers/AuthProvider.tsx
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => Promise<void>;
  logout: () => void;
}
```

## Security Considerations

### Token Storage
- **Development**: localStorage (for simplicity)
- **Production**: httpOnly cookies (recommended)
- **Alternative**: Secure memory storage with refresh on page reload

### Token Validation
- Backend validates every API request
- Token introspection with Keycloak
- Automatic token refresh before expiration
- Secure logout with token revocation

### CORS Configuration
```javascript
// Required CORS headers for Keycloak integration
{
  "Access-Control-Allow-Origin": "http://localhost:3000",
  "Access-Control-Allow-Credentials": "true",
  "Access-Control-Allow-Headers": "Authorization, Content-Type"
}
```

## Environment Variables

### Frontend (.env.local)
```bash
NEXT_PUBLIC_KEYCLOAK_URL=http://localhost:8080
NEXT_PUBLIC_KEYCLOAK_REALM=onecrm
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=onecrm-frontend
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002
```

### Backend (.env)
```bash
KEYCLOAK_URL=http://localhost:8080
KEYCLOAK_REALM=onecrm
KEYCLOAK_CLIENT_ID=onecrm-backend
KEYCLOAK_CLIENT_SECRET=your-client-secret
```

## Error Handling

### Common Scenarios
1. **Network Errors**: Retry with exponential backoff
2. **Token Expired**: Automatic refresh attempt
3. **Refresh Failed**: Redirect to login
4. **Invalid Credentials**: Show error message
5. **Server Errors**: Show user-friendly message

### Error Codes
- `401`: Unauthorized - Token invalid/expired
- `403`: Forbidden - Insufficient permissions
- `500`: Server Error - Backend/Keycloak issues

## Testing Strategy

### Unit Tests
- Token validation logic
- API interceptor behavior
- Auth context state management

### Integration Tests
- Complete login flow
- Token refresh scenarios
- Logout functionality

### E2E Tests
- User login journey
- Protected route access
- Session timeout handling
